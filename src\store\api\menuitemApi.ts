import { baseApi } from './baseApi';

// Product Interface
export interface Product {
  Product_pic?: string;
  ProductId?: string;
  id: string;
  name: string;
  barCode: string;
  categoryId: string | any[];
  categoryParents?: string | any[];
  price: number | string;
  retailPrice: string | number;
  discountPrice?: string | number;
  totalQuantity: number;
  active: boolean;
  hasPicture: boolean;
  productType: string;
  pictureUrl?: string;
  unit?: string | any;
  ingredient: Array<{
    ingredientId: {
      _id: string;
      name: string;
      IngredientName: string;
    };
    quantity: number | string;
  }>;
  userId: string;
}

// Inject Endpoints for Products API
export const productsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // ✅ Fetch All Products
    getProducts: builder.query<Product[], string>({
      query: (userId) => `/Product?userId=${userId}`,
      providesTags: ['Product'],
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          ProductId: item.ProductId,
          name: item.name,
          barCode: item.barCode,
          categoryId: item.categoryId,
          categoryParents: item.categoryParents ?? undefined,
          price: Number(item.price),
          retailPrice: Number(item.retailPrice),
          discountPrice: Number(item.discountPrice ?? 0),
          totalQuantity: Number(item.totalQuantity ?? 0),
          active: item.active === 'true' || item.active === true,
          hasPicture: item.hasPicture,
          productType: item.productType,
          pictureUrl: item.Product_pic || '',
          Product_pic: item.Product_pic || '',
          unit: item.unit,
          ingredient: item.ingredient || [], // Ensure ingredient data is mapped correctly
          userId: item.userId,
        }));
      },
    }),

    // ✅ Create a New Product
    postProduct: builder.mutation<any, FormData>({
      query: (formData) => {
        const userId = localStorage.getItem('userId');
        if (!userId) {
          throw new Error('User ID not found');
        }

        formData.append('userId', userId);

        return {
          url: '/Product',
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: ['Product'],
    }),

    updateProduct: builder.mutation({
      query: ({ id, formData }: { id: string; formData: FormData }) => ({
        url: `/Product/${id}`,
        method: 'PUT',
        body: formData, // Must stay FormData
      }),
      invalidatesTags: ['Product'],
    }),


    // delete
    deleteProduct: builder.mutation<{ success: boolean; message?: string }, string>({
      query: (id) => ({
        url: `/Product/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Product'],
    }),
  }),
  overrideExisting: false,
});

// Export Hooks
export const {
  useGetProductsQuery,
  usePostProductMutation,
  useDeleteProductMutation,
  useUpdateProductMutation
} = productsApi;