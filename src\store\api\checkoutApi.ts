import { baseApi } from "./baseApi";

// Interfaces for different response and request types
export interface CheckoutCharge {
  id: string;
  amount: number;
  currency: string;
  status: string;
  description: string;
  created: number;
  customer?: string;
  metadata?: any;
}

export interface Customer {
  id: string;
  email: string;
  name?: string;
  created: number;
  metadata?: any;
}

export interface Subscription {
  id: string;
  status: string;
  current_period_start: number;
  current_period_end: number;
  plan: any;
  items: any;
  customer?: any;
  latest_invoice?: any;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
  client_secret: string;
  created: number;
}

export interface SellerAccount {
  id: string;
  object: string;
  created: number;
  business_type: string;
  charges_enabled: boolean;
  payouts_enabled: boolean;
  details_submitted: boolean;
}

export interface Product {
  marketing_features: any;
  features: any;
  id: string;
  name: string;
  active: boolean;
  description?: string;
  price?: number;
  metadata?: any;
}

export interface InvoiceRequest {
  email: string;
  amountDue: number;
  stripeAccount: string;
  appFee?: number;
}

export interface SubscriptionUpdateRequest {
  customerEmail: string;
  productId: string;
}

export const checkoutApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Payment intents endpoints
    createPaymentIntent: builder.mutation<PaymentIntent, any>({
      query: (data) => ({
        url: "/paymentIntent",
        method: "POST",
        body: data,
      }),
    }),
    
    capturePaymentIntent: builder.mutation<any, { payment_intent_id: string }>({
      query: (data) => ({
        url: "/capture_payment",
        method: "POST",
        body: data,
      }),
    }),
    
    confirmPaymentIntent: builder.mutation<any, { payment_intent_id: string }>({
      query: (data) => ({
        url: "/confirm_payment",
        method: "POST",
        body: data,
      }),
    }),
    
    // Checkout and charge endpoints
    processCheckout: builder.mutation<any, any>({
      query: (stripeToken) => ({
        url: "/credit",
        method: "POST",
        body: { stripeToken },
      }),
    }),
    
    processSurcharge: builder.mutation<any, any>({
      query: (stripeToken) => ({
        url: "/surCharge",
        method: "POST",
        body: { stripeToken },
      }),
    }),
    
    // Charges and transactions
    getCharges: builder.query<CheckoutCharge[], void>({
      query: () => "/charges",
      transformResponse: (response: any) => {
        return response.data?.map((charge: any) => ({
          id: charge.id,
          amount: charge.amount,
          currency: charge.currency,
          status: charge.status,
          description: charge.description,
          created: charge.created,
          customer: charge.customer?.id,
          metadata: charge.metadata,
        })) || [];
      },
      providesTags: ["Charges"],
    }),
    
    getChargesByAccount: builder.query<{ paymentIntents: any[], totalApplicationFeeAmount: number }, string>({
      query: (accountId) => `/charges/${accountId}`,
      providesTags: ["Charges"],
    }),
    
    getAllAmounts: builder.query<{ charges: any[], subscriptions: any[], totalInvoiceCount: number }, void>({
      query: () => "/allamount",
      providesTags: ["Charges", "Subscriptions"],
    }),
    
    // Customers endpoints
    getCustomers: builder.query<Customer[], void>({
      query: () => "/stripe-customers",
      transformResponse: (response: any[]) => {
        return response.map((customer) => ({
          id: customer.id,
          email: customer.email,
          name: customer.name,
          created: customer.created,
          metadata: customer.metadata,
        }));
      },
      providesTags: ["Customers"],
    }),
    
    // Subscriptions endpoints
    getSubscriptions: builder.query<{ subscriptions: Subscription[], totalInvoiceCount: number }, void>({
      query: () => "/subscriptions",
      providesTags: ["Subscriptions"],
    }),
    
    getCustomerSubscriptions: builder.query<{ customer: any, subscriptions: any }, string>({
      query: (email) => `/subscriptions/${email}`,
      providesTags: (_result, _error, email) => [{ type: "Subscriptions", id: email }],
    }),
    
    cancelSubscription: builder.mutation<any, string>({
      query: (subscriptionId) => ({
        url: `/cancel-subscription/${subscriptionId}`,
        method: "POST",
      }),
      invalidatesTags: ["Subscriptions"],
    }),
    
    updateSubscription: builder.mutation<any, SubscriptionUpdateRequest>({
      query: (data) => ({
        url: "/update-plan",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Subscriptions"],
    }),
    
    createAppSubscription: builder.mutation<any, any>({
      query: (stripeToken) => ({
        url: "/app-plan",
        method: "POST",
        body: { stripeToken },
      }),
      invalidatesTags: ["Subscriptions"],
    }),
    
    createHardwareSubscription: builder.mutation<any, any>({
      query: (stripeToken) => ({
        url: "/hardware-plan",
        method: "POST",
        body: { stripeToken },
      }),
      invalidatesTags: ["Subscriptions"],
    }),
    
    // Products endpoints
    getAppProducts: builder.query<Product[], void>({
      query: () => "/stripe-products",
      transformResponse: (response: any[]) => {
        return response.map((product) => ({
          marketing_features: product.marketing_features,
          features: product.features,
          id: product.id,
          name: product.name,
          active: product.active,
          description: product.description,
          price: product.price,
          metadata: product.metadata,
        }));
      },
      providesTags: ["Products"],
    }),
    
    // Invoice endpoints
    createInvoice: builder.mutation<any, InvoiceRequest>({
      query: (data) => ({
        url: "/create-invoice",
        method: "POST",
        body: data,
      }),
    }),
    
    // Seller/Connected account endpoints
    createSellerAccount: builder.mutation<any, any>({
      query: (data) => ({
        url: "/create-account",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["SellerAccounts"],
    }),
    
    authorizeSellerAccount: builder.mutation<any, { userId: string, data: any }>({
      query: ({ userId, data }) => ({
        url: `/authorize-seller/${userId}`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["SellerAccounts"],
    }),
    
    getSellerBalance: builder.query<any, string>({
      query: (accountId) => `/sellerbalance/${accountId}`,
      providesTags: (_result, _error, accountId) => [{ type: "SellerAccounts", id: accountId }],
    }),
    
    deleteConnectedAccount: builder.mutation<any, string>({
      query: (accountId) => ({
        url: `/delete-Account/${accountId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["SellerAccounts"],
    }),
    
    getCompletedAccounts: builder.query<SellerAccount[], void>({
      query: () => "/sellerAccounts",
      transformResponse: (response: any[]) => {
        return response.map((account) => ({
          id: account.id,
          object: account.object,
          created: account.created,
          business_type: account.business_type,
          charges_enabled: account.charges_enabled,
          payouts_enabled: account.payouts_enabled,
          details_submitted: account.details_submitted,
        }));
      },
      providesTags: ["SellerAccounts"],
    }),
    
    // Terminal endpoints
    connectTerminal: builder.mutation<any, any>({
      query: (data) => ({
        url: "/connect-terminal",
        method: "POST",
        body: data,
      }),
    }),
    
    createOrderPaymentIntent: builder.mutation<any, any>({
      query: (data) => ({
        url: "/order-payment",
        method: "POST",
        body: data,
      }),
    }),
  }),
  overrideExisting: false,
});

export const {
  // Payment intents hooks
  useCreatePaymentIntentMutation,
  useCapturePaymentIntentMutation,
  useConfirmPaymentIntentMutation,
  
  // Checkout hooks
  useProcessCheckoutMutation,
  useProcessSurchargeMutation,
  
  // Charges hooks
  useGetChargesQuery,
  useGetChargesByAccountQuery,
  useGetAllAmountsQuery,
  
  // Customer hooks
  useGetCustomersQuery,
  
  // Subscription hooks
  useGetSubscriptionsQuery,
  useGetCustomerSubscriptionsQuery,
  useCancelSubscriptionMutation,
  useUpdateSubscriptionMutation,
  useCreateAppSubscriptionMutation,
  useCreateHardwareSubscriptionMutation,
  
  // Product hooks
  useGetAppProductsQuery,
  
  // Invoice hooks
  useCreateInvoiceMutation,
  
  // Seller account hooks
  useCreateSellerAccountMutation,
  useAuthorizeSellerAccountMutation,
  useGetSellerBalanceQuery,
  useDeleteConnectedAccountMutation,
  useGetCompletedAccountsQuery,
  
  // Terminal hooks
  useConnectTerminalMutation,
  useCreateOrderPaymentIntentMutation,
} = checkoutApi;