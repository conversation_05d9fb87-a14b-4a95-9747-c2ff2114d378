import React, { useState } from 'react';
import { ChevronLeft, ChevronDown } from 'lucide-react';

interface Recipient {
  id: string;
  name: string;
  selected: boolean;
}

export default function AddEmailCampaign() {
  const [emailSubject, setEmailSubject] = useState('');
  const [customRecipients, setCustomRecipients] = useState('');
  const [emailContent, setEmailContent] = useState('');
  const [showRecipientDropdown, setShowRecipientDropdown] = useState(false);
  
  const [recipients, setRecipients] = useState<Recipient[]>([
    { id: '1', name: '<PERSON><PERSON>', selected: false },
    { id: '2', name: '<PERSON>', selected: false },
    { id: '3', name: '<PERSON>', selected: false },
    { id: '4', name: 'Aliquip Et', selected: false }
  ]);

  const handleToggleRecipient = (id: string) => {
    setRecipients(recipients.map(recipient => 
      recipient.id === id ? { ...recipient, selected: !recipient.selected } : recipient
    ));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const selectedRecipients = recipients.filter(r => r.selected).map(r => r.name);
    
    console.log({
      emailSubject,
      selectedRecipients,
      customRecipients,
      emailContent
    });
    
    // Add API call here
  };

  const handleCancel = () => {
    console.log('Form cancelled');
    // Add navigation logic here
  };

  return (
    <div className="bg-white min-h-screen p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-6 text-gray-800">
          <ChevronLeft className="w-5 h-5 mr-2" />
          <h1 className="text-xl font-medium">Add Email Campaign</h1>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Email Campaign Details Section */}
          <div className="mb-6">
            <div className="bg-orange-50 py-3 px-4 text-gray-800 font-medium rounded-t-lg border-b border-orange-100">
              Email Campaign Details
            </div>
            <div className="bg-white p-6 rounded-b-lg border border-gray-200 border-t-0 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Email Subject */}
                <div>
                  <label className="block text-sm mb-2">
                    Email Subject <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    placeholder="Enter Email Subject"
                    value={emailSubject}
                    onChange={(e) => setEmailSubject(e.target.value)}
                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                    required
                  />
                </div>

                {/* Email Recipients */}
                <div>
                  <label className="block text-sm mb-2">
                    Email Recipients <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <button
                      type="button"
                      className="w-full bg-white border border-gray-300 rounded-md p-2 flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-orange-500"
                      onClick={() => setShowRecipientDropdown(!showRecipientDropdown)}
                    >
                      <span className="text-gray-500">Select Email Recipients</span>
                      <ChevronDown className="w-4 h-4 text-gray-500" />
                    </button>
                    
                    {showRecipientDropdown && (
                      <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg py-1 max-h-60 overflow-y-auto">
                        {recipients.map((recipient) => (
                          <div key={recipient.id} className="px-3 py-2 hover:bg-gray-50">
                            <label className="flex items-center space-x-3 cursor-pointer">
                              <input
                                type="checkbox"
                                className="w-4 h-4 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
                                checked={recipient.selected}
                                onChange={() => handleToggleRecipient(recipient.id)}
                              />
                              <span>{recipient.name}</span>
                            </label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Custom Email Recipients */}
              <div>
                <label className="block text-sm mb-2">
                  Custom Email Recipients <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  placeholder="Enter custom email recipients and press Enter to add multiple"
                  value={customRecipients}
                  onChange={(e) => setCustomRecipients(e.target.value)}
                  className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                  required
                />
              </div>

              {/* Compose Email */}
              <div>
                <label className="block text-sm mb-2">
                  Compose Email <span className="text-red-500">*</span>
                </label>
                <textarea
                  placeholder="Compose Email"
                  value={emailContent}
                  onChange={(e) => setEmailContent(e.target.value)}
                  className="w-full border border-gray-300 rounded-md p-2 h-40 focus:outline-none focus:ring-2 focus:ring-orange-500"
                  required
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600"
            >
              Add Email Campaign
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}