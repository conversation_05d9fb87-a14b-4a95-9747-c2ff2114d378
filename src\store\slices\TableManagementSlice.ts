import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { TableManagement } from '../api/TableManagementApi';
import { tableManagementApi } from '../api/TableManagementApi';

interface TableManagementState {
  tables: TableManagement[];
  isLoading: boolean;
  error: string | null;
}

const initialState: TableManagementState = {
  tables: [],
  isLoading: false,
  error: null,
};

const tableManagementSlice = createSlice({
  name: 'tableManagement',
  initialState,
  reducers: {
    setTables: (state, action: PayloadAction<TableManagement[]>) => {
      state.tables = action.payload;
    },
    clearTables: (state) => {
      state.tables = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET Tables List
      .addMatcher(
        tableManagementApi.endpoints.getTables.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.getTables.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.tables = payload;
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.getTables.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch tables';
        }
      )

      // DELETE Table
      .addMatcher(
        tableManagementApi.endpoints.deleteTable.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.deleteTable.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const tableId = action.meta.arg.originalArgs as string;
          state.tables = state.tables.filter(
            (table) => table._id !== tableId
          );
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.deleteTable.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete table';
        }
      )

      // POST Table
      .addMatcher(
        tableManagementApi.endpoints.postTable.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.postTable.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          // Only push if we have valid data
          if (payload && payload._id) {
            state.tables.push({
              _id: payload._id,
              tableNo: payload.tableNo,
              tableimg: payload.tableimg,
              tableName: payload.tableName,
              location: payload.location,
              description: payload.description,
              hasLampixDevice: payload.hasLampixDevice,
              userId: payload.userId,
              Status: payload.Status,
              height: payload.height,
              width: payload.width,
              x: payload.x,
              y: payload.y,
              capacity: payload.capacity,
              floor: payload.floor || '',  // Add floor property with a default empty string
            });
          }
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.postTable.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create table';
        }
      )

      // PUT Table
      .addMatcher(
        tableManagementApi.endpoints.putTable.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.putTable.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          
          // Handle the case where the payload contains the table data
          if (payload && payload._id) {
            const updated = {
              _id: payload._id,
              tableNo: payload.tableNo,
              tableimg: payload.tableimg,
              tableName: payload.tableName,
              location: payload.location,
              description: payload.description,
              hasLampixDevice: payload.hasLampixDevice,
              userId: payload.userId,
              Status: payload.Status,
              height: payload.height,
              width: payload.width,
              x: payload.x,
              y: payload.y,
              capacity: payload.capacity,
              floor: payload.floor || '',  // Add floor property with a default empty string
            };
            state.tables = state.tables.map((table) =>
              table._id === updated._id ? updated : table
            );
          } else if (payload && payload.data && payload.data._id) {
            // Handle the case where payload.data contains the table data
            const updated = {
              _id: payload.data._id,
              tableNo: payload.data.tableNo,
              tableimg: payload.data.tableimg,
              tableName: payload.data.tableName,
              location: payload.data.location,
              description: payload.data.description,
              hasLampixDevice: payload.data.hasLampixDevice,
              userId: payload.data.userId,
              Status: payload.data.Status,
              height: payload.data.height,
              width: payload.data.width,
              x: payload.data.x,
              y: payload.data.y,
              capacity: payload.data.capacity,
              floor: payload.data.floor || '',  // Add floor property with a default empty string
            };
            state.tables = state.tables.map((table) =>
              table._id === updated._id ? updated : table
            );
          } else {
            // If we get here, the payload structure is not as expected
            console.error('Invalid payload structure for table update:', payload);
          }
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.putTable.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update table';
        }
      )
      
      // Search Update Table
      .addMatcher(
        tableManagementApi.endpoints.searchUpdateTable.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.searchUpdateTable.matchFulfilled,
        (state) => {
          state.isLoading = false;
          // Since this will cause a refetch of the data via invalidateTags,
          // we don't need to manually update the state here
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.searchUpdateTable.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update table by search';
        }
      )
      
      // Update Table Positions
      .addMatcher(
        tableManagementApi.endpoints.updateTablePositions.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.updateTablePositions.matchFulfilled,
        (state, {  meta }) => {
          state.isLoading = false;
          
          // Update table positions in the state
          const { tablePositions } = meta.arg.originalArgs;
          
          if (tablePositions && tablePositions.length > 0) {
            state.tables = state.tables.map(table => {
              const updatedPosition = tablePositions.find(pos => pos.tableNo === table.tableNo);
              if (updatedPosition) {
                return {
                  ...table,
                  x: updatedPosition.x,
                  y: updatedPosition.y
                };
              }
              return table;
            });
          }
        }
      )
      .addMatcher(
        tableManagementApi.endpoints.updateTablePositions.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update table positions';
        }
      );
  },
});

export const { setTables, clearTables } = tableManagementSlice.actions;

export const selectTables = (state: RootState) => state.tableManagement.tables;
export const selectTableLoading = (state: RootState) => state.tableManagement.isLoading;
export const selectTableError = (state: RootState) => state.tableManagement.error;

export default tableManagementSlice.reducer;