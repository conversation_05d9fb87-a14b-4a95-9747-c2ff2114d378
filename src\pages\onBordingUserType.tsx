import React, { useState } from 'react';
import { UserIcon, BriefcaseBusiness, Store, Handshake } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import superAdmin from "../assets/OnBording/superAdmin.jpg";
import businessAdmin from "../assets/OnBording/businessAdmin.jpg";
import salesTeam from "../assets/OnBording/salesTeam.jpg";
import employee from "../assets/OnBording/employees.jpg";
import vendor from "../assets/OnBording/vendor.jpg";

interface UserTypeOption {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  imageSrc: string;
}

const UserTypeSelection: React.FC = () => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const navigate = useNavigate();
  
  const userTypes: UserTypeOption[] = [
    {
      id: 'super-admin',
      title: 'Super Admin',
      description: 'Super Admin is Someone who manages the business',
      icon: <UserIcon className="h-5 w-5" />,
      imageSrc: superAdmin
    },
    {
      id: 'business-admin',
      title: 'Business Admin',
      description: 'Business Admin is Someone who manages the business',
      icon: <BriefcaseBusiness className="h-5 w-5" />,
      imageSrc: businessAdmin
    },
    {
      id: 'sales-team',
      title: 'Sales Team',
      description: 'Sales team can do various tasks',
      icon: <Handshake className="h-5 w-5" />,
      imageSrc: salesTeam
    },
    {
      id: 'employee',
      title: 'Employee',
      description: 'Sales team can do various tasks',
      icon: <UserIcon className="h-5 w-5" />,
      imageSrc: employee
    },
    {
      id: 'vendor',
      title: 'Vendor',
      description: 'Sales team can do various tasks',
      icon: <Store className="h-5 w-5" />,
      imageSrc: vendor
    }
  ];
  
  const handleOptionClick = (id: string) => {
    setSelectedOption(id);
  };
  
  const handleNext = () => {
    if (!selectedOption) {
      toast.error("Please select a user role to continue", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true
      });
      return;
    }
    navigate('/auth', { state: { roleType: selectedOption } });
  };
  
  return (
    <div className="w-full min-h-screen bg-gray-50 px-4 sm:px-6 py-8 flex flex-col justify-center">
      <div className="max-w-6xl mx-auto w-full">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-center sm:text-left">
            Patron<span className="text-orange-500">Works</span>
          </h1>
        </div>

        <div className="mb-6 text-center sm:text-left">
          <h2 className="text-lg text-gray-700 font-medium">
            Select User Type to Login with your Credentials
          </h2>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-8">
          {userTypes.map((userType) => (
            <div 
              key={userType.id}
              className={`border rounded-lg overflow-hidden cursor-pointer transition-all duration-300 ${
                selectedOption === userType.id 
                  ? 'border-orange-500 bg-orange-50 shadow-md' 
                  : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
              }`}
              onClick={() => handleOptionClick(userType.id)}
            >
              <div className="p-4">
                <div className={`flex items-center gap-2 ${
                  selectedOption === userType.id ? 'text-orange-500' : 'text-gray-700'
                }`}>
                  <div className={`p-1 rounded-full ${
                    selectedOption === userType.id ? 'bg-orange-100' : 'text-orange-500'
                  }`}>
                    {userType.icon}
                  </div>
                  <span className="font-medium">{userType.title}</span>
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  {userType.description}
                </p>
              </div>
              <div className="h-24 sm:h-28 md:h-32 overflow-hidden">
                <img 
                  src={userType.imageSrc} 
                  alt={userType.title} 
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-center">
          <button 
            className="w-full sm:w-auto sm:px-36 px-6 py-2 bg-orange-500 text-white font-medium rounded hover:bg-orange-600 transition-all duration-300"
            onClick={handleNext}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserTypeSelection;
