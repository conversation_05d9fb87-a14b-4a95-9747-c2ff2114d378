import React, { useState } from 'react';
import { IoClose } from "react-icons/io5";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { format, addDays, subDays, startOfWeek } from 'date-fns';
import NewReservationModal from './NewReservationModal';

interface ReservationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onNewReservation: () => void;
}

interface Reservation {
  name: string;
  time: string;
  tableNo: string;
  people: number;
}

const ReservationModal: React.FC<ReservationModalProps> = ({ isOpen, onClose, onNewReservation }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isNewReservationOpen, setIsNewReservationOpen] = useState(false);

  // Add this function to handle opening new reservation
  // const handleOpenNewReservation = () => {
  //   setIsNewReservationOpen(true);
  //   onClose(); // Close the ReservationModal
  // };

  // Add this function to handle closing new reservation
  const handleCloseNewReservation = () => {
    setIsNewReservationOpen(false);
  };

  const reservations: Reservation[] = [
    { name: "Matilda R", time: "08:00 AM", tableNo: "T-11", people: 12 },
    { name: "Margaret", time: "08:00 AM", tableNo: "T-8", people: 2 },
    { name: "Joseph M", time: "08:00 AM", tableNo: "T-7", people: 2 },
    { name: "Maikhel D", time: "08:00 AM", tableNo: "T-7", people: 2 },
    { name: "Wesley", time: "08:00 AM", tableNo: "T-7", people: 2 },
    { name: "Francis Eli", time: "08:00 AM", tableNo: "T-7", people: 2 },
    { name: "John Sparrow", time: "08:00 AM", tableNo: "T-7", people: 2 },
  ];

  // Generate week days
  const generateWeekDays = (startDate: Date) => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      days.push(addDays(startDate, i));
    }
    return days;
  };

  const weekDays = generateWeekDays(startOfWeek(currentDate));

  const handlePreviousWeek = () => {
    setCurrentDate(subDays(currentDate, 7));
  };

  const handleNextWeek = () => {
    setCurrentDate(addDays(currentDate, 7));
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 z-50 flex justify-end">
        <div className="bg-black/50 absolute inset-0" onClick={onClose} />
        <div className="relative w-[500px] bg-white h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-[#E4E4E4]">
            <h2 className="text-xl font-semibold">Reservation</h2>
            <button onClick={onClose}>
              <IoClose size={24} />
            </button>
          </div>

          <div className="flex-1 overflow-y-auto">
            {/* Date Selection */}
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <button 
                    className="p-2 hover:bg-orange hover:text-white rounded-lg text-black"
                    onClick={handlePreviousWeek}
                  >
                    <IoIosArrowBack />
                  </button>
                  <span className="text-gray-600">Select date</span>
                </div>
                <span className='font-bold ml-25'>{format(currentDate, 'MMMM yyyy')}</span>
                <button 
                  className="p-2 hover:bg-orange hover:text-white rounded-lg text-black"
                  onClick={handleNextWeek}
                >
                  <IoIosArrowForward />
                </button>
              </div>

              {/* Calendar Days */}
              <div className="rounded-2xl border border-[#E4E4E4] overflow-hidden">
                <div className="grid grid-cols-7 text-center border-x-2 border-[#E4E4E4]">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                    <div key={day} className="text-gray-500 text-xs">
                      {day}
                    </div>
                  ))}
                  
                  {weekDays.map((date, index) => (
                    <div 
                      key={index}
                      onClick={() => handleDateSelect(date)}
                      className={`py-2 cursor-pointer ${
                        selectedDate && format(selectedDate, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
                          ? 'bg-orange-100 text-orange-500 font-bold'
                          : 'hover:bg-gray-50 font-bold'
                      }`}
                    >
                      {format(date, 'd')}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Reservation List */}
            <div>
              {/* Header */}
              <div className="grid grid-cols-4 px-4 py-3 text-xs text-gray-500 border-y border-[#E4E4E4]">
                <div>Name</div>
                <div>Time</div>
                <div>Table No</div>
                <div>People</div>
              </div>

              {/* List Items */}
              {reservations.map((reservation, index) => (
                <div 
                  key={index} 
                  className="grid grid-cols-4 px-4 py-4 text-sm border-b border-[#E4E4E4] hover:bg-gray-50"
                >
                  <div className="font-medium">{reservation.name}</div>
                  <div className="text-black">{reservation.time}</div>
                  <div className="text-black">{reservation.tableNo}</div>
                  <div className="flex items-center gap-1">
                    <svg 
                      className="w-4 h-4" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="2"
                    >
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                      <circle cx="12" cy="7" r="4" />
                    </svg>
                    {reservation.people}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 mt-auto">
            <button 
              className="w-full bg-[#FF5C00] text-white py-3 rounded-full font-medium hover:bg-orange-600"
              onClick={onNewReservation}
            >
              Add New Reservation
            </button>
          </div>
        </div>
      </div>

      {/* Add the New Reservation Modal */}
      <NewReservationModal 
        isOpen={isNewReservationOpen}
        onClose={handleCloseNewReservation}
        onContinue={() => {}}
      />
    </>
  );
};

export default ReservationModal;







