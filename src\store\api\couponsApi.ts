import { baseApi } from "./baseApi";

export interface Coupon {
  _id?: string;
  series: string;
  description: string;
  discount: number;
  start: number;
  end: number;
  startDate: string;
  endDate: string;
  userId: string;
  discountType: string;
  publish: boolean;
  message?: string;
}

// Response types
interface CouponResponse {
  message: string;
}

// Update coupon request type
interface UpdateCouponRequest {
  id: string;
  couponData: Partial<Coupon>;
}

export const couponApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get coupons by user
    getCouponsByUser: builder.query<Coupon[], string>({
      query: (userId: string) => `/coupens?userId=${userId}`,
      providesTags: ["Coupon"],
    }),
    
    // Add a new coupon
    addCoupon: builder.mutation<CouponResponse, Partial<Coupon>>({
      query: (couponData) => ({
        url: '/coupens',
        method: 'POST',
        body: couponData,
      }),
      invalidatesTags: ["Coupon"],
    }),
    
    // Update a coupon
    updateCoupon: builder.mutation<CouponResponse, UpdateCouponRequest>({
      query: ({id, couponData}) => ({
        url: `/coupens/${id}`,
        method: 'PUT',
        body: couponData,
      }),
      invalidatesTags: (_result, _error, {id}) => [
        { type: 'Coupon', id },
        { type: 'Coupon', id: 'LIST' },
      ],
    }),
    
    // Delete a coupon
    deleteCoupon: builder.mutation<CouponResponse, string>({
      query: (id) => ({
        url: `/coupens/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ["Coupon"],
    }),
  }),
});

export const {
  useGetCouponsByUserQuery,
  useAddCouponMutation,
  useUpdateCouponMutation,
  useDeleteCouponMutation,
} = couponApi;