import React, { useEffect, useState } from "react";
import { ChevronLeft, Image as ImageIcon, AlertCircle } from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import Swal from "sweetalert2";

import {
  usePostParentCategoryMutation,
  usePutParentCategoryMutation,
  useGetParentCategoriesQuery,
} from "../../../store/api/parentCategoryApi";

interface FormData {
  name: string;
  imageUrl?: string;
  imageFile?: FileList;
}

interface CompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  outputFormat?: string;
}
const compressImage = (
  file: File,
  options: CompressionOptions = {}
): Promise<File> => {
  return new Promise((resolve, reject) => {
    const {
      maxWidth = 800,
      maxHeight = 800,
      quality = 0.8,
      outputFormat = "image/jpeg",
    } = options;

    // Check if file is an image
    if (!file.type.startsWith("image/")) {
      reject(new Error("File is not an image"));
      return;
    }

    // For SVG files, just return as-is since they're vector-based
    if (file.type === "image/svg+xml") {
      resolve(file);
      return;
    }

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      reject(new Error("Canvas context not available"));
      return;
    }

    const img = new Image();

    // Set a timeout to prevent hanging
    const timeoutId = setTimeout(() => {
      reject(new Error("Image loading timeout"));
    }, 10000);

    img.onload = function () {
      clearTimeout(timeoutId);

      try {
        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = img;

        // Only resize if image is larger than max dimensions
        if (width > maxWidth || height > maxHeight) {
          if (width > height) {
            if (width > maxWidth) {
              height = (height * maxWidth) / width;
              width = maxWidth;
            }
          } else {
            if (height > maxHeight) {
              width = (width * maxHeight) / height;
              height = maxHeight;
            }
          }
        }

        canvas.width = Math.floor(width);
        canvas.height = Math.floor(height);

        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";

        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        let finalFormat = outputFormat;
        if (
          file.type === "image/png" &&
          !file.name.toLowerCase().includes("photo")
        ) {
          finalFormat = "image/png"; 
        }

        // Convert canvas to blob with error handling
        canvas.toBlob(
          (blob) => {
            if (blob && blob.size > 0) {
              // Create a new File object with the compressed data
              const compressedFile = new File([blob], file.name, {
                type: finalFormat,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              reject(
                new Error("Canvas to Blob conversion failed - empty blob")
              );
            }
          },
          finalFormat,
          quality
        );
      } catch (error) {
        reject(
          new Error(`Canvas processing failed: ${(error as Error).message}`)
        );
      }
    };

    img.onerror = function () {
      clearTimeout(timeoutId);
      reject(new Error("Failed to load image - image may be corrupted"));
    };

    // Create object URL with error handling
    try {
      const objectUrl = URL.createObjectURL(file);
      img.src = objectUrl;

      // Clean up object URL after image loads or fails
      const originalOnLoad = img.onload;
      img.onload = function () {
        URL.revokeObjectURL(objectUrl);
        if (originalOnLoad) {
          return originalOnLoad.apply(this, arguments as any);
        }
      };

      const originalOnError = img.onerror;
      img.onerror = function () {
        URL.revokeObjectURL(objectUrl);
        if (originalOnError) {
          return originalOnError.apply(this, arguments as any);
        }
      };
    } catch (error) {
      reject(new Error("Failed to create object URL"));
    }
  });
};

// Progressive compression with multiple attempts
const progressiveCompress = async (file: File): Promise<File> => {
  const strategies = [
    { maxWidth: 800, maxHeight: 800, quality: 0.8 },
    { maxWidth: 600, maxHeight: 600, quality: 0.7 },
    { maxWidth: 400, maxHeight: 400, quality: 0.6 },
  ];

  let lastError: Error | null = null;

  for (const strategy of strategies) {
    try {
      const compressed = await compressImage(file, strategy);
      return compressed;
    } catch (error) {
      lastError = error as Error;
      console.warn(`Compression strategy failed:`, strategy, error);
    }
  }

  throw lastError || new Error("All compression strategies failed");
};

// Utility to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const CreateParentCategory: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const userId = localStorage.getItem("userId") || "";

  const { data: categories = [] } = useGetParentCategoriesQuery(userId);
  console.log("all all categories", categories);
  const category = categories.find((cat) => cat.id === id);

  console.log("selected category", category);

  const [postParentCategory, { isLoading: isCreating }] =
    usePostParentCategoryMutation();
  const [putParentCategory, { isLoading: isUpdating }] =
    usePutParentCategoryMutation();

  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [compressedFile, setCompressedFile] = useState<File | null>(null);
  const [isCompressing, setIsCompressing] = useState(false);
  const [compressionError, setCompressionError] = useState<string>("");

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<FormData>();

  const imageFile = watch("imageFile");
useEffect(() => {
  if (isEditMode && category) {
    // Set the form values
    setValue("name", category.name);
    
    // Handle image preview for edit mode
    if (category.parent_pic) {
      setValue("imageUrl", category.parent_pic);
      setImagePreview(category.parent_pic); 
      setCompressionError("");
    }
  }
}, [isEditMode, category, setValue]);

 const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
  const file = e.target.files?.[0];
  if (!file) return;

  setCompressionError("");
  setIsCompressing(true);

  try {
    const originalSize = file.size;

    // Check if file is too large (e.g., > 50MB)
    const maxFileSize = 50 * 1024 * 1024; // 50MB
    if (originalSize > maxFileSize) {
      const errorMessage = `File size (${formatFileSize(
        originalSize
      )}) exceeds 50MB limit`;
      setCompressionError(errorMessage);
      setIsCompressing(false);

      Swal.fire({
        icon: "error",
        title: "File Too Large",
        text: errorMessage,
        confirmButtonColor: "#6366f1",
      });
      return;
    }

    // Validate file type
    const validTypes = [
      "image/jpeg",
      "image/jpg", 
      "image/png",
      "image/gif",
      "image/webp",
      "image/svg+xml",
    ];
    if (!validTypes.includes(file.type)) {
      const errorMessage =
        "Unsupported file type. Please use JPEG, PNG, GIF, WebP, or SVG.";
      setCompressionError(errorMessage);
      setIsCompressing(false);

      Swal.fire({
        icon: "error",
        title: "Invalid File Type",
        text: errorMessage,
        confirmButtonColor: "#6366f1",
      });
      return;
    }

    let processedFile: File;

    if (file.type === "image/svg+xml") {
      processedFile = file;
      Swal.fire({
        icon: "info",
        title: "SVG Uploaded",
        text: "SVG file uploaded successfully (no compression needed)",
        confirmButtonColor: "#6366f1",
      });
    } else {
      try {
        processedFile = await progressiveCompress(file);
      } catch (compressionError) {
        console.error("Progressive compression failed:", compressionError);

        if (originalSize <= 5 * 1024 * 1024) {
          processedFile = file;
        } else {
          const errorMessage = `File too large (${formatFileSize(
            originalSize
          )}) and compression failed. Please use a smaller image or try a different format.`;

          Swal.fire({
            icon: "error",
            title: "Compression Failed",
            text: errorMessage,
            confirmButtonColor: "#6366f1",
          });

          throw new Error(errorMessage);
        }
      }
    }

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreview(reader.result as string);
    };
    reader.readAsDataURL(processedFile);

    // Store processed file and clear URL field
    setCompressedFile(processedFile);
    setValue("imageUrl", ""); // Clear the URL when file is selected
    
  } catch (error) {
    console.error("Image processing failed:", error);
    const errorMessage =
      error instanceof Error
        ? error.message
        : "Failed to process image. Please try a different image or format.";
    setCompressionError(errorMessage);

    Swal.fire({
      icon: "error",
      title: "Image Processing Failed", 
      text: errorMessage,
      confirmButtonColor: "#6366f1",
    });

    setImagePreview(null);
    setCompressedFile(null);
  } finally {
    setIsCompressing(false);
  }
};

  const handleCancel = () => {
    navigate("/admin/catalog/parent-categories");
  };

  const onSubmit = async (data: FormData) => {
    const formData = new FormData();
    formData.append("name", data.name);

    // Use compressed file if available, otherwise use the original file or URL
    if (compressedFile) {
      formData.append("parent_pic", compressedFile);
    } else if (data.imageFile?.[0]) {
      formData.append("parent_pic", data.imageFile[0]);
    } else if (data.imageUrl) {
      formData.append("parent_pic", data.imageUrl);
    }

    try {
      if (isEditMode && id) {
        await putParentCategory({ id, formData }).unwrap();
        Swal.fire({
          icon: "success",
          title: "Success!",
          text: "Parent category updated successfully!",
          confirmButtonColor: "#6366f1",
        });
      } else {
        await postParentCategory(formData).unwrap();
        Swal.fire({
          icon: "success",
          title: "Success!",
          text: "Parent category created successfully!",
          confirmButtonColor: "#6366f1",
        });
      }
      reset();
      setImagePreview(null);
      setCompressedFile(null);
      navigate("/admin/catalog/parent-categories");
    } catch (error) {
      // Show SweetAlert2 error instead of toast
      const errorMessage = isEditMode
        ? "Failed to update category."
        : "Failed to create category.";
      Swal.fire({
        icon: "error",
        title: "Operation Failed",
        text: errorMessage,
        confirmButtonColor: "#6366f1",
      });
      console.error("Operation failed:", error);
    }
  };

  return (
    <div className="rounded-lg shadow bg-gray-50 p-4">
      <div className="p-4 mb-4 border border-gray-100 bg-white rounded-lg">
        <button
          onClick={handleCancel}
          className="flex items-center text-gray-800 font-bold text-lg"
        >
          <ChevronLeft size={20} className="mr-1" />
          <h1 className="text-xl">
            {isEditMode ? "Edit" : "Create"} Parent Category
          </h1>
        </button>
      </div>

      <form
        onSubmit={handleSubmit(onSubmit)}
        className="mb-4 bg-white border border-gray-200 rounded-2xl"
      >
        <div className="bg-orange-50 p-4 rounded-t-2xl">
          <h2 className="font-medium text-gray-800">Parent Category Name</h2>
        </div>
        <div className="p-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            placeholder="Enter Parent Category Name"
            className={`w-full p-2 border ${
              errors.name ? "border-red-500" : "border-gray-300"
            } rounded-lg focus:outline-none focus:ring-1 focus:ring-orange-500`}
            {...register("name", { required: "Category name is required" })}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>

        <div className="bg-orange-50 p-4">
          <h2 className="font-medium text-gray-800">Parent Category Image</h2>
        </div>
        <div className="p-4">
          <div className="flex items-start mb-6">
            <div className="mr-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center text-gray-400 overflow-hidden">
                {isCompressing ? (
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                ) : imagePreview ? (
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="w-full h-full object-cover rounded-full"
                  />
                ) : (
                  <ImageIcon size={24} className="text-orange-500" />
                )}
              </div>
            </div>

            <div className="flex-1">
              <label className="block text-sm font-medium text-blue-600 cursor-pointer">
                <span>Upload</span>
                <span className="text-gray-700"> Parent Category Image</span>
                <input
                  type="file"
                  className="hidden"
                  accept="image/*"
                  disabled={isCompressing}
                  {...register("imageFile", {
                    onChange: handleImageChange,
                  })}
                />
              </label>
              <p className="text-xs text-gray-500">
                SVG, PNG, JPG, GIF, WebP (max. 50MB, auto-compressed to
                800x800px)
              </p>

              {/* Compression Status */}
              {isCompressing && (
                <div className="mt-2 flex items-center text-sm text-blue-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  Compressing image...
                </div>
              )}

              {/* Compression Error */}
              {compressionError && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs flex items-start">
                  <AlertCircle
                    size={16}
                    className="text-red-500 mr-1 mt-0.5 flex-shrink-0"
                  />
                  <p className="text-red-700">{compressionError}</p>
                </div>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Or Enter Image URL
            </label>
            <input
              type="url"
              placeholder="Add Image URL"
              className={`w-full p-2 border ${
                errors.imageUrl ? "border-red-500" : "border-gray-300"
              } rounded-lg focus:outline-none focus:ring-1 focus:ring-orange-500`}
              disabled={!!imageFile?.[0] || isCompressing}
              {...register("imageUrl")}
            />
            {errors.imageUrl && (
              <p className="mt-1 text-sm text-red-600">
                {errors.imageUrl.message}
              </p>
            )}
          </div>
        </div>

        <div className="px-4 mt-4 flex justify-end space-x-2 mb-4">
          <button
            type="submit"
            className={`px-6 py-2 rounded text-white ${
              isCreating || isUpdating || isCompressing
                ? "bg-orange-300 cursor-not-allowed"
                : "bg-orange-500 hover:bg-orange-600"
            }`}
            disabled={isCreating || isUpdating || isCompressing}
          >
            {isCompressing
              ? "Compressing..."
              : isCreating || isUpdating
              ? isEditMode
                ? "Update Parent Category..."
                : "Create Parent Category..."
              : isEditMode
              ? "Update Parent Category"
              : "Add Parent Category"}
          </button>
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 border border-gray-300 rounded text-gray-700 bg-white hover:bg-gray-50"
            disabled={isCompressing}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateParentCategory;
