import { createSlice } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type {  Modifier } from '../api/modifier';
import { modifiersApi } from '../api/modifier';

interface ModifierState {
  modifiers: Modifier[];
  isLoading: boolean;
  error: string | null;
}

const initialState: ModifierState = {
  modifiers: [],
  isLoading: false,
  error: null,
};

const modifierSlice = createSlice({
  name: 'modifier', // Changed from 'modifiers' to 'modifier' to match store configuration
  initialState,
  reducers: {
    // Local reducers for manual operations if needed
    setModifiers: (state, action) => {
      state.modifiers = action.payload;
    },
    clearModifiers: (state) => {
      state.modifiers = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle getModifiersByUser query
      .addMatcher(
        modifiersApi.endpoints.getModifiersByUser.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        modifiersApi.endpoints.getModifiersByUser.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          state.modifiers = action.payload;
        }
      )
      .addMatcher(
        modifiersApi.endpoints.getModifiersByUser.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to fetch modifiers';
        }
      )
      
      // Handle addModifiers mutation
      .addMatcher(
        modifiersApi.endpoints.addModifiers.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        modifiersApi.endpoints.addModifiers.matchFulfilled,
        (state) => {
          state.isLoading = false;
          // RTK Query will automatically invalidate the cache and trigger a refetch
        }
      )
      .addMatcher(
        modifiersApi.endpoints.addModifiers.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to add modifier';
        }
      )
      
      // Handle updateModifier mutation
      .addMatcher(
        modifiersApi.endpoints.updateModifier.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        modifiersApi.endpoints.updateModifier.matchFulfilled,
        (state) => {
          state.isLoading = false;
          // RTK Query will automatically invalidate the cache and trigger a refetch
        }
      )
      .addMatcher(
        modifiersApi.endpoints.updateModifier.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to update modifier';
        }
      )
      
      // Handle deleteModifier mutation
      .addMatcher(
        modifiersApi.endpoints.deleteModifier.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        modifiersApi.endpoints.deleteModifier.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          // Filter out the deleted modifier
          const deletedId = action.meta.arg.originalArgs;
          state.modifiers = state.modifiers.filter(
            (modifier) => modifier._id !== deletedId
          );
        }
      )
      .addMatcher(
        modifiersApi.endpoints.deleteModifier.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to delete modifier';
        }
      );
  },
});

// Export actions
export const { setModifiers, clearModifiers } = modifierSlice.actions;

export const selectModifiers = (state: RootState) => state.modifier.modifiers;
export const selectModifiersLoading = (state: RootState) => state.modifier.isLoading;
export const selectModifiersError = (state: RootState) => state.modifier.error;

// Export reducer
export default modifierSlice.reducer;