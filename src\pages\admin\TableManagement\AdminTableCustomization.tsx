import React, { useState, useRef, useMemo, useEffect } from 'react';
import { Plus, Save, RotateCcw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  useGetSitesQuery,
} from "../../../store/api/siteManagementApi";
import {
  useGetTablesQuery,
  useUpdateTablePositionsMutation
} from "../../../store/api/TableManagementApi";
import Swal from 'sweetalert2';

interface LocationData {
  _id: string;
  [key: string]: any;
}

interface TableData {
  _id: string;
  tableNo: string;
  tableName: string;
  x: number;
  y: number;
  floor: string;
  capacity: number;
  location: string | LocationData;
}

const TablesCustomization: React.FC = () => {
  const [activeFloor, setActiveFloor] = useState<string | null>(null);
  const [tables, setTables] = useState<TableData[]>([]);
  const [originalTables, setOriginalTables] = useState<TableData[]>([]);
  const [isCustomizing, setIsCustomizing] = useState<boolean>(false);
  const [draggedTable, setDraggedTable] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [selectedSite, setSelectedSite] = useState<string | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);
  
  const userId = localStorage.getItem('userId') || '';
  
  const { data: sitesData = [], isLoading: sitesLoading } = useGetSitesQuery(userId);
  const { data: tablesData = [], isLoading: tablesLoading, refetch: refetchTables } = useGetTablesQuery(userId);
  const [updateTablePositions, { isLoading: isUpdating }] = useUpdateTablePositionsMutation();

  // Helper function to get table dimensions based on capacity
  const getTableDimensions = (capacity: number) => {
    const actualCapacity = Math.max(1, capacity || 4);
    
    // Calculate dimensions based on how many seats can fit around perimeter
    if (actualCapacity <= 2) return { width: 80, height: 60 };
    if (actualCapacity <= 4) return { width: 100, height: 80 };
    if (actualCapacity <= 6) return { width: 120, height: 80 };
    if (actualCapacity <= 8) return { width: 140, height: 100 };
    if (actualCapacity <= 10) return { width: 160, height: 120 };
    if (actualCapacity <= 12) return { width: 180, height: 120 };
    if (actualCapacity <= 16) return { width: 200, height: 140 };
    if (actualCapacity <= 20) return { width: 240, height: 160 };
    if (actualCapacity <= 24) return { width: 280, height: 180 };
    
    // For very large capacities, scale proportionally
    const baseWidth = 300;
    const baseHeight = 200;
    const scaleFactor = Math.ceil(actualCapacity / 24);
    return { 
      width: baseWidth + ((scaleFactor - 1) * 40), 
      height: baseHeight + ((scaleFactor - 1) * 30) 
    };
  };

  // Helper function to find non-overlapping position for new table
  const findNonOverlappingPosition = (existingTables: TableData[], newCapacity: number, canvasWidth: number, canvasHeight: number) => {
    const newDimensions = getTableDimensions(newCapacity);
    const margin = 30; // Minimum space between tables
    
    // Start from top-left corner
    const startX = newDimensions.width / 2 + 20; // Half width + margin from edge
    const startY = newDimensions.height / 2 + 20; // Half height + margin from edge
    const stepSize = 50;

    for (let y = startY; y < canvasHeight - newDimensions.height / 2 - margin; y += stepSize) {
      for (let x = startX; x < canvasWidth - newDimensions.width / 2 - margin; x += stepSize) {
        let overlapping = false;
        
        // Check if this position overlaps with any existing table
        for (const table of existingTables) {
          const tableDimensions = getTableDimensions(table.capacity);
          const tableLeft = table.x - tableDimensions.width / 2;
          const tableRight = table.x + tableDimensions.width / 2;
          const tableTop = table.y - tableDimensions.height / 2;
          const tableBottom = table.y + tableDimensions.height / 2;

          const newLeft = x - newDimensions.width / 2;
          const newRight = x + newDimensions.width / 2;
          const newTop = y - newDimensions.height / 2;
          const newBottom = y + newDimensions.height / 2;

          // Check for overlap with margin
          if (!(newRight + margin < tableLeft || 
                newLeft - margin > tableRight || 
                newBottom + margin < tableTop || 
                newTop - margin > tableBottom)) {
            overlapping = true;
            break;
          }
        }

        if (!overlapping) {
          return { x, y };
        }
      }
    }

    // If no non-overlapping position found, return top-left corner position
    return { x: startX, y: startY };
  };

  // Set default site when sites are loaded
  useEffect(() => {
    if (sitesData.length > 0) {
      if (!selectedSite) {
        setSelectedSite(sitesData[0]?._id || null);
      }
      
      if (!activeFloor && sitesData[0]?._id) {
        setActiveFloor(sitesData[0]?._id);
      }
    }
  }, [sitesData, selectedSite, activeFloor]);

  // Transform tables data when it's loaded or when selected site changes
 useEffect(() => {
  if (selectedSite && sitesData.length > 0) {
    let siteTablesData: TableData[] = [];
    
    if (tablesData.length > 0) {
      siteTablesData = tablesData
        .filter(table => {
          if (typeof table.location === 'object' && table.location && '_id' in table.location) {
            return table.location._id === selectedSite;
          } else if (typeof table.location === 'string') {
            return table.location === selectedSite;
          }
          return false;
        })
        .map(table => ({
          _id: table._id || '',
          tableNo: table.tableNo || '',
          tableName: table.tableName || '',
          x: table.x || findNonOverlappingPosition([], table.capacity || 4, 800, 400).x,
          y: table.y || findNonOverlappingPosition([], table.capacity || 4, 800, 400).y,
          floor: selectedSite,
          capacity: table.capacity || 4,
          location: typeof table.location === 'string' ? table.location : (table.location as LocationData)
        }));
    }
    
    setTables(siteTablesData);
    setOriginalTables([...siteTablesData]);
  }
}, [tablesData, selectedSite, sitesData]);

  // Filter tables based on active floor
  const filteredTables = useMemo(() => 
    tables.filter(table => table.floor === activeFloor), 
    [tables, activeFloor]
  );

  const handleFloorClick = (siteId: string) => {
    setActiveFloor(siteId);
    setSelectedSite(siteId);
  };
 
  const handleMouseDown = (e: React.MouseEvent, tableId: string) => {
    if (!isCustomizing) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    if (canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect();
      const table = tables.find(t => t._id === tableId);
      
      if (table) {
        setDraggedTable(tableId);
        // Calculate offset from mouse to table center, not table top-left
        setDragOffset({ 
          x: e.clientX - rect.left - table.x, 
          y: e.clientY - rect.top - table.y
        });
      }
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (draggedTable !== null && canvasRef.current && isCustomizing) {
      e.preventDefault();
      const rect = canvasRef.current.getBoundingClientRect();
      const table = tables.find(t => t._id === draggedTable);
      
      if (table) {
        const dimensions = getTableDimensions(table.capacity);
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        // Calculate new position considering the offset
        let newX = mouseX - dragOffset.x;
        let newY = mouseY - dragOffset.y;
        
        // Apply boundaries based on table size AND seat positions
        const halfWidth = dimensions.width / 2;
        const halfHeight = dimensions.height / 2;
        
        // Extra margin for seats (seats extend beyond table boundaries)
        const seatMargin = 30;
        
        // Ensure table + seats stay within canvas bounds
        newX = Math.max(halfWidth + seatMargin, Math.min(rect.width - halfWidth - seatMargin, newX));
        newY = Math.max(halfHeight + seatMargin, Math.min(rect.height - halfHeight - seatMargin, newY));
        
        setTables(prevTables => prevTables.map(t => 
          t._id === draggedTable 
            ? { ...t, x: newX, y: newY } 
            : t
        ));
      }
    }
  };

  const handleMouseUp = () => {
    setDraggedTable(null);
    setDragOffset({ x: 0, y: 0 });
  };

  const navigate = useNavigate();

  const handleAddTable = () => {
    if (isCustomizing) {
      saveChanges();
    } else {
      navigate('/admin/tables-management/tables/table-form');
    }
  };

  const toggleCustomization = () => {
    if (!isCustomizing) {
      setOriginalTables([...tables]);
      setIsCustomizing(true);
    } else {
      resetChanges();
    }
  };

  const resetChanges = () => {
    setTables([...originalTables]);
    setIsCustomizing(false);
    setDraggedTable(null);
  };

const saveChanges = async () => {
  if (!selectedSite) {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: 'No site selected',
      confirmButtonColor: '#d33'
    });
    return;
  }

  try {
    // Create the payload with all necessary table data
    const tableUpdates = tables.map(table => ({
      _id: table._id,
      tableNo: table.tableNo,
      tableName: table.tableName,
      x: Math.round(table.x),
      y: Math.round(table.y),
      floor: table.floor,
      capacity: table.capacity,
      location: typeof table.location === 'string' ? table.location : table.location._id
    }));

    // Send the update
    await updateTablePositions({
      siteId: selectedSite,
      tablePositions: tableUpdates
    }).unwrap();

    // Update both tables and originalTables states
    setOriginalTables([...tables]);
    setIsCustomizing(false);
    setDraggedTable(null);
    
    // Show success message
    Swal.fire({
      icon: 'success',
      title: 'Success',
      text: 'Table positions saved successfully',
      confirmButtonColor: '#3085d6'
    });
    
    // Refetch data to ensure consistency
    await refetchTables();
  } catch (error) {
    console.error("Failed to save table positions:", error);
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: 'Failed to save table positions',
      confirmButtonColor: '#d33'
    });
  }
};

  // Enhanced function to render ALL seats dynamically with larger dimensions
  const renderSeats = (capacity: number, tableWidth: number, tableHeight: number) => {
    const actualCapacity = Math.max(1, capacity || 4);
    const seats = [];
    
    // Calculate seat dimensions - INCREASED SIZES
    const seatWidth = 18; // Increased from 12
    const seatHeight = 8; // Increased from 4
    const seatThickness = 8; // Increased from 4
    const seatSpacing = 4; // Increased from 2 for better spacing
    
    // Calculate how many seats fit on each side with new dimensions
    const topSeats = Math.floor((tableWidth - 20) / (seatWidth + seatSpacing));
    const sideSeats = Math.floor((tableHeight - 20) / (seatWidth + seatSpacing));
    const bottomSeats = Math.floor((tableWidth - 20) / (seatWidth + seatSpacing));
    const leftSeats = Math.floor((tableHeight - 20) / (seatWidth + seatSpacing));
    
    // Calculate total seats that can fit around perimeter
    const maxPerimeterSeats = topSeats + sideSeats + bottomSeats + leftSeats;
    
    // If capacity is less than or equal to what fits around perimeter, distribute normally
    if (actualCapacity <= maxPerimeterSeats) {
      // Distribute seats around the perimeter
      let seatsPlaced = 0;
      
      // Top side
      const topSeatCount = Math.min(topSeats, Math.ceil(actualCapacity * (topSeats / maxPerimeterSeats)));
      for (let i = 0; i < topSeatCount && seatsPlaced < actualCapacity; i++) {
        const seatX = (tableWidth / (topSeatCount + 1)) * (i + 1) - (seatWidth / 2);
        seats.push(
          <div key={`top-${i}`} className="absolute bg-white border-2 border-gray-400 rounded-md shadow-sm"
               style={{
                 width: `${seatWidth}px`,
                 height: `${seatHeight}px`,
                 top: `-${seatHeight + 4}px`, // Increased spacing
                 left: `${seatX}px`
               }} />
        );
        seatsPlaced++;
      }
      
      // Right side
      const rightSeatCount = Math.min(sideSeats, Math.ceil((actualCapacity - seatsPlaced) * (sideSeats / (maxPerimeterSeats - topSeatCount))));
      for (let i = 0; i < rightSeatCount && seatsPlaced < actualCapacity; i++) {
        const seatY = (tableHeight / (rightSeatCount + 1)) * (i + 1) - (seatWidth / 2);
        seats.push(
          <div key={`right-${i}`} className="absolute bg-white border-2 border-gray-400 rounded-md shadow-sm"
               style={{
                 width: `${seatThickness}px`,
                 height: `${seatWidth}px`,
                 right: `-${seatThickness + 4}px`, // Increased spacing
                 top: `${seatY}px`
               }} />
        );
        seatsPlaced++;
      }
      
      // Bottom side
      const bottomSeatCount = Math.min(bottomSeats, Math.ceil((actualCapacity - seatsPlaced) * (bottomSeats / (maxPerimeterSeats - topSeatCount - rightSeatCount))));
      for (let i = 0; i < bottomSeatCount && seatsPlaced < actualCapacity; i++) {
        const seatX = (tableWidth / (bottomSeatCount + 1)) * (i + 1) - (seatWidth / 2);
        seats.push(
          <div key={`bottom-${i}`} className="absolute bg-white border-2 border-gray-400 rounded-md shadow-sm"
               style={{
                 width: `${seatWidth}px`,
                 height: `${seatHeight}px`,
                 bottom: `-${seatHeight + 4}px`, // Increased spacing
                 left: `${seatX}px`
               }} />
        );
        seatsPlaced++;
      }
      
      // Left side
      const leftSeatCount = actualCapacity - seatsPlaced;
      for (let i = 0; i < leftSeatCount && seatsPlaced < actualCapacity; i++) {
        const seatY = (tableHeight / (leftSeatCount + 1)) * (i + 1) - (seatWidth / 2);
        seats.push(
          <div key={`left-${i}`} className="absolute bg-white border-2 border-gray-400 rounded-md shadow-sm"
               style={{
                 width: `${seatThickness}px`,
                 height: `${seatWidth}px`,
                 left: `-${seatThickness + 4}px`, // Increased spacing
                 top: `${seatY}px`
               }} />
        );
        seatsPlaced++;
      }
    } else {
      // For very high capacity, create multiple rows/layers of seats
      let seatsPlaced = 0;
      const layers = Math.ceil(actualCapacity / maxPerimeterSeats);
      
      for (let layer = 0; layer < layers && seatsPlaced < actualCapacity; layer++) {
        const layerOffset = (layer + 1) * (seatHeight + 8); // Increased spacing between layers
        const seatsInThisLayer = Math.min(maxPerimeterSeats, actualCapacity - seatsPlaced);
        
        // Top side for this layer
        const layerTopSeats = Math.min(topSeats, Math.ceil(seatsInThisLayer * (topSeats / maxPerimeterSeats)));
        for (let i = 0; i < layerTopSeats && seatsPlaced < actualCapacity; i++) {
          const seatX = (tableWidth / (layerTopSeats + 1)) * (i + 1) - (seatWidth / 2);
          seats.push(
            <div key={`top-${layer}-${i}`} className="absolute bg-white border-2 border-gray-400 rounded-md shadow-sm"
                 style={{
                   width: `${seatWidth}px`,
                   height: `${seatHeight}px`,
                   top: `-${layerOffset + 4}px`,
                   left: `${seatX}px`
                 }} />
          );
          seatsPlaced++;
        }
        
        // Right side
        const layerRightSeats = Math.min(sideSeats, Math.ceil((seatsInThisLayer - layerTopSeats) * (sideSeats / (maxPerimeterSeats - layerTopSeats))));
        for (let i = 0; i < layerRightSeats && seatsPlaced < actualCapacity; i++) {
          const seatY = (tableHeight / (layerRightSeats + 1)) * (i + 1) - (seatWidth / 2);
          seats.push(
            <div key={`right-${layer}-${i}`} className="absolute bg-white border-2 border-gray-400 rounded-md shadow-sm"
                 style={{
                   width: `${seatThickness}px`,
                   height: `${seatWidth}px`,
                   right: `-${layerOffset + 4}px`,
                   top: `${seatY}px`
                 }} />
          );
          seatsPlaced++;
        }
        
        // Bottom side
        const layerBottomSeats = Math.min(bottomSeats, Math.ceil((seatsInThisLayer - layerTopSeats - layerRightSeats) * (bottomSeats / (maxPerimeterSeats - layerTopSeats - layerRightSeats))));
        for (let i = 0; i < layerBottomSeats && seatsPlaced < actualCapacity; i++) {
          const seatX = (tableWidth / (layerBottomSeats + 1)) * (i + 1) - (seatWidth / 2);
          seats.push(
            <div key={`bottom-${layer}-${i}`} className="absolute bg-white border-2 border-gray-400 rounded-md shadow-sm"
                 style={{
                   width: `${seatWidth}px`,
                   height: `${seatHeight}px`,
                   bottom: `-${layerOffset + 4}px`,
                   left: `${seatX}px`
                 }} />
          );
          seatsPlaced++;
        }
        
        // Left side
        const layerLeftSeats = seatsInThisLayer - layerTopSeats - layerRightSeats - layerBottomSeats;
        for (let i = 0; i < layerLeftSeats && seatsPlaced < actualCapacity; i++) {
          const seatY = (tableHeight / (layerLeftSeats + 1)) * (i + 1) - (seatWidth / 2);
          seats.push(
            <div key={`left-${layer}-${i}`} className="absolute bg-white border-2 border-gray-400 rounded-md shadow-sm"
                 style={{
                   width: `${seatThickness}px`,
                   height: `${seatWidth}px`,
                   left: `-${layerOffset + 4}px`,
                   top: `${seatY}px`
                 }} />
          );
          seatsPlaced++;
        }
      }
    }
    
    return seats;
  };

  // Loading state
  if (tablesLoading || sitesLoading) {
    return (
      <div className="flex justify-center items-start bg-white h-screen pt-[35vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading Tables Customization...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 w-full bg-gray-50">
      <div className="flex md:flex-row flex-col justify-between items-center mb-6 border border-gray-200 p-2 rounded-2xl bg-white">
        <h1 className="text-3xl font-bold text-gray-800 p-3">Tables Customization</h1>
        
        <div className="flex space-x-2">
          <button 
            className={`border px-4 py-2 rounded-md flex items-center ${
              isCustomizing
                ? 'bg-white border-red-500 hover:bg-red-500 hover:text-white text-red-500'
                : 'bg-white border-orange-500 hover:bg-orange-500 hover:text-white text-orange-500'
            }`}
            onClick={toggleCustomization}
          >
            {isCustomizing ? (
              <>Reset Changes <RotateCcw size={16} className="ml-1" /></>
            ) : (
              <>Customize Tables <Plus size={16} className="ml-1" /></>
            )}
          </button>
          <button 
            className="bg-orange-500 text-white px-4 py-2 rounded-md flex items-center"
            onClick={handleAddTable}
            disabled={isUpdating}
          >
            {isCustomizing ? (
              <>Save Changes <Save size={16} className="ml-1" /></>
            ) : (
              <>Add Table <Plus size={16} className="ml-1" /></>
            )}
          </button>
        </div>
      </div>
      
      <div className="mb-6 flex space-x-2 flex-wrap">
        {sitesData.map((site) => (
          <button
            key={site._id}
            className={`px-4 py-1 rounded-xl mb-2 ${
              activeFloor === site._id 
                ? 'bg-orange-100 text-orange-600 border border-orange-500' 
                : 'text-gray-700 hover:bg-gray-100'
            }`}
            onClick={() => site._id && handleFloorClick(site._id)}
          >
            {site.siteName}
          </button>
        ))}
      </div>
      
      <div 
        ref={canvasRef}
        className={`bg-white rounded-md border border-gray-200 h-96 relative overflow-hidden ${
          isCustomizing ? 'cursor-move' : ''
        }`}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        style={{ userSelect: 'none' }} // Prevent text selection during drag
      >
        {filteredTables.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-lg mb-4 text-gray-700 font-medium">
              {selectedSite 
                ? sitesData.find(s => s._id === activeFloor)?.siteName || 'Selected Site'
                : 'Please select a site to view tables'
              }
            </div>
            {selectedSite && (
              <div className="text-center">
                <div className="mb-4">
                  <div className="text-sm text-gray-600">Expected Tables</div>
                  <div className="text-2xl font-bold text-orange-500">
                    {sitesData.find(s => s._id === activeFloor)?.numberOfTables || '0'}
                  </div>
                </div>
                <div className="mb-4">
                  <div className="text-sm text-gray-600">Current Tables</div>
                  <div className="text-2xl font-bold text-gray-700">0</div>
                </div>
                <button
                  onClick={() => navigate('/admin/tables-management/tables/table-form')}
                  className="mt-2 px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors"
                >
                  Add Tables
                </button>
              </div>
            )}
          </div>
        ) : (
          filteredTables.map(table => {
            const dimensions = getTableDimensions(table.capacity);
            return (
              <div 
                key={table._id}
                className="absolute"
                style={{ 
                  left: `${table.x - dimensions.width / 2}px`, 
                  top: `${table.y - dimensions.height / 2}px`,
                  cursor: isCustomizing ? (draggedTable === table._id ? 'grabbing' : 'grab') : 'default'
                }}
              >
                <div className="relative" style={{ width: `${dimensions.width}px`, height: `${dimensions.height}px` }}>
                  <div 
                    className="w-full h-full border-2 border-gray-300 rounded-md flex items-center justify-center"
                    style={{ 
                      minWidth: `${dimensions.width}px`, 
                      minHeight: `${dimensions.height}px` 
                    }}
                  >
                    <div 
                      className={`rounded-md flex flex-col items-center justify-center bg-orange-100 transition-colors duration-150 ${
                        isCustomizing ? 'hover:bg-orange-200' : ''
                      } ${
                        draggedTable === table._id ? 'bg-orange-300 shadow-lg' : ''
                      }`}
                      style={{ 
                        width: `${dimensions.width - 8}px`, 
                        height: `${dimensions.height - 8}px` 
                      }}
                      onMouseDown={(e) => handleMouseDown(e, table._id)}
                    >
                      <span className="text-gray-800 font-bold text-sm">{table.tableNo}</span>
                      <span className="text-gray-600 text-xs">({table.capacity || 4} seats)</span>
                    </div>
                  </div>
                  
                  {renderSeats(table.capacity || 4, dimensions.width, dimensions.height)}
                </div>
              </div>
            );
          })
        )}
      </div>
      
      <div className="mt-4 bg-white p-3 rounded-md border border-gray-200">
        <p className="text-gray-700 text-sm">
          <span className="font-medium">Current site:</span> {sitesData.find(s => s._id === activeFloor)?.siteName || 'None'} 
          <span className="ml-4 font-medium">Tables:</span> {filteredTables.length}
          <span className="ml-4 font-medium">Expected Tables:</span> {sitesData.find(s => s._id === activeFloor)?.numberOfTables || '0'}
          {isCustomizing && <span className="ml-4 text-orange-500 font-medium">Customization mode active</span>}
        </p>
      </div>
    </div>
  );
};

export default TablesCustomization;