import { createSlice } from '@reduxjs/toolkit';
// import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import { couponApi } from '../api/couponsApi';

// Define proper types for coupon data with required fields
export interface CouponItem {
  _id: string;
  series: string;
  description: string;
  discount: number;
  start: number;
  end: number;
  startDate: string;
  endDate: string;
  userId: string;
  discountType: string;
  publish: boolean;
  message?: string;
  createdAt?: string;
  updatedAt?: string;
  __v?: number;
}

interface CouponState {
  coupens: CouponItem[];
  isLoading: boolean;
  error: string | null;
}

const initialState: CouponState = {
  coupens: [],
  isLoading: false,
  error: null,
};

const coupensSlice = createSlice({
  name: 'coupens',
  initialState,
  reducers: {
    // Local reducers for manual operations if needed
    setCoupens: (state, action) => {
      state.coupens = action.payload;
    },
    clearCoupens: (state) => {
      state.coupens = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle getCouponsByUser query
      .addMatcher(
        couponApi.endpoints.getCouponsByUser.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        couponApi.endpoints.getCouponsByUser.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          // Convert potentially undefined _id to required format
          state.coupens = action.payload.map(coupon => ({
            ...coupon,
            _id: coupon._id || '',  // Convert undefined to empty string to satisfy TypeScript
          })) as CouponItem[];
        }
      )
      .addMatcher(
        couponApi.endpoints.getCouponsByUser.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to fetch coupons';
        }
      )
      
      // Handle addCoupon mutation
      .addMatcher(
        couponApi.endpoints.addCoupon.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        couponApi.endpoints.addCoupon.matchFulfilled,
        (state) => {
          state.isLoading = false;
          // Note: We don't add the coupon to the state here because
          // invalidatesTags in the API will trigger a refetch
        }
      )
      .addMatcher(
        couponApi.endpoints.addCoupon.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to create coupon';
        }
      )
      
      // Handle updateCoupon mutation
      .addMatcher(
        couponApi.endpoints.updateCoupon.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        couponApi.endpoints.updateCoupon.matchFulfilled,
        (state) => {
          state.isLoading = false;
          // The updated data will be fetched again due to invalidateTags
        }
      )
      .addMatcher(
        couponApi.endpoints.updateCoupon.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to update coupon';
        }
      )
      
      // Handle deleteCoupon mutation
      .addMatcher(
        couponApi.endpoints.deleteCoupon.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        couponApi.endpoints.deleteCoupon.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          // Filter out the deleted coupon
          const deletedId = action.meta.arg.originalArgs;
          state.coupens = state.coupens.filter(
            (coupon) => coupon._id !== deletedId
          );
        }
      )
      .addMatcher(
        couponApi.endpoints.deleteCoupon.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to delete coupon';
        }
      );
  },
});

// Export actions
export const { setCoupens, clearCoupens } = coupensSlice.actions;

// Export selectors
export const selectCoupens = (state: RootState) => state.coupens.coupens;
export const selectCoupensLoading = (state: RootState) => state.coupens.isLoading;
export const selectCoupensError = (state: RootState) => state.coupens.error;

// Export reducer
export default coupensSlice.reducer;