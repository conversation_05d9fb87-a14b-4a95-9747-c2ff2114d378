import { Outlet, useNavigate, useLocation } from 'react-router-dom';

const AdministrationLayout = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get the active tab from the URL path
  const getActiveTab = () => {
    const path = location.pathname.split('/');
    return path[path.length - 1];
  };
  
  const activeTab = getActiveTab();
  
  const tabs = [
    { id: "business-name", label: "Business Name" },
    { id: "payment-types", label: "Payment Types" },
    { id: "tax", label: "Tax" },
    { id: "tax-collected", label: "Tax Collected" },
    { id: "tax-paid", label: "Tax Paid" },
    { id: "customization", label: "Customization" },
    { id: "subscription-plan", label: "Subscription Plan" },
  ];

  const handleTabClick = (tabId: string) => {
    navigate(`/admin/administration/${tabId}`);
  };

  return (
    <div className="p-4 bg-gray-50 min-h-screen">
      <div className="flex items-center justify-between flex-wrap gap-4 border border-gray-300 py-1 px-3 mb-4 rounded-2xl bg-white">
        <h2 className="text-2xl font-semibold p-3">Administration</h2>
        
      </div>
      
      <div className="flex gap-2 mb-4 overflow-x-auto">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => handleTabClick(tab.id)}
            className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${
              activeTab === tab.id
                ? "bg-orange-500 text-white"
                : "bg-white text-gray-700 border border-gray-200"
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>
      
      <div>
        {/* Content area that will be populated by Outlet in react-router */}
        <Outlet />
      </div>
    </div>
  );
};

export default AdministrationLayout;