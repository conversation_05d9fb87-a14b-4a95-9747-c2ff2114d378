import { useState, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import {
    usePostPaymentTypeMutation,
    usePutPaymentTypeMutation,
    useGetPaymentTypeQuery
} from '../../../store/api/paymentTypesApi';
import { toast } from 'react-toastify';
import { useNavigate, useParams } from 'react-router-dom';


export default function PaymentTypeForm() {
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();
    const isEditMode = Boolean(id);

    const userId = localStorage.getItem('userId') || '';

    const [postPaymentType, { isLoading: isCreating }] = usePostPaymentTypeMutation();
    const [putPaymentType, { isLoading: isUpdating }] = usePutPaymentTypeMutation();

    const { data: paymentTypeData, isLoading: isLoadingPaymentType } = useGetPaymentTypeQuery(id || '', {
        skip: !isEditMode,
    });

    const [formData, setFormData] = useState({
        name: '',
        isActive: false,
        defaultPayment: false,
        showCaption: false
    });

    const [paymentMethod, setPaymentMethod] = useState('');
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);

    // Load existing data when in edit mode
    useEffect(() => {
        if (isEditMode && paymentTypeData) {
            setFormData({
                name: paymentTypeData.name || '',
                isActive: paymentTypeData.isActive === 'true' || false,
                defaultPayment: paymentTypeData.defaultPayment === 'true' || false,
                showCaption: paymentTypeData.showCaption === 'true' || false
            });
            setPaymentMethod(paymentTypeData.name || '');
        }
    }, [paymentTypeData, isEditMode]);

    const handleToggle = (name: 'isActive' | 'defaultPayment' | 'showCaption') => {
        setFormData({
            ...formData,
            [name]: !formData[name]
        });
    };

    const paymentMethods = [
        'Cash',
        'Terminal Pay',
        'Stripe Checkout',
        'Check',
        'GiftCard'
    ];

    const handleSubmit = async () => {
        try {
            // Validate payment method is selected
            if (!paymentMethod) {
                toast.error('Please select a payment method');
                return;
            }

            // Prepare data for API
            const submitData = {
                name: paymentMethod,
                isActive: formData.isActive.toString(),
                defaultPayment: formData.defaultPayment.toString(),
                showCaption: formData.showCaption.toString(),
                userId: userId
            };

            console.log(`${isEditMode ? 'Updating' : 'Creating'} payment type...`, submitData);

            if (isEditMode && id) {
                await putPaymentType({ id, formData: submitData }).unwrap();
                toast.success('Payment type updated successfully!');
            } else {
                await postPaymentType(submitData).unwrap();
                toast.success('Payment type added successfully!');
            }

            // Redirect to payment types list
            navigate('/admin/administration/payment-types');
        } catch (error) {
            console.error('Error submitting form:', error);
            toast.error(`Failed to ${isEditMode ? 'update' : 'add'} payment type. Please try again.`);
        }
    };

    const handleCancel = () => {
        navigate('/admin/administration/payment-types');
    };

    if (isEditMode && isLoadingPaymentType) {
        return (
            <div className="w-full p-6 flex justify-center items-center">
                <p className="text-gray-600">Loading payment type data...</p>
            </div>
        );
    }

    return (
        <div className="w-full p-2">
            {/* Payment Type Details Section */}
            <div className="bg-orange-50 p-4 mb-6 rounded-md">
                <h2 className="text-lg font-semibold text-gray-800">Payment Type Details</h2>
            </div>

            {/* Payment Type Selector */}
            <div className="mb-8">
                <label className="block text-gray-800 mb-2">
                    Select Payment Types <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                    <button
                        type="button"
                        className="w-full text-left border border-gray-300 rounded-md p-2.5 bg-white flex justify-between items-center"
                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    >
                        <span className="text-gray-700">
                            {paymentMethod || 'Select Payment method'}
                        </span>
                        <ChevronDown className="h-5 w-5 text-gray-400" />
                    </button>

                    {isDropdownOpen && (
                        <ul className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                            {paymentMethods.map((method) => (
                                <li
                                    key={method}
                                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                    onClick={() => {
                                        setPaymentMethod(method);
                                        setIsDropdownOpen(false);
                                    }}
                                >
                                    {method}
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
            </div>

            {/* Permissions Section */}
            <div className="bg-orange-50 p-4 mb-6 rounded-md">
                <h2 className="text-lg font-semibold text-gray-800">Permissions</h2>
            </div>

            {/* Toggle Switches */}
            <div className="space-y-4 mb-8">
                <div className="flex items-center">
                    <button
                        type="button"
                        className={`relative inline-flex h-6 w-11 items-center rounded-full ${formData.isActive ? 'bg-orange-500' : 'bg-gray-200'
                            }`}
                        onClick={() => handleToggle('isActive')}
                    >
                        <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${formData.isActive ? 'translate-x-6' : 'translate-x-1'
                                }`}
                        />
                    </button>
                    <span className="ml-3 text-gray-700">Activate</span>
                </div>

                <div className="flex items-center">
                    <button
                        type="button"
                        className={`relative inline-flex h-6 w-11 items-center rounded-full ${formData.defaultPayment ? 'bg-orange-500' : 'bg-gray-200'
                            }`}
                        onClick={() => handleToggle('defaultPayment')}
                    >
                        <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${formData.defaultPayment ? 'translate-x-6' : 'translate-x-1'
                                }`}
                        />
                    </button>
                    <span className="ml-3 text-gray-700">Make Default</span>
                </div>

                <div className="flex items-center">
                    <button
                        type="button"
                        className={`relative inline-flex h-6 w-11 items-center rounded-full ${formData.showCaption ? 'bg-orange-500' : 'bg-gray-200'
                            }`}
                        onClick={() => handleToggle('showCaption')}
                    >
                        <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${formData.showCaption ? 'translate-x-6' : 'translate-x-1'
                                }`}
                        />
                    </button>
                    <span className="ml-3 text-gray-700">Picture Availability</span>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4 justify-end">
                <button
                    type="button"
                    onClick={handleCancel}
                    className="border border-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-50 transition"
                >
                    Cancel
                </button>
                <button
                    type="button"
                    onClick={handleSubmit}
                    disabled={isCreating || isUpdating}
                    className={`${(isCreating || isUpdating) ? 'bg-orange-300' : 'bg-orange-500 hover:bg-orange-600'} text-white px-6 py-2 rounded-md transition`}
                >
                    {isEditMode ?
                        (isUpdating ? 'Updating...' : 'Update Payment Type') :
                        (isCreating ? 'Adding...' : 'Add Payment Type')
                    }
                </button>
            </div>
        </div>
    );
}