import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';

interface SelectedTable {
  id: string;
  name: string;
  number?: string;
}

interface SelectedTablesState {
  selectedTables: SelectedTable[];
}

const initialState: SelectedTablesState = {
  selectedTables: [],
};

const selectedTablesSlice = createSlice({
  name: 'selectedTables',
  initialState,
  reducers: {
    addTable: (state, action: PayloadAction<SelectedTable>) => {
      // Only add if not already in the array
      if (!state.selectedTables.find(table => table.id === action.payload.id)) {
        state.selectedTables.push(action.payload);
      }
    },
    removeTable: (state, action: PayloadAction<string>) => {
      state.selectedTables = state.selectedTables.filter(
        (table) => table.id !== action.payload
      );
    },
    clearSelectedTables: (state) => {
      state.selectedTables = [];
    },
    setSelectedTables: (state, action: PayloadAction<SelectedTable[]>) => {
      state.selectedTables = action.payload;
    },
  },
});

export const {
  addTable,
  removeTable,
  clearSelectedTables,
  setSelectedTables
} = selectedTablesSlice.actions;

export const selectSelectedTables = (state: RootState) => state.selectedTables.selectedTables;

export type { SelectedTable };
export default selectedTablesSlice.reducer;
