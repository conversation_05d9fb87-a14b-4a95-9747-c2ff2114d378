import React, { useState } from 'react';
import CustomModal from '../../CustomModal';
import { Printer } from 'lucide-react';
import { useGetXReportQuery } from '../../../store/api/pos/orderapi';

interface XReportSearchResultsProps {
    isOpen: boolean;
    onClose: () => void;
    searchParams: {
        employee: string;
        employeeId: string | null;
        timeRange: string;
        dateRange: { startDate: string; endDate: string } | null;
        timeSelection: { startTime: string; endTime: string } | null;
        showOnlySelectedCustomer: boolean;
        selectedEmployees: Array<{
            _id: string;
            firstName: string;
            lastName: string;
        }>;
        selectedCustomerId?: string;
        isCustomDateRange?: boolean;
    };
}

const XReportSearchResults: React.FC<XReportSearchResultsProps> = ({
    isOpen,
    onClose,
    searchParams,
}) => {
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 5; // Show only 5 items per page as per requirements
    const userId = localStorage.getItem("userId") || "";

    // Convert timeRange to reportType
    const getReportType = (): 'Daily' | 'Weekly' | 'Monthly' | 'date' => {
        // If custom date range is selected, use 'date' as reportType
        if (searchParams.isCustomDateRange) {
            return 'date';
        }

        // Otherwise, determine reportType based on timeRange
        switch (searchParams.timeRange) {
            case 'Today':
            case 'Yesterday':
                return 'Daily';
            case 'This Week':
                return 'Weekly';
            case 'This Month':
                return 'Monthly';
            default:
                return 'Daily';
        }
    };

    // Prepare API parameters based on the selection
    const apiParams: {
        userId: string;
        reportType: 'Daily' | 'Weekly' | 'Monthly' | 'date';
        paymentIds: string[];
        status: 'payment' | 'customers';
        startDate?: string;
        endDate?: string;
        employeeId?: string;
        customerId?: string;
    } = {
        userId,
        // For custom date range, use specific parameters
        reportType: getReportType(),
        paymentIds: [] as string[],
        // Use 'payment' status for custom date range, otherwise 'customers'
        status: searchParams.isCustomDateRange ? 'payment' as const : 'customers' as const,
        // Include date range for custom date selection
        startDate: searchParams.dateRange?.startDate,
        endDate: searchParams.dateRange?.endDate,
    };

    // Add employeeId only if selected and not using customer filter
    if (!searchParams.selectedCustomerId && searchParams.employeeId) {
        apiParams.employeeId = searchParams.employeeId;
    }

    // Add customerId only if a customer is selected and not using custom date range
    if (searchParams.selectedCustomerId && !searchParams.isCustomDateRange) {
        apiParams.customerId = searchParams.selectedCustomerId;
    }

    console.log('API Parameters:', apiParams);

    // Fetch data from API
    const { data: reportData, isLoading, error } = useGetXReportQuery(apiParams);

    // Process customer data to display
    const customerItems = React.useMemo(() => {
        // If no data is available
        if (!reportData) {
            return [];
        }

        // For custom date range with payment status
        if (searchParams.isCustomDateRange && apiParams.status === 'payment') {
            // If we have breakdown data for payments, transform it to customer format
            if (reportData.breakdown && Array.isArray(reportData.breakdown)) {
                return reportData.breakdown.map(item => ({
                    customerName: item.paymentType || 'Unknown',
                    count: item.totalOrders || 0,
                    total: item.totalRevenue || 0
                }));
            }
        }

        // For customer status - if we have breakdown data with customerName
        if (apiParams.status === 'customers' && reportData.breakdown &&
            Array.isArray(reportData.breakdown) &&
            reportData.breakdown.length > 0 &&
            reportData.breakdown[0].customerName) {
            return reportData.breakdown.map(item => ({
                customerName: item.customerName || 'Unknown',
                count: item.totalOrders || 0,
                total: item.totalRevenue || 0
            }));
        }

        // Otherwise, process from orders
        if (!reportData.orders || !Array.isArray(reportData.orders)) {
            return [];
        }

        // Group orders by customer or payment type based on status
        const itemMap = new Map();

        reportData.orders.forEach(order => {
            // Get name based on status
            const itemName = apiParams.status === 'payment'
                ? (order.paymentType || 'Unknown')
                : (order.customerName ||
                    (order.customerInfo ? order.customerInfo.name : null) ||
                    'Guest');

            if (!itemMap.has(itemName)) {
                itemMap.set(itemName, {
                    customerName: itemName,
                    count: 0,
                    total: 0
                });
            }

            const item = itemMap.get(itemName);
            item.count += 1;
            item.total += order.totalPrice || 0;
        });

        return Array.from(itemMap.values());
    }, [reportData, searchParams.isCustomDateRange, apiParams.status]);

    // Get paginated items
    const paginatedItems = React.useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return customerItems.slice(startIndex, endIndex);
    }, [customerItems, currentPage, itemsPerPage]);

    // Calculate total pages
    const totalPages = React.useMemo(() => {
        return Math.ceil(customerItems.length / itemsPerPage);
    }, [customerItems, itemsPerPage]);

    // Calculate total from the orders data
    const totalSales = React.useMemo(() => {
        if (!reportData?.summary) return 0;
        return reportData.summary.totalRevenue || 0;
    }, [reportData]);

    // Handle pagination
    const handlePrevPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    const handleNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    // Handle print
    const handlePrint = () => {
        window.print();
    };

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={onClose}
            title="X-Report Sales Summary"
            width="max-w-3xl"
            zIndex={100}
        >
            <div className="p-6">
                {/* Employee Info */}
                <div className="flex items-center mb-6">
                    <div className="text-gray-700 font-medium">Employee:</div>
                    <div className="ml-2 text-orange-500 font-bold text-lg">
                        {searchParams.selectedEmployees.length > 0
                            ? searchParams.selectedEmployees.map(emp => `${emp.firstName} ${emp.lastName}`).join(', ')
                            : 'Admin'}
                    </div>
                </div>

                {isLoading ? (
                    // Loading state
                    <div className="flex justify-center items-center p-12">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
                        <span className="ml-3 text-gray-600">Loading report data...</span>
                    </div>
                ) : error ? (
                    // Error state
                    <div className="p-6 bg-red-50 border border-red-200 rounded-lg text-red-600 mb-6">
                        <p className="font-medium mb-2">Error loading report data</p>
                        <p>Please try again later or contact support if the problem persists.</p>
                    </div>
                ) : (
                    <>
                        {/* Customer Table */}
                        <div className="mb-6 border rounded-lg overflow-hidden">
                            {/* Table Header */}
                            <div className="grid grid-cols-3 bg-gray-50 border-b">
                                <div className="p-4 font-medium text-gray-600">Customer Name</div>
                                <div className="p-4 font-medium text-gray-600 text-center">Count</div>
                                <div className="p-4 font-medium text-gray-600 text-right">Total</div>
                            </div>

                            {/* Table Body */}
                            <div className="divide-y">
                                {customerItems.length === 0 ? (
                                    <div className="p-4 text-center text-gray-500">No customer data available</div>
                                ) : (
                                    paginatedItems.map((item, index) => (
                                        <div key={index} className="grid grid-cols-3 hover:bg-gray-50">
                                            <div className="p-4 text-gray-700">{item.customerName}</div>
                                            <div className="p-4 text-gray-700 text-center">{item.count}</div>
                                            <div className="p-4 text-gray-700 text-right">$ {item.total.toFixed(2)}</div>
                                        </div>
                                    ))
                                )}
                            </div>
                        </div>

                        {/* Total Summary */}
                        <div className="bg-gray-50 p-4 rounded-lg mb-6 flex justify-end items-center">
                            <div className="flex items-center">
                                <span className="text-gray-700 font-medium">Total:</span>
                                <span className="ml-2 text-orange-500 font-bold text-xl">${totalSales.toFixed(2)}</span>
                            </div>
                        </div>
                    </>
                )}

                {/* Pagination and Action Buttons */}
                <div className="flex justify-between items-center">
                    {/* Pagination */}
                    <div className="flex items-center border rounded-full overflow-hidden">
                        <button
                            onClick={handlePrevPage}
                            className="px-4 py-2 flex items-center text-gray-500 hover:bg-gray-50"
                            disabled={currentPage <= 1}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                            Previous
                        </button>
                        <div className="px-4 py-2 border-l border-r text-gray-700">
                            {currentPage}
                        </div>
                        <button
                            onClick={handleNextPage}
                            className="px-4 py-2 flex items-center text-gray-500 hover:bg-gray-50"
                            disabled={currentPage >= totalPages}
                        >
                            Next
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-4">
                        <button
                            onClick={handlePrint}
                            className="px-6 py-2.5 bg-black text-white rounded-full flex items-center"
                            disabled={isLoading || !!error}
                        >
                            <Printer className="w-4 h-4 mr-2" />
                            Print
                        </button>
                        <button
                            onClick={onClose}
                            className="px-6 py-2.5 border border-orange-500 text-orange-500 rounded-full"
                        >
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </CustomModal>
    );
};

export default XReportSearchResults;
