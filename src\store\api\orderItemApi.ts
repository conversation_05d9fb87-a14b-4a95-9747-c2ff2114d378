import { baseApi } from "./baseApi";

// Extend your existing API with a new endpoint
export const orderItemsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    posOrderItems: builder.query({
      query: (userId) => ({
        url: `orderitem/lastWeek`,
        params: { userId },
      }),
      transformResponse: (response) => response,
      providesTags: ['posOrderItems'],
    }),
    deleteOrder: builder.mutation({
      query: (orderId) => ({
        url: `/orderitem/${orderId}`,
        method: 'DELETE',
      }),
      // Invalidate the cache after deletion so the UI updates
      invalidatesTags: ['posOrderItems'],
    }),

    onlineOrderItems: builder.query({
      query: (userId) => ({
        url: `/sub-orderitem/lastWeek`,
        params: { userId },
      }),
      transformResponse: (response) => response,
      providesTags: ['onlineOrderItems'],
    }),
    
     deleteOnlineOrder: builder.mutation({
      query: (orderId) => ({
        url: `/sub-orderitem/${orderId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['onlineOrderItems'],
    }),


  }),
});


export const { 
  usePosOrderItemsQuery,   
  useOnlineOrderItemsQuery,
  useDeleteOrderMutation,
   useDeleteOnlineOrderMutation
} = orderItemsApi;