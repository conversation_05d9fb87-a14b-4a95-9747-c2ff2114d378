// src/store/slices/orderItemsSlice.ts
import { createSlice } from '@reduxjs/toolkit';
import { orderItemsApi } from '../api/orderItemApi';
import type { RootState } from '../store';

interface OrderItemsState {
  posOrders: any[];
  isLoading: boolean;
  error: string | null;
}

const initialState: OrderItemsState = {
  posOrders: [],
  isLoading: false,
  error: null,
};

const orderItemsSlice = createSlice({
  name: 'orderItems',
  initialState,
  reducers: {
    setPosOrders: (state, action) => {
      state.posOrders = action.payload;
    },
    clearPosOrders: (state) => {
      state.posOrders = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle fetching POS Orders
      .addMatcher(
        orderItemsApi.endpoints.posOrderItems.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        orderItemsApi.endpoints.posOrderItems.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          state.posOrders = action.payload;
        }
      )
      .addMatcher(
        orderItemsApi.endpoints.posOrderItems.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to fetch POS orders';
        }
      )

      // Handle delete order mutation
      .addMatcher(
        orderItemsApi.endpoints.deleteOrder.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        orderItemsApi.endpoints.deleteOrder.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const deletedId = action.meta.arg.originalArgs;
          state.posOrders = state.posOrders.filter(
            (order) => order._id !== deletedId
          );
        }
      )
      .addMatcher(
        orderItemsApi.endpoints.deleteOrder.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to delete order';
        }
      );
  },
});

// Export actions
export const { setPosOrders, clearPosOrders } = orderItemsSlice.actions;

// Export selectors
export const selectPosOrders = (state: RootState) => state.posOrder.posOrders;
export const selectOrderItemsLoading = (state: RootState) => state.posOrder.isLoading;
export const selectOrderItemsError = (state: RootState) => state.posOrder.error;

// Export reducer
export default orderItemsSlice.reducer;
