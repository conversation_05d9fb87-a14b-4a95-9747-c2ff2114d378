import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import CustomModal from "../../CustomModal";
import { FiMinus, FiPlus } from "react-icons/fi";
import { applyItemDiscount } from "../../../store/slices/cartSlice";

interface ItemDiscountModalProps {
  isOpen: boolean;
  onClose: () => void;
  items: {
    id: string;
    name: string;
    price: number;
    image?: string;
    quantity: number;
    discountType?: string;
    discountAmount?: string;
    originalPrice?: number;
    note?: string;
  }[];
}

const ItemDiscountModal: React.FC<ItemDiscountModalProps> = ({
  isOpen,
  onClose,
  items
}) => {
  const dispatch = useDispatch();
  const [selectedItems, setSelectedItems] = useState<
    {
      id: string;
      name: string;
      oldPrice: number;
      newPrice: number;
      quantity: number;
      image?: string;
      discountType: string;
      discountAmount: string;
      discountApplied: boolean;
      note?: string;
    }[]
  >([]);

  // Update selected items when the modal opens or items change
  useEffect(() => {
    if (isOpen && items.length > 0) {
      setSelectedItems(
        items.map(item => ({
          id: item.id,
          name: item.name,
          oldPrice: item.originalPrice || item.price, // Use original price if available
          newPrice: item.price,
          quantity: item.quantity,
          image: item.image,
          discountType: item.discountType || "", // Preserve existing discount type
          discountAmount: item.discountAmount || "", // Preserve existing discount amount
          discountApplied: !!(item.discountType && item.discountAmount), // Mark as applied if both type and amount exist
          note: item.note || "" // Preserve existing note
        }))
      );
    }
  }, [isOpen, items]);

  // For direct price editing
  const [activeItemIndex, setActiveItemIndex] = useState<number | null>(null);
  const [inputValue, setInputValue] = useState<string>("");

  // For individual item discount editing
  const [activeDiscountIndex, setActiveDiscountIndex] = useState<number | null>(null);

  // For error message when discount type is not selected
  const [discountTypeError, setDiscountTypeError] = useState<string>("");

  const handleQuantityChange = (id: string, increment: boolean) => {
    setSelectedItems(prev =>
      prev.map(item =>
        item.id === id
          ? {
            ...item,
            quantity: increment
              ? item.quantity + 1
              : Math.max(1, item.quantity - 1)
          }
          : item
      )
    );
  };

  const handleApply = () => {
    selectedItems.forEach(item => {
      // Apply discount if there's a discount applied, or reset if discount was removed
      if (item.discountApplied && item.discountType && item.discountAmount) {
        dispatch(applyItemDiscount({
          id: item.id,
          newPrice: item.newPrice,
          discountType: item.discountType,
          discountAmount: item.discountAmount,
          note: item.note || "" // Use existing note if available
        }));
      } else if (!item.discountApplied && item.newPrice === item.oldPrice) {
        // If discount was removed, reset the item to original state
        dispatch(applyItemDiscount({
          id: item.id,
          newPrice: item.oldPrice,
          discountType: "",
          discountAmount: "",
          note: ""
        }));
      }
    });
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  const handleNumberClick = (num: string) => {
    // If we're in discount mode (editing an item's discount)
    if (activeDiscountIndex !== null) {
      if (num === "C") {
        // Clear the discount amount for this item
        setSelectedItems(prev =>
          prev.map((item, idx) =>
            idx === activeDiscountIndex
              ? { ...item, discountAmount: "" }
              : item
          )
        );
        return;
      }

      if (num === "×") {
        // Clear the discount and reset price to original
        setSelectedItems(prev =>
          prev.map((item, idx) =>
            idx === activeDiscountIndex
              ? {
                ...item,
                discountAmount: "",
                discountType: "",
                newPrice: item.oldPrice,
                discountApplied: false
              }
              : item
          )
        );
        setActiveDiscountIndex(null);
        return;
      }

      if (num === ".") {
        const currentItem = selectedItems[activeDiscountIndex];
        if (currentItem.discountAmount.includes(".")) return;

        // Check if discount type is selected
        if (!currentItem.discountType) {
          setDiscountTypeError("Please select a discount type first");
          return;
        }

        // Clear any previous error
        setDiscountTypeError("");

        const newValue = currentItem.discountAmount + ".";
        updateItemDiscount(activeDiscountIndex, currentItem.discountType, newValue);
        return;
      }

      // Handle special buttons for quick discount amounts
      if (num === "10" || num === "20") {
        const currentItem = selectedItems[activeDiscountIndex];

        // Check if discount type is selected
        if (!currentItem.discountType) {
          setDiscountTypeError("Please select a discount type first");
          return;
        }

        // Clear any previous error
        setDiscountTypeError("");

        updateItemDiscount(activeDiscountIndex, currentItem.discountType, num);
        return;
      }

      // Regular number input for discount amount
      const currentItem = selectedItems[activeDiscountIndex];

      // Check if discount type is selected
      if (!currentItem.discountType) {
        setDiscountTypeError("Please select a discount type first");
        return;
      }

      // Clear any previous error
      setDiscountTypeError("");

      const newValue = currentItem.discountAmount + num;
      updateItemDiscount(activeDiscountIndex, currentItem.discountType, newValue);
      return;
    }

    // If we have an active item selected for direct price editing
    if (activeItemIndex !== null) {
      if (num === "C") {
        // Clear the input
        setInputValue("");
        return;
      }

      if (num === "×") {
        // Clear the input and reset price to original
        setInputValue("");
        setSelectedItems(prev =>
          prev.map((item, idx) =>
            idx === activeItemIndex
              ? { ...item, newPrice: item.oldPrice }
              : item
          )
        );
        setActiveItemIndex(null);
        return;
      }

      if (num === ".") {
        if (inputValue.includes(".")) return;
        const newValue = inputValue + ".";
        setInputValue(newValue);
        updateItemPrice(newValue);
        return;
      }

      // Handle special buttons for quick amounts
      if (num === "10" || num === "20") {
        const newPrice = parseFloat(num);
        setInputValue(num);
        setSelectedItems(prev =>
          prev.map((item, idx) =>
            idx === activeItemIndex
              ? { ...item, newPrice }
              : item
          )
        );
        return;
      }

      // Regular number input
      const newValue = inputValue + num;
      setInputValue(newValue);
      updateItemPrice(newValue);
      return;
    }

    // If no specific item is selected, do nothing
    // This is a fallback for when neither activeItemIndex nor activeDiscountIndex is set
    return;
  };

  const updateItemPrice = (value: string) => {
    if (activeItemIndex === null) return;

    const newPrice = value === "" ? 0 : parseFloat(value);
    if (isNaN(newPrice)) return;

    setSelectedItems(prev =>
      prev.map((item, idx) =>
        idx === activeItemIndex
          ? { ...item, newPrice }
          : item
      )
    );
  };

  const handleInputFocus = (index: number) => {
    setActiveItemIndex(index);
    setInputValue("");
  };

  // Function to update an individual item's discount
  const updateItemDiscount = (itemIndex: number, discountType: string, discountAmount: string) => {
    if (itemIndex === null || itemIndex < 0 || itemIndex >= selectedItems.length) return;

    setSelectedItems(prev => {
      const updatedItems = [...prev];
      const item = updatedItems[itemIndex];

      // Update the discount amount
      updatedItems[itemIndex] = {
        ...item,
        discountAmount: discountAmount
      };

      // Calculate the new price if both discount type and amount are provided
      if (discountType && discountAmount) {
        const numericValue = parseFloat(discountAmount);
        if (!isNaN(numericValue)) {
          let newPrice = item.oldPrice;

          if (discountType === "percentage") {
            // Limit percentage discount to 100%
            const limitedPercentage = Math.min(100, numericValue);

            // Apply percentage discount
            newPrice = item.oldPrice * (1 - limitedPercentage / 100);

            // If user entered more than 100%, update the discount amount to show 100%
            if (numericValue > 100) {
              updatedItems[itemIndex] = {
                ...updatedItems[itemIndex],
                discountAmount: "100"
              };
            }
          } else if (discountType === "fixed") {
            // Limit fixed discount to the item price
            const limitedFixedAmount = Math.min(item.oldPrice, numericValue);

            // Apply fixed amount discount
            newPrice = item.oldPrice - limitedFixedAmount;

            // If user entered more than the item price, update the discount amount
            if (numericValue > item.oldPrice) {
              updatedItems[itemIndex] = {
                ...updatedItems[itemIndex],
                discountAmount: item.oldPrice.toString()
              };
            }
          }

          // Ensure price is never negative
          newPrice = Math.max(0, newPrice);

          // Round to 2 decimal places
          newPrice = Math.round(newPrice * 100) / 100;

          // Update the item with the new price and mark discount as applied
          updatedItems[itemIndex] = {
            ...updatedItems[itemIndex],
            newPrice: newPrice,
            discountApplied: true
          };
        }
      }

      return updatedItems;
    });
  };

  // Function to update the discount type for an individual item
  const updateItemDiscountType = (itemIndex: number, discountType: string) => {
    if (itemIndex === null || itemIndex < 0 || itemIndex >= selectedItems.length) return;

    // Clear any error message when user selects a discount type
    setDiscountTypeError("");

    setSelectedItems(prev => {
      const updatedItems = [...prev];
      const item = updatedItems[itemIndex];

      // If discount type is cleared (empty string), reset everything
      if (!discountType) {
        updatedItems[itemIndex] = {
          ...item,
          discountType: "",
          discountAmount: "",
          newPrice: item.oldPrice,
          discountApplied: false
        };
        return updatedItems;
      }

      // Update the discount type
      updatedItems[itemIndex] = {
        ...item,
        discountType: discountType
      };

      // Recalculate the price if a discount amount is already set
      if (item.discountAmount) {
        const numericValue = parseFloat(item.discountAmount);
        if (!isNaN(numericValue)) {
          let newPrice = item.oldPrice;
          let updatedDiscountAmount = item.discountAmount;

          if (discountType === "percentage") {
            // Limit percentage discount to 100%
            const limitedPercentage = Math.min(100, numericValue);

            // Apply percentage discount
            newPrice = item.oldPrice * (1 - limitedPercentage / 100);

            // If user entered more than 100%, update the discount amount to show 100%
            if (numericValue > 100) {
              updatedDiscountAmount = "100";
            }
          } else if (discountType === "fixed") {
            // Limit fixed discount to the item price
            const limitedFixedAmount = Math.min(item.oldPrice, numericValue);

            // Apply fixed amount discount
            newPrice = item.oldPrice - limitedFixedAmount;

            // If user entered more than the item price, update the discount amount
            if (numericValue > item.oldPrice) {
              updatedDiscountAmount = item.oldPrice.toString();
            }
          }

          // Ensure price is never negative
          newPrice = Math.max(0, newPrice);

          // Round to 2 decimal places
          newPrice = Math.round(newPrice * 100) / 100;

          // Update the item with the new price and mark discount as applied
          updatedItems[itemIndex] = {
            ...updatedItems[itemIndex],
            discountAmount: updatedDiscountAmount,
            newPrice: newPrice,
            discountApplied: true
          };
        }
      }

      return updatedItems;
    });
  };

  // No global discount function needed anymore as we use individual item discounts

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Item Discount"
      width="max-w-6xl"
      zIndex={100}
    >
      <div className="flex flex-col h-full">
        {/* Title section */}


        {/* Active discount indicator */}
        {activeDiscountIndex !== null && (
          <div className="bg-blue-50 p-3 border-b border-blue-200">
            <p className="text-blue-700 font-medium">
              Editing discount for: {selectedItems[activeDiscountIndex]?.name}
            </p>
            <p className="text-sm text-blue-600">
              Use the numpad to enter the discount amount
            </p>
          </div>
        )}

        {/* Error message */}
        {discountTypeError && (
          <div className="bg-red-50 p-3 border-b border-red-200">
            <p className="text-red-600 font-medium">
              <span className="mr-2">⚠️</span>
              {discountTypeError}
            </p>
          </div>
        )}

        {/* Main content area with side-by-side layout */}
        <div className="flex flex-1 h-full">
          {/* Left side - Scrollable items list */}
          <div className="w-3/5 border-r border-gray-200 overflow-y-auto" style={{ height: "calc(100vh - 300px)" }}>
            {selectedItems.map((item, index) => (
              <div
                key={item.id}
                className={`p-4 border-b border-gray-200 ${activeDiscountIndex === index ? 'bg-blue-50' : ''}`}
              >
                <div className="flex">
                  <div className="w-16 h-16 mr-4">
                    {item.image && (
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-full h-full object-cover rounded"
                      />
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-lg mb-1">
                      {item.name}
                    </h3>
                    <div className="flex space-x-4 mb-3">
                      <div>
                        <div className="text-gray-500 text-sm">Old Price</div>
                        <div className="font-medium text-lg text-orange-500">
                          ${item.oldPrice.toFixed(2)}
                        </div>
                      </div>
                      <div>
                        <div className="text-gray-500 text-sm">New Price</div>
                        <div
                          className="font-medium text-lg cursor-pointer"
                          onClick={() => handleInputFocus(index)}
                        >
                          ${activeItemIndex === index && inputValue
                            ? inputValue
                            : item.newPrice.toFixed(2)}
                        </div>
                      </div>
                      <div className="flex items-center ml-auto">
                        <button
                          className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center"
                          onClick={() => handleQuantityChange(item.id, false)}
                        >
                          <FiMinus />
                        </button>
                        <span className="mx-3 w-4 text-center">{item.quantity}</span>
                        <button
                          className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center"
                          onClick={() => handleQuantityChange(item.id, true)}
                        >
                          <FiPlus />
                        </button>
                      </div>
                    </div>

                    {/* Individual item discount section */}
                    <div className="flex space-x-4 mt-2">
                      <div className="w-1/2">
                        <div className="text-sm text-gray-500 mb-1">Discount Type</div>
                        <select
                          className={`w-full border border-gray-300 rounded p-2 bg-white ${activeDiscountIndex === index ? 'border-blue-500 ring-2 ring-blue-200' : ''}`}
                          value={item.discountType}
                          onChange={(e) => updateItemDiscountType(index, e.target.value)}
                        >
                          <option value="">Select Type</option>
                          <option value="percentage">Percentage</option>
                          <option value="fixed">Fixed Amount</option>
                        </select>
                      </div>
                      <div className="w-1/2">
                        <div className="text-sm text-gray-500 mb-1">Discount Amount</div>
                        <div
                          className={`w-full border border-gray-300 rounded p-2 cursor-pointer ${activeDiscountIndex === index ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200 font-medium' : ''
                            }`}
                          onClick={() => {
                            setActiveDiscountIndex(index);
                            setDiscountTypeError(""); // Clear any error message
                          }}
                        >
                          {item.discountAmount || "0"}
                          {item.discountType === "percentage" && item.discountAmount && "%"}
                          {item.discountType === "fixed" && item.discountAmount && "$"}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Right side - Calculator */}
          <div className="w-2/5 flex flex-col">
            {/* Numpad section */}
            <div className="p-6 flex-1 flex flex-col justify-center">
              <div className="grid grid-cols-4 gap-4">
                <button
                  className="bg-gray-100 rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => handleNumberClick("1")}
                >
                  1
                </button>
                <button
                  className="bg-gray-100 rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => handleNumberClick("2")}
                >
                  2
                </button>
                <button
                  className="bg-gray-100 rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => handleNumberClick("3")}
                >
                  3
                </button>
                <button
                  className="bg-blue-100 rounded-lg py-5 text-2xl font-semibold text-blue-500"
                  onClick={() => handleNumberClick("10")}
                >
                  10
                </button>
                <button
                  className="bg-gray-100 rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => handleNumberClick("4")}
                >
                  4
                </button>
                <button
                  className="bg-gray-100 rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => handleNumberClick("5")}
                >
                  5
                </button>
                <button
                  className="bg-gray-100 rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => handleNumberClick("6")}
                >
                  6
                </button>
                <button
                  className="bg-blue-100 rounded-lg py-5 text-2xl font-semibold text-blue-500"
                  onClick={() => handleNumberClick("20")}
                >
                  20
                </button>
                <button
                  className="bg-gray-100 rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => handleNumberClick("7")}
                >
                  7
                </button>
                <button
                  className="bg-gray-100 rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => handleNumberClick("8")}
                >
                  8
                </button>
                <button
                  className="bg-gray-100 rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => handleNumberClick("9")}
                >
                  9
                </button>
                <button
                  className="bg-red-100 rounded-lg py-5 text-2xl font-semibold text-red-500"
                  onClick={() => handleNumberClick("×")}
                >
                  ×
                </button>
                <button
                  className="bg-orange-100 rounded-lg py-5 text-2xl font-semibold text-orange-500"
                  onClick={() => handleNumberClick("C")}
                >
                  C
                </button>
                <button
                  className="bg-gray-100 rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => handleNumberClick("0")}
                >
                  0
                </button>
                <button
                  className="bg-gray-100 rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => handleNumberClick(".")}
                >
                  .
                </button>
                <button
                  className="bg-blue-500 text-white rounded-lg py-5 text-2xl font-semibold"
                  onClick={() => {
                    // Apply the discount to the currently selected item
                    if (activeDiscountIndex !== null) {
                      const item = selectedItems[activeDiscountIndex];
                      if (item.discountType && item.discountAmount) {
                        // Discount is already being calculated in updateItemDiscount
                        // Just clear the active discount index to finish editing
                        setActiveDiscountIndex(null);
                      }
                    }
                  }}
                >
                  Add
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Buttons */}
        <div className="flex p-4 border-t border-gray-200 bg-white mt-auto">
          <button
            onClick={handleCancel}
            className="w-1/2 mr-2 py-3 border border-orange-500 text-orange-500 rounded-full"
          >
            Cancel
          </button>
          <button
            onClick={handleApply}
            className="w-1/2 ml-2 py-3 bg-orange-500 text-white rounded-full"
          >
            Apply
          </button>
        </div>
      </div>
    </CustomModal>
  );
};

export default ItemDiscountModal;