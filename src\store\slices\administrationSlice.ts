import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { Administration } from '../api/administrationApi';
import { administrationApi } from '../api/administrationApi';

interface AdministrationState {
  administrations: Administration[];
  isLoading: boolean;
  error: string | null;
}

const initialState: AdministrationState = {
  administrations: [],
  isLoading: false,
  error: null,
};

const administrationSlice = createSlice({
  name: 'administration',
  initialState,
  reducers: {
    setAdministrations: (state, action: PayloadAction<Administration[]>) => {
      state.administrations = action.payload;
    },
    clearAdministrations: (state) => {
      state.administrations = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET Administrations List
      .addMatcher(
        administrationApi.endpoints.getAdministrations.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        administrationApi.endpoints.getAdministrations.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.administrations = payload;
        }
      )
      .addMatcher(
        administrationApi.endpoints.getAdministrations.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch administrations';
        }
      )

      // DELETE Administration
      .addMatcher(
        administrationApi.endpoints.deleteAdministration.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        administrationApi.endpoints.deleteAdministration.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const administrationId = action.meta.arg.originalArgs as string;
          state.administrations = state.administrations.filter(
            (administration) => administration.id !== administrationId
          );
        }
      )
      .addMatcher(
        administrationApi.endpoints.deleteAdministration.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete administration';
        }
      )

      // POST Administration
      .addMatcher(
        administrationApi.endpoints.postAdministration.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        administrationApi.endpoints.postAdministration.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          // Only push if we have valid data
          if (payload && payload._id) {
            state.administrations.push({
              id: payload._id,
              name: payload.name,
              active: payload.active,
              Line1: payload.Line1,
              Line2: payload.Line2,
              City: payload.City,
              Phoneno: payload.Phoneno,
              State: payload.State,
              PostalCode: payload.PostalCode,
              Country: payload.Country,
              image: payload.image,
              businessType: payload.businessType,
              delivery: payload.delivery,
              deliveryStartTime: payload.deliveryStartTime,
              deliveryEndTime: payload.deliveryEndTime,
              ChargesperKm: payload.ChargesperKm,
              ChargesFreeKm: payload.ChargesFreeKm,
              pickupStartTime: payload.pickupStartTime,
              pickupEndTime: payload.pickupEndTime,
              userId: payload.userId,
              createdDate: payload.createdDate,
              reviews: payload.reviews || [],
              favorites: payload.favorites || [],
            });
          }
        }
      )
      .addMatcher(
        administrationApi.endpoints.postAdministration.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create administration';
        }
      )

      // PUT Administration
      .addMatcher(
        administrationApi.endpoints.putAdministration.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        administrationApi.endpoints.putAdministration.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          
          // Handle the case where the payload contains the administration data
          if (payload && payload._id) {
            const updated = {
              id: payload._id,
              name: payload.name,
              active: payload.active,
              Line1: payload.Line1,
              Line2: payload.Line2,
              City: payload.City,
              Phoneno: payload.Phoneno,
              State: payload.State,
              PostalCode: payload.PostalCode,
              Country: payload.Country,
              image: payload.image,
              businessType: payload.businessType,
              delivery: payload.delivery,
              deliveryStartTime: payload.deliveryStartTime,
              deliveryEndTime: payload.deliveryEndTime,
              ChargesperKm: payload.ChargesperKm,
              ChargesFreeKm: payload.ChargesFreeKm,
              pickupStartTime: payload.pickupStartTime,
              pickupEndTime: payload.pickupEndTime,
              userId: payload.userId,
              createdDate: payload.createdDate,
              reviews: payload.reviews || [],
              favorites: payload.favorites || [],
            };
            state.administrations = state.administrations.map((administration) =>
              administration.id === updated.id ? updated : administration
            );
          } else if (payload && payload.data && payload.data._id) {
            // Handle the case where payload.data contains the administration data
            const updated = {
              id: payload.data._id,
              name: payload.data.name,
              active: payload.data.active,
              Line1: payload.data.Line1,
              Line2: payload.data.Line2,
              City: payload.data.City,
              Phoneno: payload.data.Phoneno,
              State: payload.data.State,
              PostalCode: payload.data.PostalCode,
              Country: payload.data.Country,
              image: payload.data.image,
              businessType: payload.data.businessType,
              delivery: payload.data.delivery,
              deliveryStartTime: payload.data.deliveryStartTime,
              deliveryEndTime: payload.data.deliveryEndTime,
              ChargesperKm: payload.data.ChargesperKm,
              ChargesFreeKm: payload.data.ChargesFreeKm,
              pickupStartTime: payload.data.pickupStartTime,
              pickupEndTime: payload.data.pickupEndTime,
              userId: payload.data.userId,
              createdDate: payload.data.createdDate,
              reviews: payload.data.reviews || [],
              favorites: payload.data.favorites || [],
            };
            state.administrations = state.administrations.map((administration) =>
              administration.id === updated.id ? updated : administration
            );
          } else {
            // If we get here, the payload structure is not as expected
            console.error('Invalid payload structure for administration update:', payload);
          }
        }
      )
      .addMatcher(
        administrationApi.endpoints.putAdministration.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update administration';
        }
      )
      
      // POST Review
      .addMatcher(
        administrationApi.endpoints.postReview.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        administrationApi.endpoints.postReview.matchFulfilled,
        (state) => {
          state.isLoading = false;
          // Since this will cause a refetch of the affected administration,
          // we don't need to manually update the state here
        }
      )
      .addMatcher(
        administrationApi.endpoints.postReview.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to post review';
        }
      )
      
      // Add Favorite
      .addMatcher(
        administrationApi.endpoints.addFavorite.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        administrationApi.endpoints.addFavorite.matchFulfilled,
        (state) => {
          state.isLoading = false;
          // Since this will cause a refetch of the data,
          // we don't need to manually update the state here
        }
      )
      .addMatcher(
        administrationApi.endpoints.addFavorite.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to add favorite';
        }
      )
      
      // Get Patronpal Devices
      .addMatcher(
        administrationApi.endpoints.getPatronpalDevices.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        administrationApi.endpoints.getPatronpalDevices.matchFulfilled,
        (state, { }) => {
          state.isLoading = false;
          // This could potentially update the state with patronpal devices,
          // but since it's a separate query, it might be better to handle it separately
          // if needed, you could add a patronpalDevices field to the state
        }
      )
      .addMatcher(
        administrationApi.endpoints.getPatronpalDevices.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch patronpal devices';
        }
      );
  },
});

export const { setAdministrations, clearAdministrations } = administrationSlice.actions;

export const selectAdministrations = (state: RootState) => state.administration.administrations;
export const selectAdministrationLoading = (state: RootState) => state.administration.isLoading;
export const selectAdministrationError = (state: RootState) => state.administration.error;

export default administrationSlice.reducer;