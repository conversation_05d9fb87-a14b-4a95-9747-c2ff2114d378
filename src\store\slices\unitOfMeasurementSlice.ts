import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type {UnitOfMeasurement } from '../api/unitOfMeasurementApi';
import { unitOfMeasurementApi } from '../api/unitOfMeasurementApi';

interface UnitOfMeasurementState {
  unitOfMeasurements: UnitOfMeasurement[];
  isLoading: boolean;
  error: string | null;
}

const initialState: UnitOfMeasurementState = {
  unitOfMeasurements: [],
  isLoading: false,
  error: null,
};

const unitOfMeasurementSlice = createSlice({
  name: 'unitOfMeasurement',
  initialState,
  reducers: {
    setUnitOfMeasurements: (state, action: PayloadAction<UnitOfMeasurement[]>) => {
      state.unitOfMeasurements = action.payload;
    },
    clearUnitOfMeasurements: (state) => {
      state.unitOfMeasurements = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET all units of measurement
      .addMatcher(
        unitOfMeasurementApi.endpoints.getUnitOfMeasurements.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        unitOfMeasurementApi.endpoints.getUnitOfMeasurements.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.unitOfMeasurements = payload;
        }
      )
      .addMatcher(
        unitOfMeasurementApi.endpoints.getUnitOfMeasurements.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch units of measurement';
        }
      )

      // DELETE unit of measurement
      .addMatcher(
        unitOfMeasurementApi.endpoints.deleteUnitOfMeasurement.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        unitOfMeasurementApi.endpoints.deleteUnitOfMeasurement.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const unitId = action.meta.arg.originalArgs as string;
          state.unitOfMeasurements = state.unitOfMeasurements.filter(
            (unit) => unit.id !== unitId
          );
        }
      )
      .addMatcher(
        unitOfMeasurementApi.endpoints.deleteUnitOfMeasurement.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete unit of measurement';
        }
      )

      // POST unit of measurement
      .addMatcher(
        unitOfMeasurementApi.endpoints.postUnitOfMeasurement.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        unitOfMeasurementApi.endpoints.postUnitOfMeasurement.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          const newUnit: UnitOfMeasurement = {
            id: payload._id,
            name: payload.name,
            code: payload.code,
            userId: payload.userId,
            active: payload.active,
            value: payload.value
          };
          state.unitOfMeasurements.push(newUnit);
        }
      )
      .addMatcher(
        unitOfMeasurementApi.endpoints.postUnitOfMeasurement.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create unit of measurement';
        }
      )

      // PUT unit of measurement
      .addMatcher(
        unitOfMeasurementApi.endpoints.putUnitOfMeasurement.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        unitOfMeasurementApi.endpoints.putUnitOfMeasurement.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          const updated: UnitOfMeasurement = {
            id: payload.data._id,
            name: payload.data.name,
            code: payload.data.code,
            userId: payload.data.userId,
            active: payload.data.active,
            value: payload.data.value
          };
          state.unitOfMeasurements = state.unitOfMeasurements.map((unit) =>
            unit.id === updated.id ? updated : unit
          );
        }
      )
      .addMatcher(
        unitOfMeasurementApi.endpoints.putUnitOfMeasurement.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update unit of measurement';
        }
      );
  },
});

export const { setUnitOfMeasurements, clearUnitOfMeasurements } = unitOfMeasurementSlice.actions;

export const selectUnitOfMeasurements = (state: RootState) => state.unitOfMeasurement.unitOfMeasurements;
export const selectUnitOfMeasurementLoading = (state: RootState) => state.unitOfMeasurement.isLoading;
export const selectUnitOfMeasurementError = (state: RootState) => state.unitOfMeasurement.error;

export default unitOfMeasurementSlice.reducer;