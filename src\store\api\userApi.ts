import { baseApi } from "./baseApi";

// Define interfaces for request and response types
export interface LoginRequest {
  email: string;
  password: string;
}



export interface LoginResponse {
  message: string;
  token: string;
  userId: { _id: string };
  role: string;
  loginDate: string;
  email: string;
  appFee: number;
  name: {
    _id: string;
    name: string;
    email: string;
    password: string;
    role: string;
    isActive: boolean;
    userId: string;
    createdDate: string;
    createdAt: string;
    updatedAt: string;
    appFee: number;
    monthlySurchargePayments: Array<{
      month: number;
      year: number;
      paid: boolean;
      amount: number;
      _id: string;
    }>;
    stripe_account_id?: string;
    stripe_acess_token?: string;
    stripe_refresh_token?: string;
    google: boolean;
    surChargeThreshold?: number;
  };
}

export const userApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation<LoginResponse, LoginRequest>({
      query: (credentials) => ({
        url: "/login",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["User"],
    }),
  }),
  overrideExisting: false,
});

export const { useLoginMutation } = userApi;

