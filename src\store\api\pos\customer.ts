import { baseApi } from "../baseApi";

// Define the Customer interface
export interface Customer {
    CustomerLoyalty: any;
    id: string;
    firstName: string;
    lastName: string;
    address: string;
    city: string;
    state: string;
    email: string;
    phone: string;
    cardNo?: string;
    startDate?: string;
    points?: number;
    visits?: number;
    lastVisit?: string;
}

export interface CustomerOrderItem {
    receiptNumber: string;
    operatorName: string;
    customer: string;
    points: number;
    recordDate: string;
    tax: number;
    amount: number;
    receivedAmount: number;
    dueAmount: number;
    date: string;
}

// Define the AggregatedOrdersResponse interface to match the actual API response
export interface AggregatedOrdersResponse {
    combinedProducts: {
        name: string;
        price: number;
        productId: string;
        qty: number;
        remainingqty: number;
        unitPrice: number;
    }[];
    totalPrice: number;
}

// Define the Deduction interface
interface Deduction {
    amount: number;
    name: string;
    _id: string;
}

// Define the EmployeeRecord interface
export interface EmployeeRecord {
    _id: string;
    userId: string;
    employeeId: string;
    firstName: string;
    lastName: string;
    empName: string;
    address: string;
    email: string;
    phoneNo: string;
    employeeType: string;
    role: string;
    hourlyRate: number;
    overTimeRate: number;
    employeeStartTime: string;
    employeeEndTime: string;
    startDate: string;
    totalHours: number;
    deductions: Deduction[];
    allowancesHistory: any[];
    bonusesHistory: any[];
    createdAt: string;
    updatedAt: string;
    __v: number;

    // Calculated fields
    shiftHours?: string;
    shiftTotalAmount?: number;
    month?: string;
    startHourlyRate?: number;
    total: number;
    overtimeHours?: string;
    overtimeHourlyRate?: number;
    overtimeTotalAmount?: number;
    totalWork?: string;
    subtotal?: number;
    deductionAmount?: number;
}

// Create the API endpoints
export const customerApi = baseApi.injectEndpoints({
    endpoints: (builder) => ({
        getCustomers: builder.query<Customer[], string>({
            query: (userId) => `/customer?userId=${userId}`,
            providesTags: ["Customer"],
            transformResponse: (response: any[]) => {
                return response.map((item) => ({
                    ...item,
                    id: item._id,
                    firstName: item.FirstName,
                    lastName: item.LastName,
                    address: item.Address,
                    city: item.City,
                    state: item.State,
                    email: item.Email,
                    phone: item.Phone,
                    cardNo: item.CardNo,
                    startDate: item.StartDate,
                    points: item.Points,
                    visits: item.Visits,
                    lastVisit: item.LastVisit,
                }));
            },
        }),
        addCustomer: builder.mutation<any, Partial<Customer>>({
            query: (customerData) => ({
                url: '/customer',
                method: 'POST',
                body: customerData,
            }),
            invalidatesTags: ["pos"],
        }),
        updateCustomer: builder.mutation<any, { _id: string; data: Partial<any> }>({
            query: ({ _id, data }) => ({
                url: `/customer/${_id}`,
                method: 'PUT',
                body: data,
            }),
            invalidatesTags: ["Customer"],
        }),

        getCustomerOrderItems: builder.query<CustomerOrderItem[], string>({
            query: (userId) => `/customerOrderitem?userId=${userId}`,
            providesTags: ["CustomerOrderItem"],
            transformResponse: (response: any[]) => {
                return response.map((item) => ({
                    ...item,
                    receiptNumber: item.recieptId?.recieptNo?.toString() || item.OrderNumber || 'N/A',
                    operatorName: item.orderId?.operator || 'N/A',
                    customer: `${item.customerId?.FirstName || ''} ${item.customerId?.LastName || ''}`.trim() || 'N/A',
                    points: item.customerId?.CustomerLoyalty?.Points || 0,
                    recordDate: item.createdAt || item.orderId?.orderDate || 'N/A',
                    tax: Array.isArray(item.tax) ? item.tax.reduce((sum: number, t: any) => sum + (t.addtax || 0), 0) : (item.lineValueTax || 0),
                    amount: item.recieptId?.total || item.grandTotal || 0,
                    receivedAmount: item.recieptId?.total || item.grandTotal || 0,
                    dueAmount: item.dueamount || 0,
                    date: item.createdAt || item.orderId?.orderDate || 'N/A'
                }));
            },
        }),

        getEmployeeTime: builder.query<any[], string>({
            query: (userId) => `/employeTime?userId=${userId}`,
            providesTags: ["EmployeeTime"],
            transformResponse: (response: any[]) => response,
        }),

        getAggregatedOrders: builder.query<AggregatedOrdersResponse, { userId: string; reporttype: string }>({
            query: ({ userId, reporttype }) =>
                `/aggregated-orders?userId=${userId}&reportType=${reporttype}`,
            providesTags: ["AggregatedOrders"],
            transformResponse: (response: any) => response,
        }),

        getLastMonthOrders: builder.query<any[], string>({
            query: (userId) => `orderitem/lastmonth?userId=${userId}`,
            providesTags: ["LastMonthOrders"],
            transformResponse: (response: any[]) => response,
        }),

        getAggregatedOrdersByDate: builder.query<AggregatedOrdersResponse, { userId: string; startDate: string; endDate: string }>({
            query: ({ userId, startDate, endDate }) =>
                `/aggregated-orders?userId=${encodeURIComponent(userId)}&reportType=date&startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`,
            providesTags: ["AggregatedOrdersByDate"],
            transformResponse: (response: any) => response,
        }),

        getEmployees: builder.query<EmployeeRecord[], string>({
            query: (userId) => `/employee?userId=${userId}`,
            providesTags: ["Employees"],
            transformResponse: (response: any[]) => response,
        }),

        // New endpoint added here
        getBillDenomination: builder.query<any[], string>({
            query: (userId) => `/billdenomination?userId=${userId}`,
            providesTags: ["pos"],
            transformResponse: (response: any[]) => response,
        }),
        updateBillDenomination: builder.mutation<any, { _id: string; data: Partial<any> }>({
            query: ({ _id, data }) => ({
                url: `/billdenomination/${_id}`,
                method: 'PUT',
                body: data,
            }),
            invalidatesTags: ["pos"], // Invalidate to refresh relevant data
        }),
        deleteBillDenomination: builder.mutation<any, string>({
            query: (id) => ({
                url: `/billdenomination/${id}`,
                method: 'DELETE',
            }),
            invalidatesTags: ["pos"], // Invalidate related cache to ensure UI refresh
        }),

    }),
    overrideExisting: false,
});



// Export the generated hooks
export const {
    useGetCustomersQuery,
    useGetCustomerOrderItemsQuery,
    useGetAggregatedOrdersByDateQuery, // Fixed hook name to match endpoint name
    useGetEmployeeTimeQuery,
    useGetAggregatedOrdersQuery,
    useGetLastMonthOrdersQuery,
    useGetEmployeesQuery,
    useAddCustomerMutation,
    useUpdateCustomerMutation,
    useGetBillDenominationQuery,
    useUpdateBillDenominationMutation,
    useDeleteBillDenominationMutation,
} = customerApi;