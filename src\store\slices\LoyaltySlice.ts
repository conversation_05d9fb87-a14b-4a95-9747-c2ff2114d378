import { createSlice } from '@reduxjs/toolkit';
// import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import { loyaltyApi } from '../api/loyaltyOfferAPi';

export interface LoyaltyOfferItem {
  _id: string;
  productName: {
    name: string;
  };
  offerQty: number | string;
  description: string;
  active: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface LoyaltyOfferState {
  loyaltyOffers: LoyaltyOfferItem[];
  isLoading: boolean;
  error: string | null;
}

const initialState: LoyaltyOfferState = {
  loyaltyOffers: [],
  isLoading: false,
  error: null,
};

const loyaltySlice = createSlice({
  name: 'loyaltyOffers',
  initialState,
  reducers: {
    // Local reducers for manual operations if needed
    setLoyaltyOffers: (state, action) => {
      state.loyaltyOffers = action.payload;
    },
    clearLoyaltyOffers: (state) => {
      state.loyaltyOffers = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle getLoyaltyOffers query
      .addMatcher(
        loyaltyApi.endpoints.getLoyaltyOffers.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        loyaltyApi.endpoints.getLoyaltyOffers.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          state.loyaltyOffers = action.payload;
        }
      )
      .addMatcher(
        loyaltyApi.endpoints.getLoyaltyOffers.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to fetch loyalty offers';
        }
      )
      
      // Handle createLoyaltyOffer mutation
      .addMatcher(
        loyaltyApi.endpoints.createLoyaltyOffer.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        loyaltyApi.endpoints.createLoyaltyOffer.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          state.loyaltyOffers.push(action.payload);
        }
      )
      .addMatcher(
        loyaltyApi.endpoints.createLoyaltyOffer.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to create loyalty offer';
        }
      )
      
      // Handle updateLoyaltyOffer mutation
      .addMatcher(
        loyaltyApi.endpoints.updateLoyaltyOffer.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        loyaltyApi.endpoints.updateLoyaltyOffer.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          // Update the modified offer
          const updatedOffer = action.payload;
          const index = state.loyaltyOffers.findIndex(
            (offer) => offer._id === updatedOffer._id
          );
          if (index !== -1) {
            state.loyaltyOffers[index] = updatedOffer;
          }
        }
      )
      .addMatcher(
        loyaltyApi.endpoints.updateLoyaltyOffer.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to update loyalty offer';
        }
      )
      
      // Handle deleteLoyaltyOffer mutation
      .addMatcher(
        loyaltyApi.endpoints.deleteLoyaltyOffer.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        loyaltyApi.endpoints.deleteLoyaltyOffer.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          // Filter out the deleted offer
          const deletedId = action.meta.arg.originalArgs;
          state.loyaltyOffers = state.loyaltyOffers.filter(
            (offer) => offer._id !== deletedId
          );
        }
      )
      .addMatcher(
        loyaltyApi.endpoints.deleteLoyaltyOffer.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to delete loyalty offer';
        }
      );
  },
});

// Export actions
export const { setLoyaltyOffers, clearLoyaltyOffers } = loyaltySlice.actions;

// Export selectors
export const selectLoyaltyOffers = (state: RootState) => state.loyaltyOffers.loyaltyOffers;
export const selectLoyaltyOffersLoading = (state: RootState) => state.loyaltyOffers.isLoading;
export const selectLoyaltyOffersError = (state: RootState) => state.loyaltyOffers.error;

// Export reducer
export default loyaltySlice.reducer;