import { baseApi } from "./baseApi";

export interface Employee {
  id: string;
  userName: string;
  firstName: string;
  lastName: string;
  email: string;
  employeeId: string;
  userId: string;
  role: string;
  hourlyRate: number;
  employeeType: string;
  overTimeRate: number;
  totalHours: number;
  phoneNo: string;
  address: string;
  employeeStartTime?: string;
  employeeEndTime?: string;
  deductions?: Array<{
    date: string;
    name: string;
    type: string;
    amount: number;
    description?: string;
  }>;
}

export const employeeListApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getEmployeesList: builder.query<Employee[], string>({
      query: (userId) => `/employee?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          userName: item.userName,
          firstName: item.firstName,
          lastName: item.lastName,
          email: item.email,
          employeeId: item.employeeId,
          userId: item.userId,
          role: item.role,
          hourlyRate: item.hourlyRate,
          employeeType: item.employeeType,
          overTimeRate: item.overTimeRate,
          totalHours: item.totalHours,
          phoneNo: item.phoneNo,
          address: item.address,
          employeeStartTime: item.employeeStartTime,
          employeeEndTime: item.employeeEndTime,
          deductions: item.deductions || [],
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({
                type: "EmployeeList" as const,
                id,
              })),
              { type: "EmployeeList", id: "LIST" },
            ]
          : [{ type: "EmployeeList", id: "LIST" }],
    }),

    getEmployeeList: builder.query<Employee, string>({
      query: (id) => `/employee/${id}`,
      transformResponse: (response: any) => ({
        id: response._id,
        userName: response.userName,
        firstName: response.firstName,
        lastName: response.lastName,
        email: response.email,
        employeeId: response.employeeId,
        userId: response.userId,
        role: response.role,
        hourlyRate: response.hourlyRate,
        employeeType: response.employeeType,
        overTimeRate: response.overTimeRate,
        totalHours: response.totalHours,
        phoneNo: response.phoneNo,
        address: response.address,
        employeeStartTime: response.employeeStartTime,
        employeeEndTime: response.employeeEndTime,
        deductions: response.deductions || [],
      }),
      providesTags: (_result, _error, id) => [{ type: "EmployeeList", id }],
    }),

    deleteEmployeeList: builder.mutation<void, string>({
      query: (id) => ({
        url: `/employee/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            employeeListApi.util.invalidateTags([{ type: "EmployeeList", id }])
          );
        } catch (error) {
          console.error("Failed to delete employee", error);
        }
      },
      invalidatesTags: [{ type: "EmployeeList", id: "LIST" }],
    }),

    postEmployeeList: builder.mutation<any, any>({
      query: (data) => {
        return {
          url: "/employee",
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: [{ type: "EmployeeList", id: "LIST" }],
    }),

    putEmployeeList: builder.mutation<any, { id: string; data: any }>({
      query: ({ id, data }) => ({
        url: `/employee/${id}`,
        method: "PUT",
        body: data,
      }),
      transformResponse: (response: any) => {
        return response;
      },
      invalidatesTags: (_result, _error, { id }) => [
    { type: "EmployeeList", id },      
    { type: "EmployeeList", id: "LIST" }, 
    { type: "Deductions", id: "LIST" }   
  ],
    }),

    // Employee deduction endpoints
    getDeductions: builder.query<any[], void>({
      query: () => `/deductions`,
      providesTags: [{ type: "Deductions", id: "LIST" }],
    }),

    getDeductionById: builder.query<any, string>({
      query: (id) => `/deductions/${id}`,
      providesTags: (_result, _error, id) => [{ type: "Deductions", id }],
    }),

    createDeduction: builder.mutation<any, any>({
      query: (data) => ({
        url: "/deductions",
        method: "POST",
        body: data,
      }),
      invalidatesTags: [{ type: "Deductions", id: "LIST" }],
    }),

    updateDeduction: builder.mutation<any, { id: string; data: any }>({
      query: ({ id, data }) => ({
        url: `/deductions/${id}`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: [{ type: "Deductions", id: "LIST" }],
    }),

    deleteDeduction: builder.mutation<void, string>({
      query: (id) => ({
        url: `/deductions/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: [{ type: "Deductions", id: "LIST" }],
    }),

    // Employee login
    employeeLogin: builder.mutation<any, { employeeId: string }>({
      query: (credentials) => ({
        url: "/employeeLogin",
        method: "POST",
        body: credentials,
      }),
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetEmployeesListQuery,
  useGetEmployeeListQuery,
  useDeleteEmployeeListMutation,
  usePostEmployeeListMutation,
  usePutEmployeeListMutation,
  useGetDeductionsQuery,
  useGetDeductionByIdQuery,
  useCreateDeductionMutation,
  useUpdateDeductionMutation,
  useDeleteDeductionMutation,
  useEmployeeLoginMutation,
} = employeeListApi;
