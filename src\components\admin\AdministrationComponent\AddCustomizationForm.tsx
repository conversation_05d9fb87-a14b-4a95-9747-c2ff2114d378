import { type SetStateAction, useState, useEffect } from 'react';
import {
    useGetCustomizationQuery,
    usePostCustomizationMutation,
    usePutCustomizationMutation,
} from '../../../store/api/customizationApi';
import { toast } from 'react-toastify';
import { useNavigate, useParams } from 'react-router-dom';

export default function AddCustomizationForm() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  
  const userId = localStorage.getItem('userId') || '';
  
  const [postCustomization, { isLoading: isCreating }] = usePostCustomizationMutation();
  const [putCustomization, { isLoading: isUpdating }] = usePutCustomizationMutation();
  
  const { data: customizationData, isLoading: isLoadingCustomization } = useGetCustomizationQuery(id || '', {
    skip: !isEditMode,
  });

  const [formData, setFormData] = useState({
    customizationName: '',
    isActive: false
  });


  const customizationOptions = [
    { value: 'Orders', label: 'Orders' },
    { value: 'Orders History', label: 'Orders History' },
    { value: 'All Orders', label: 'All Orders' },
    { value: 'Completed Orders', label: 'Completed Orders' },
    { value: 'Catalog', label: 'Catalog' }
  ];

  // Load existing data when in edit mode
  useEffect(() => {
    if (isEditMode && customizationData) {
      setFormData({
        customizationName: customizationData.name || '',
        isActive: customizationData.active || false
      });
    }
  }, [customizationData, isEditMode]);

  const handleCustomizationChange = (e: { target: { value: SetStateAction<string>; }; }) => {
    setFormData(prevData => ({
      ...prevData,
      customizationName: e.target.value as string
    }));
  };

  const handleAddCustomization = async () => {
    if (!formData.customizationName) {
      toast.error('Please select Customization first');
      return;
    }

    try {
      // Prepare data for API
      const submitData = {
        name: formData.customizationName,
        active: formData.isActive.toString(),
        userId: userId
      };

      console.log(`${isEditMode ? 'Updating' : 'Creating'} customization...`, submitData);

      if (isEditMode && id) {
        await putCustomization({ id, formData: submitData }).unwrap();
        toast.success('Customization updated successfully!');
      } else {
        await postCustomization(submitData).unwrap();
        toast.success('Customization added successfully!');
      }

      // Redirect to customization list
      navigate('/admin/administration/customization');
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error(`Failed to ${isEditMode ? 'update' : 'add'} customization. Please try again.`);
    }
  };

  const handleCancel = () => {
    navigate('/admin/administration/customization');
  };

  if (isEditMode && isLoadingCustomization) {
    return (
      <div className="w-full p-6 flex justify-center items-center">
        <p className="text-gray-600">Loading customization data...</p>
      </div>
    );
  }

  return (
    <div className="w-full p-2">
      <div>
        {/* Customization Details Section */}
        <div className="rounded-md p-6 mb-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Customization Details</h2>
          
          <div className="mb-2">
            <div className="relative">
              <select
                value={formData.customizationName}
                onChange={handleCustomizationChange}
                className="w-full border border-gray-300 rounded p-2 pr-8 bg-white appearance-none focus:outline-none focus:ring-2 focus:ring-orange-500"
              
              >
                <option value="" disabled>Select customization</option>
                {customizationOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                  <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                </svg>
              </div>
            </div>
            {!formData.customizationName && (
              <p className="text-red-500 text-sm mt-1">Please select Customization first.</p>
            )}
          </div>
        </div>

        {/* Permissions Section */}
        <div className="rounded-md p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Permissions</h2>
          
          <div className="flex items-center">
            <label className="inline-flex items-center cursor-pointer">
              <div className={`relative ${formData.isActive ? 'bg-orange-500' : 'bg-gray-200'} w-12 h-6 rounded-full transition-colors duration-200 ease-in-out`}>
                <input
                  type="checkbox"
                  className="opacity-0 w-0 h-0"
                  checked={formData.isActive}
                  onChange={() => setFormData(prev => ({ ...prev, isActive: !prev.isActive }))}
                />
                <span className={`absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${formData.isActive ? 'transform translate-x-6' : ''}`}></span>
              </div>
              <span className="ml-3 text-gray-700">Activate</span>
            </label>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          <button
            onClick={handleCancel}
            className="border border-gray-300 text-gray-700 px-6 py-2 rounded hover:bg-gray-50 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={handleAddCustomization}
            disabled={isCreating || isUpdating}
            className={`${
              (isCreating || isUpdating) ? 'bg-orange-300' : 'bg-orange-500 hover:bg-orange-600'
            } text-white px-6 py-2 rounded transition-colors duration-200`}
          >
            {isEditMode ? 
              (isUpdating ? 'Updating...' : 'Update Customization') : 
              (isCreating ? 'Adding...' : 'Add Customization')
            }
          </button>
        </div>
      </div>
    </div>
  );
}