import React, { useState } from "react";
import CustomModal from "../CustomModal";

interface ReasonModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (reason: string) => void;
  initialReason?: string;
}

const ReasonModal: React.FC<ReasonModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialReason = ""
}) => {
  const [reason, setReason] = useState(initialReason);
  const [capsLock, setCapsLock] = useState(false);
  const [pressedKey, setPressedKey] = useState<string | null>(null);

  const handleKeyPress = (key: string) => {
    setPressedKey(key);
    setTimeout(() => setPressedKey(null), 200);

    if (key === "backspace") {
      setReason(prev => prev.slice(0, -1));
    } else if (key === "enter") {
      setReason(prev => prev + "\n");
    } else if (key === "tab") {
      setReason(prev => prev + "\t");
    } else if (key === "caps") {
      setCapsLock(prev => !prev);
    } else if (key === "shift") {
      return; // shift not handled
    } else if (key === ".com") {
      setReason(prev => prev + ".com");
    } else {
      setReason(prev => prev + (capsLock ? key.toUpperCase() : key));
    }
  };

  const handleSave = () => {
    onSave(reason);
    onClose();
  };

  const handleCancel = () => {
    setReason(initialReason);
    onClose();
  };

  const keyboardRows = [
    ["~", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "-", "=", "backspace"],
    ["tab", "q", "w", "e", "r", "t", "y", "u", "i", "o", "p", "[", "]", "\\"],
    ["caps", "a", "s", "d", "f", "g", "h", "j", "k", "l", ";", "'", "enter"],
    ["shift", "z", "x", "c", "v", "b", "n", "m", ",", ".", "/", "shift"],
    [".com", "@"]
  ];

  const getKeyDisplayText = (key: string) => {
    switch (key) {
      case "backspace":
        return "⌫";
      case "enter":
        return "↵";
      case "tab":
        return "⇥";
      case "caps":
        return "Caps";
      case "shift":
        return "⇧";
      default:
        return capsLock && key.length === 1 ? key.toUpperCase() : key;
    }
  };

  const getKeyClassName = (key: string) => {
    const baseClass =
      "px-3 py-2 border-2 border-orange-500 rounded-lg cursor-pointer hover:bg-orange-100 transition-colors text-orange-500 font-medium";
    const activeClass = pressedKey === key ? "bg-orange-200" : "";

    const sizeClass = () => {
      switch (key) {
        case "backspace":
        case "enter":
        case "shift":
          return "px-6";
        case "tab":
        case "caps":
        case ".com":
          return "px-4";
        default:
          return "w-10 h-10 flex items-center justify-center";
      }
    };

    return `${baseClass} ${activeClass} ${sizeClass()}`;
  };

  // Removed global keyboard event listener to prevent interference with other modals

  return (
    <CustomModal isOpen={isOpen} onClose={onClose} title="Reason">
      <div className="p-6 max-w-4xl mx-auto">
        {/* Text Input Area */}
        <div className="mb-6">
          <textarea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Enter Refund reason"
            className="w-full h-20 p-3 border-2 border-orange-500 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-orange-500 text-gray-700"
          />
        </div>

        {/* Virtual Keyboard */}
        <div className="space-y-2 mb-6">
          {keyboardRows.map((row, rowIndex) => (
            <div key={rowIndex} className="flex justify-center gap-1">
              {row.map((key) => (
                <button
                  key={key}
                  onClick={() => handleKeyPress(key)}
                  className={getKeyClassName(key)}
                >
                  {getKeyDisplayText(key)}
                </button>
              ))}
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4">
          <button
            onClick={handleSave}
            className="px-8 py-2 bg-orange-500 text-white font-bold rounded-lg hover:bg-orange-600 transition-colors"
          >
            OK
          </button>
          <button
            onClick={handleCancel}
            className="px-8 py-2 bg-red-500 text-white font-bold rounded-lg hover:bg-red-600 transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </CustomModal>
  );
};

export default ReasonModal;
