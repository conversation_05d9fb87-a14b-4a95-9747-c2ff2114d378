import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {

  Search,
  Download,
  Plus,
  Edit,
  Trash2,
  ChevronDown,
  ChevronUp,


} from "lucide-react";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";

import {
  useGetCustomersQuery,
  useDeleteCustomerMutation,
} from "../../../store/api/customerApi";
import Swal from "sweetalert2";

const CustomerManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [expandedCustomer, setExpandedCustomer] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [showError, setShowError] = useState<boolean>(false);
  const customersPerPage = 5;

  const navigate = useNavigate();

  const { data: customersData, isLoading, error, refetch } = useGetCustomersQuery();
  const [deleteCustomer] = useDeleteCustomerMutation();

  // Handle error state
  useEffect(() => {
    if (error) {
      // First show popup
      Swal.fire({
        title: "Error!",
        text: "Unable to load customer data. Please check your connection and try again.",
        icon: "error",
        confirmButtonText: "OK",
        confirmButtonColor: "#F29339",
        customClass: {
          popup: "rounded-xl",
          confirmButton: "px-4 py-2 rounded-md",
        },
      }).then(() => {
        // After popup is closed, show error in center of screen
        setShowError(true);
      });
    } else {
      setShowError(false);
    }
  }, [error]);

  // Handle customer deletion
  const handleDelete = async (id: string) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#6b7280",
      confirmButtonText: "Yes, delete it!",
      customClass: {
        popup: "rounded-xl",
        confirmButton: "px-4 py-2 rounded-md",
        cancelButton: "px-4 py-2 rounded-md",
      },
    });

    if (result.isConfirmed) {
      try {
        await deleteCustomer(id).unwrap();
        refetch();
        Swal.fire({
          title: "Deleted!",
          text: "Customer has been deleted.",
          icon: "success",
        });
      } catch (error) {
        Swal.fire({
          title: "Error!",
          text: "Failed to delete customer.",
          icon: "error",
          confirmButtonColor: "#F29339",
          customClass: {
            popup: "rounded-xl",
            confirmButton: "px-4 py-2 rounded-md",
          },
        });
      }
    }
  };

  const toggleExpandCustomer = (customerId: string) => {
    if (expandedCustomer === customerId) {
      setExpandedCustomer(null);
    } else {
      setExpandedCustomer(customerId);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleDownloadData = () => {
    // Create CSV content
    if (!customersData) return;

    const headers =
      "Customer ID,First Name,Last Name,Email,Phone,Address,City,State\n";
    const csvContent = customersData.reduce((acc, customer) => {
      return (
        acc +
        `${customer.CustomerId},${customer.FirstName},${customer.LastName},${customer.Email},${customer.Phone},${customer.Address},${customer.City},${customer.State}\n`
      );
    }, headers);

    // Create a blob and download
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.setAttribute("hidden", "");
    a.setAttribute("href", url);
    a.setAttribute("download", "customer_data.csv");
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    Swal.fire({
      title: "Download Started!",
      text: "Customer data is being downloaded.",
      icon: "success",
      confirmButtonColor: "#F29339",
      customClass: {
        popup: "rounded-xl",
        confirmButton: "px-4 py-2 rounded-md",
      },
    });
  };

  const handleAddCustomer = () => {
    navigate("/admin/customer/customer-list/customer-form");
  };

  const handleEditCustomer = (id: string) => {
    navigate(`/admin/customer/customer-list/customer-form/${id}`);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

;

  // Filter customers based on search term
  const filteredCustomers = customersData
    ? customersData.filter((customer) =>
        `${customer.FirstName} ${customer.LastName} ${customer.Email} ${customer.Phone} ${customer.CustomerId}`
          .toLowerCase()
          .includes(searchTerm.toLowerCase())
      )
    : [];

  // Pagination logic
  const totalPages = Math.ceil(
    (filteredCustomers?.length || 0) / customersPerPage
  );
  const paginatedCustomers =
    filteredCustomers?.slice(
      (currentPage - 1) * customersPerPage,
      currentPage * customersPerPage
    ) || [];

  if (isLoading) {
    return (
      <div className="flex justify-center items-start bg-white h-screen pt-[40vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading Customers...</p>
        </div>
      </div>
    );
  }

  if (showError) {
    return (
      <div className="flex justify-center items-center bg-gray-50 h-screen">
       
          <p className="text-red-600 text-center mb-6">
            We couldn't load the customer data. This might be due to a network issue or server problem.
          </p>

      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen p-6">
      <div>
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 border border-gray-200 p-4 bg-white rounded-2xl flex-wrap">
          <div className="flex items-center">
           
            <h1 className="text-2xl font-semibold">Customers</h1>
          </div>
          <div className="flex flex-col sm:flex-row flex-wrap items-start sm:items-center gap-2">
            {/* Search Input */}
            <div className="relative w-full sm:w-auto">
              <input
                type="text"
                placeholder="Search Customer"
                className="pl-10 pr-4 py-2 border border-gray-200 bg-gray-50 rounded-lg w-full focus:outline-none focus:ring-1 focus:ring-orange-500"
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
              />
              <div className="absolute left-3 top-2.5 text-gray-400">
                <Search size={18} />
              </div>
            </div>

            {/* Download Button */}
            <button
              className="flex items-center px-4 py-2  border border-orange-500 text-orange-500 hover:text-white cursor-pointer rounded-lg bg-orange-50  hover:bg-orange-500 transition-colors w-full sm:w-auto"
              onClick={handleDownloadData}
            >
              <Download size={18} className="mr-2" />
              <span>Download Customer Data</span>
            </button>

            {/* Add Customer Button */}
            <button
              className="flex items-center  cursor-pointer px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors w-full sm:w-auto"
              onClick={handleAddCustomer}
            >
              <Plus size={18} className="mr-2" />
              <span>Add Customer</span>
            </button>
          </div>
        </div>

        <div className="overflow-x-auto border border-gray-200 mt-3 rounded-2xl">
          <table className="min-w-full divide-y divide-gray-200 table-fixed">
            <thead className="bg-orange-50">
              <tr>
                <th className="px-4 py-6 text-left text-sm font-semibold text-gray-700 tracking-wider w-[120px] whitespace-nowrap">
                  Customer ID
                </th>
                <th className="px-4 py-6 text-left text-sm font-semibold text-gray-700 tracking-wider w-[110px] whitespace-nowrap">
                  First Name
                </th>
                <th className="px-4 py-6 text-left text-sm font-semibold text-gray-700 tracking-wider w-[110px] whitespace-nowrap">
                  Last Name
                </th>
                <th className="px-4 py-6 text-left text-sm font-semibold text-gray-700 tracking-wider w-[200px] whitespace-nowrap">
                  Address
                </th>
                <th className="px-4 py-6 text-left text-sm font-semibold text-gray-700 tracking-wider w-[120px] whitespace-nowrap">
                  City
                </th>
                <th className="px-4 py-6 text-left text-sm font-semibold text-gray-700 tracking-wider w-[100px] whitespace-nowrap">
                  State
                </th>
                <th className="px-4 py-6 text-left text-sm font-semibold text-gray-700 tracking-wider w-[200px] whitespace-nowrap">
                  Email
                </th>
                <th className="px-4 py-6 text-left text-sm font-semibold text-gray-700 tracking-wider w-[130px] whitespace-nowrap">
                  Contact
                </th>
                <th className="px-4 py-6 text-left text-sm font-semibold text-gray-700 tracking-wider w-[90px] whitespace-nowrap">
                  Status
                </th>
                <th className="px-4 py-6 text-left text-sm font-semibold text-gray-700 tracking-wider w-[120px] whitespace-nowrap">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedCustomers.map((customer) => (
                <React.Fragment key={customer._id}>
                  <tr className="hover:bg-orange-50">
                    <td className="px-4 py-4 text-sm text-gray-600 w-[120px] whitespace-nowrap overflow-hidden text-ellipsis">
                      {customer.CustomerId}
                    </td>
                    <td className="px-4 py-4 text-sm font-medium text-gray-600 w-[110px] whitespace-nowrap overflow-hidden text-ellipsis">
                      {customer.FirstName}
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-600 w-[110px] whitespace-nowrap overflow-hidden text-ellipsis">
                      {customer.LastName}
                    </td>
                    <td
                      className="px-4 py-4 text-sm text-gray-600 w-[200px] whitespace-nowrap overflow-hidden text-ellipsis"
                      title={customer.Address}
                    >
                      {customer.Address}
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-600 w-[120px] whitespace-nowrap overflow-hidden text-ellipsis">
                      {customer.City}
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-600 w-[100px] whitespace-nowrap overflow-hidden text-ellipsis">
                      {customer.State}
                    </td>
                    <td
                      className="px-4 py-4 text-sm text-gray-600 w-[200px] whitespace-nowrap overflow-hidden text-ellipsis"
                      title={customer.Email}
                    >
                      {customer.Email}
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-600 w-[130px] whitespace-nowrap overflow-hidden text-ellipsis">
                      {customer.Phone}
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-600 w-[90px]">
                      <span
                        className={`px-3 inline-flex text-xs leading-5 font-semibold rounded-lg whitespace-nowrap ${
                          customer.isActive
                            ? "bg-green-50 text-green-500 border px-6 border-green-400"
                            : "bg-orange-50 text-orange-500 border px-5 border-orange-400"
                        }`}
                      >
                        {customer.isActive ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className="px-4 py-4 text-right text-sm font-medium w-[120px]">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          className="text-blue-500 hover:text-blue-700"
                          onClick={() => handleEditCustomer(customer._id)}
                        >
                          <Edit
                            id="edit-icon"
                            data-tooltip-id="edit-tooltip"
                            data-tooltip-content="Edit"
                            size={20}
                            className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                          />
                          <Tooltip
                            id="edit-tooltip"
                            place="bottom"
                            className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                          />
                        </button>
                        <button
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleDelete(customer._id)}
                        >
                          <Trash2
                            id="delete-icon"
                            data-tooltip-id="delete-tooltip"
                            data-tooltip-content="Delete"
                            size={20}
                            className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                          />
                          <Tooltip
                            id="delete-tooltip"
                            place="bottom"
                            className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                          />
                        </button>
                        <button
                          className="text-gray-500 hover:text-gray-700"
                          onClick={() => toggleExpandCustomer(customer._id)}
                          data-tooltip-id={`toggle-tooltip-${customer._id}`}
                          data-tooltip-content={
                            expandedCustomer === customer._id
                              ? "Collapse"
                              : "Expand"
                          }
                        >
                          {expandedCustomer === customer._id ? (
                            <ChevronUp size={18} />
                          ) : (
                            <ChevronDown size={18} />
                          )}
                        </button>

                        <Tooltip
                          id={`toggle-tooltip-${customer._id}`}
                          place="bottom"
                          className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                        />
                      </div>
                    </td>
                  </tr>
                  {expandedCustomer === customer._id && (
                    <tr className="bg-gray-50">
                      <td colSpan={10} className="px-6 py-4">
                        <div className="grid grid-cols-6 gap-4">
                          <div>
                            <p className="text-xs text-gray-500">Card No</p>
                            <p className="text-sm font-medium">
                              {customer.CustomerLoyalty?.CardNo || "N/A"}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500">Start Date</p>
                            <p className="text-sm font-medium">
                              {customer.CustomerLoyalty?.StartDate
                                ? new Date(
                                    customer.CustomerLoyalty.StartDate
                                  ).toLocaleDateString()
                                : "N/A"}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500">Points</p>
                            <p className="text-sm font-medium">
                              {customer.CustomerLoyalty?.Points || "0"}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500">Visits</p>
                            <p className="text-sm font-medium">
                              {customer.CustomerLoyalty?.Visits || "0"}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500">Last Visit</p>
                            <p className="text-sm font-medium">
                              {customer.CustomerLoyalty?.LastVisit
                                ? new Date(
                                    customer.CustomerLoyalty.LastVisit
                                  ).toLocaleDateString()
                                : "No visits recorded"}
                            </p>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>

       
      </div>
       <div className="px-5 py-5 flex flex-col xs:flex-row items-start xs:justify-between">
          <div className="flex items-center">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`h-8 w-8 mr-1 flex justify-center items-center rounded-md border ${
                currentPage === 1
                  ? "border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "border-gray-300 bg-white text-gray-500 hover:bg-gray-100"
              }`}
            >
              <svg
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`h-8 w-8 mx-1 flex justify-center items-center rounded-md ${
                  currentPage === page
                    ? "border border-orange-500 bg-orange-500 text-white"
                    : "border border-gray-300 bg-white text-gray-500 hover:bg-gray-100"
                }`}
              >
                {page}
              </button>
            ))}

            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`h-8 w-8 ml-1 flex justify-center items-center rounded-md border ${
                currentPage === totalPages
                  ? "border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "border-gray-300 bg-white text-gray-500 hover:bg-gray-100"
              }`}
            >
              <svg
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>
    </div>
  );
};

export default CustomerManagement;