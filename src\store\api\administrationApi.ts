import { baseApi } from "./baseApi";

export interface Administration {
  id: string;
  name?: string;
  active?: boolean;
  Line1?: string;
  Line2?: string;
  City?: string;
  Phoneno?: string;
  State?: string;
  PostalCode?: string;
  Country?: string;
  image?: string;
  businessType?: string;
  delivery?: boolean;
  deliveryStartTime?: string;
  deliveryEndTime?: string;
  ChargesperKm?: number;
  ChargesFreeKm?: number;
  pickupStartTime?: string;
  pickupEndTime?: string;
  userId: string;
  createdDate?: string;
  reviews?: Array<{
    food: number;
    service: number;
    ambiance: number;
    customerId: string;
    testimonial: string;
    createdDate?: string;
  }>;
  favorites?: Array<{
    customerId: string;
  }>;
}

export const administrationApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAdministrations: builder.query<Administration[], string>({
      query: (userId) => `/device?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          name: item.name,
          active: item.active,
          Line1: item.Line1,
          Line2: item.Line2,
          City: item.City,
          Phoneno: item.Phoneno,
          State: item.State,
          PostalCode: item.PostalCode,
          Country: item.Country,
          image: item.image,
          businessType: item.businessType,
          delivery: item.delivery,
          deliveryStartTime: item.deliveryStartTime,
          deliveryEndTime: item.deliveryEndTime,
          ChargesperKm: item.ChargesperKm,
          ChargesFreeKm: item.ChargesFreeKm,
          pickupStartTime: item.pickupStartTime,
          pickupEndTime: item.pickupEndTime,
          userId: item.userId,
          createdDate: item.createdDate,
          reviews: item.reviews || [],
          favorites: item.favorites || []
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({
                type: "Administration" as const,
                id,
              })),
              { type: "Administration", id: "LIST" },
            ]
          : [{ type: "Administration", id: "LIST" }],
    }),

    getAdministration: builder.query<Administration, string>({
      query: (id) => `/device/${id}`,
      transformResponse: (response: any) => ({
        id: response._id,
        name: response.name,
        active: response.active,
        Line1: response.Line1,
        Line2: response.Line2,
        City: response.City,
        Phoneno: response.Phoneno,
        State: response.State,
        PostalCode: response.PostalCode,
        Country: response.Country,
        image: response.image,
        businessType: response.businessType,
        delivery: response.delivery,
        deliveryStartTime: response.deliveryStartTime,
        deliveryEndTime: response.deliveryEndTime,
        ChargesperKm: response.ChargesperKm,
        ChargesFreeKm: response.ChargesFreeKm,
        pickupStartTime: response.pickupStartTime,
        pickupEndTime: response.pickupEndTime,
        userId: response.userId,
        createdDate: response.createdDate,
        reviews: response.reviews || [],
        favorites: response.favorites || []
      }),
      providesTags: (_result, _error, id) => [{ type: "Administration", id }],
    }),

    deleteAdministration: builder.mutation<void, string>({
      query: (id) => ({
        url: `/device/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            administrationApi.util.invalidateTags([{ type: "Administration", id }])
          );
        } catch (error) {
          console.error("Failed to delete administration", error);
        }
      },
      invalidatesTags: [{ type: "Administration", id: "LIST" }],
    }),

    postAdministration: builder.mutation<any, FormData>({
      query: (data) => {
        return {
          url: "/device",
          method: "POST",
          body: data, // This should be FormData if you're uploading an image
        };
      },
      invalidatesTags: [{ type: "Administration", id: "LIST" }],
    }),

    putAdministration: builder.mutation<any, { id: string; data: FormData }>({
      query: ({ id, data }) => ({
        url: `/device/${id}`,
        method: "PUT",
        body: data, // This should be FormData if you're uploading an image
      }),
      transformResponse: (response: any) => {
        return response;
      },
      invalidatesTags: [{ type: "Administration", id: "LIST" }],
    }),

    // Review endpoints
    postReview: builder.mutation<any, {
      deviceId: string;
      food: number;
      service: number;
      ambiance: number;
      testimonial: string;
      customerId: string;
    }>({
      query: (data) => ({
        url: "/device/review",
        method: "POST",
        body: data,
      }),
      invalidatesTags: [{ type: "Administration", id: "LIST" }],
    }),

    // Get customer reviews
    getCustomerReviews: builder.query<any[], string>({
      query: (customerId) => `/devices/customer/${customerId}`,
      providesTags: [{ type: "Administration", id: "REVIEWS" }],
    }),

    // Favorite endpoints
    addFavorite: builder.mutation<any, { deviceId: string; customerId: string }>({
      query: ({ deviceId, customerId }) => ({
        url: `/device/${deviceId}/favorite/${customerId}`,
        method: "POST",
      }),
      invalidatesTags: [{ type: "Administration", id: "LIST" }],
    }),

    getFavorites: builder.query<any[], string>({
      query: (customerId) => `/devices/favorites/${customerId}`,
      providesTags: [{ type: "Administration", id: "FAVORITES" }],
    }),

    // Patronpal specific endpoint
    getPatronpalDevices: builder.query<Administration[], string>({
      query: (userId) => `/Pdevice?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          name: item.name,
          active: item.active,
          Line1: item.Line1,
          Line2: item.Line2,
          City: item.City,
          Phoneno: item.Phoneno,
          State: item.State,
          PostalCode: item.PostalCode,
          Country: item.Country,
          image: item.image,
          businessType: item.businessType,
          delivery: item.delivery,
          deliveryStartTime: item.deliveryStartTime,
          deliveryEndTime: item.deliveryEndTime,
          ChargesperKm: item.ChargesperKm,
          ChargesFreeKm: item.ChargesFreeKm,
          pickupStartTime: item.pickupStartTime,
          pickupEndTime: item.pickupEndTime,
          userId: item.userId,
          createdDate: item.createdDate,
          reviews: item.reviews || [],
          favorites: item.favorites || []
        }));
      },
      providesTags: [{ type: "Administration", id: "PATRONPAL" }],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetAdministrationsQuery,
  useGetAdministrationQuery,
  useDeleteAdministrationMutation,
  usePostAdministrationMutation,
  usePutAdministrationMutation,
  usePostReviewMutation,
  useGetCustomerReviewsQuery,
  useAddFavoriteMutation,
  useGetFavoritesQuery,
  useGetPatronpalDevicesQuery
} = administrationApi;