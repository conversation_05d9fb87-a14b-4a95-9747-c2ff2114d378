import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { IngredientsCategory } from '../api/ingredientsCategoryApi';
import { ingredientsCategoryApi } from '../api/ingredientsCategoryApi';

interface IngredientsCategoryState {
  ingredientsCategories: IngredientsCategory[];
  isLoading: boolean;
  error: string | null;
}

const initialState: IngredientsCategoryState = {
  ingredientsCategories: [],
  isLoading: false,
  error: null,
};

const ingredientsCategorySlice = createSlice({
  name: 'ingredientsCategory',
  initialState,
  reducers: {
    setIngredientsCategories: (state, action: PayloadAction<IngredientsCategory[]>) => {
      state.ingredientsCategories = action.payload;
    },
    clearIngredientsCategories: (state) => {
      state.ingredientsCategories = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET
      .addMatcher(
        ingredientsCategoryApi.endpoints.getIngredientsCategories.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        ingredientsCategoryApi.endpoints.getIngredientsCategories.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.ingredientsCategories = payload;
        }
      )
      .addMatcher(
        ingredientsCategoryApi.endpoints.getIngredientsCategories.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch ingredients categories';
        }
      )

      // DELETE
      .addMatcher(
        ingredientsCategoryApi.endpoints.deleteIngredientsCategory.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        ingredientsCategoryApi.endpoints.deleteIngredientsCategory.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const categoryId = action.meta.arg.originalArgs as string;
          state.ingredientsCategories = state.ingredientsCategories.filter(
            (category) => category.id !== categoryId
          );
        }
      )
      .addMatcher(
        ingredientsCategoryApi.endpoints.deleteIngredientsCategory.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete category';
        }
      )

      // POST
      .addMatcher(
        ingredientsCategoryApi.endpoints.postIngredientsCategory.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        ingredientsCategoryApi.endpoints.postIngredientsCategory.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.ingredientsCategories.push({
            id: payload._id,
            name: payload.name,
            subCategoriesCount: 0,
          });
        }
      )
      .addMatcher(
        ingredientsCategoryApi.endpoints.postIngredientsCategory.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create category';
        }
      )

      // PUT
      .addMatcher(
        ingredientsCategoryApi.endpoints.putIngredientsCategory.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        ingredientsCategoryApi.endpoints.putIngredientsCategory.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          const updated = {
            id: payload.data._id,
            name: payload.data.name,
            subCategoriesCount: 0,
          };
          state.ingredientsCategories = state.ingredientsCategories.map((cat) =>
            cat.id === updated.id ? updated : cat
          );
        }
      )
      .addMatcher(
        ingredientsCategoryApi.endpoints.putIngredientsCategory.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update category';
        }
      );
  },
});

export const { setIngredientsCategories, clearIngredientsCategories } =
  ingredientsCategorySlice.actions;

export const selectIngredientsCategories = (state: RootState) =>
  state.ingredientsCategory.ingredientsCategories;
export const selectIngredientsLoading = (state: RootState) =>
  state.ingredientsCategory.isLoading;
export const selectIngredientsError = (state: RootState) =>
  state.ingredientsCategory.error;

export default ingredientsCategorySlice.reducer;
