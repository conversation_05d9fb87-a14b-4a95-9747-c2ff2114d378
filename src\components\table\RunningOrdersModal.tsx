import React from 'react';
import { IoClose } from "react-icons/io5";
import { MdOutlineEdit, MdOutlinePrint, MdPayment } from "react-icons/md";

interface RunningOrdersModalProps {
    isOpen: boolean;
    onClose: () => void;
}

interface Order {
    tableNumber: string;
    orderNumber: string;
    orderDetails: string;
    amount: number;
    status: "Running Order" | "Done soon" | "Done";
    dinningFor: string;
}

const RunningOrdersModal: React.FC<RunningOrdersModalProps> = ({ isOpen, onClose }) => {
    const orders: Order[] = [
        {
            tableNumber: "T-06",
            orderNumber: "#46",
            orderDetails: "Steak Sapi Baker x10...",
            amount: 25.30,
            status: "Running Order",
            dinningFor: "35 minutes"
        },
        {
            tableNumber: "T-07",
            orderNumber: "#47",
            orderDetails: "Steak Sapi Baker x10...",
            amount: 25.30,
            status: "Done soon",
            dinningFor: "35 minutes"
        },
        {
            tableNumber: "T-08",
            orderNumber: "#48",
            orderDetails: "Steak Sapi Baker x10...",
            amount: 25.30,
            status: "Done",
            dinningFor: "35 minutes"
        },
    ];

    if (!isOpen) return null;

    const getStatusColor = (status: string) => {
        switch (status) {
            case "Running Order":
                return "bg-blue-100 text-blue-600";
            case "Done soon":
                return "bg-orange-100 text-orange-600";
            case "Done":
                return "bg-green-100 text-green-600";
            default:
                return "bg-gray-100 text-gray-600";
        }
    };

    const getActionButtons = (status: string, _tableNumber: string) => {
        switch (status) {
            case "Running Order":
                return (
                    <div className="flex space-x-3 items-center">
                        <button className="px-6 py-[6px] text-sm font-semibold cursor-pointer border border-orange-500 text-orange-500 rounded-full  flex items-center justify-center whitespace-nowrap">
                            <MdOutlineEdit className="w-4 h-4 mr-1" />
                            Edit info
                        </button>
                        <button className="px-6 py-[6px] text-sm font-semibold cursor-pointer border border-orange-500 text-orange-500 rounded-full  flex items-center justify-center whitespace-nowrap">
                            <MdOutlinePrint className="w-4 h-4 mr-1" />
                            Print Bill
                        </button>
                    </div>
                );
            case "Done soon":
                return (
                    <div className="flex space-x-3 items-center">
                        <button className="px-6 py-[6px] text-sm font-semibold cursor-pointer border border-orange-500 text-orange-500 rounded-full  flex items-center justify-center whitespace-nowrap">
                            <MdOutlinePrint className="w-4 h-4 mr-1" />
                            Re-print
                        </button>
                        <button className="px-6 py-[6px] text-sm font-semibold cursor-pointer bg-orange-500 text-white rounded-full flex items-center justify-center whitespace-nowrap">
                            <MdPayment className="w-4 h-4 mr-1" />
                            Pay Now
                        </button>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <div className="fixed inset-0 z-50 flex justify-end">
            <div className="bg-black/50 absolute inset-0" onClick={onClose} />
            <div className="relative xl:w-[81vw] w-full bg-white h-full flex flex-col">
                <div className="flex items-center justify-between p-4">
                    <h2 className="text-xl font-semibold">Running Orders</h2>
                    <button onClick={onClose} className=' text-gray-400 hover:text-gray-600 cursor-pointer'>
                        <IoClose size={28} />
                    </button>
                </div>

                <div className="flex-1 overflow-y-auto px-7 pt-2">
                    <div className="grid grid-cols-16 px-4 py-2 text-sm text-gray-500 border-b border-[#E4E4E4]">
                        <div className="col-span-2">Table Number</div>
                        <div className="col-span-3">Order Number</div>
                        <div className="col-span-2">Amount</div>
                        <div className="col-span-2">Status</div>
                        <div className="col-span-2">Dinning For</div>
                    </div>

                    {/* Order List */}
                    {orders.map((order, index) => (
                        <div key={index} className="grid grid-cols-16 px-4 py-3 items-center border-b border-[#E4E4E4] hover:bg-gray-50">
                            <div className="col-span-2">
                                <span className="px-3 py-4 bg-[#FFF5EE] text-orange-500 rounded-full font-semibold text-sm">
                                    {order.tableNumber}
                                </span>
                            </div>
                            <div className="col-span-3">
                                <div className="font-semibold">{order.orderNumber}</div>
                                <div className="text-xs text-gray-500">{order.orderDetails}</div>
                            </div>
                            <div className="col-span-2 text-sm">${order.amount.toFixed(2)}</div>
                            <div className="col-span-2">
                                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                                    {order.status}
                                </span>
                            </div>
                            <div className="col-span-2 text-sm">{order.dinningFor}</div>
                            <div className="col-span-2">
                                {getActionButtons(order.status, order.tableNumber)}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default RunningOrdersModal;