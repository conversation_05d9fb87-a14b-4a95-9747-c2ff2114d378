import React from 'react';
import CustomModal from '../../CustomModal';
import { Info } from 'lucide-react';

// Define the employee object type to avoid duplication
interface EmployeeObject {
    address?: string;
    allowancesHistory?: any[];
    bonusesHistory?: any[];
    createdAt?: string;
    deductions?: Array<{ amount: number; name: string; _id: string }>;
    email?: string;
    employeeEndTime?: string;
    employeeId?: string;
    employeeStartTime?: string;
    employeeType?: string;
    firstName?: string;
    hourlyRate?: number;
    lastName?: string;
    overTimeRate?: number;
    phoneNo?: string;
    role?: string;
    startDate?: string;
    totalHours?: number;
    updatedAt?: string;
    userId?: string;
    __v?: number;
    _id?: string;
}

// Define the main employee data type
interface EmployeeData {
    empName?: string;
    employeeId?: string | EmployeeObject;
    startDate?: string;
    startHour?: string;
    deductionAmount?: number | string;
    deductions?: Array<{ amount: number; name: string; _id: string }>;
    email?: string;
    employeeEndTime?: string;
    employeeStartTime?: string;
    employeeType?: string;
    firstName?: string;
    hourlyRate?: number;
    lastName?: string;
    month?: string;
    overTimeRate?: number;
    overtimeHourlyRate?: number;
    overtimeHours?: string;
    overtimeTotalAmount?: number;
    phoneNo?: string;
    role?: string;
    shiftHours?: string;
    shiftTotalAmount?: number;
    startHourlyRate?: number;
    subtotal?: number;
    total?: number;
    totalHours?: number;
    totalWork?: string;
    userId?: string;
    _id?: string;
    __v?: number;
}

interface PaySlipProps {
    isOpen: boolean;
    onClose: () => void;
    employeeData: EmployeeData;
}

const PaySlip: React.FC<PaySlipProps> = ({ isOpen, onClose, employeeData }) => {
    const handlePrint = () => {
        window.print();
    };

    // Format the payment date (current date)
    const currentDate = new Date();
    const paymentDate = currentDate.toDateString();

    // Get the pay period from the employee data or use a default
    const payPeriod = employeeData.month || 'Current Month';

    // Helper to safely format numbers
    const formatNumber = (value: unknown): number => {
        if (typeof value === 'number') return value;
        if (typeof value === 'string') {
            const parsed = parseFloat(value);
            return isNaN(parsed) ? 0 : parsed;
        }
        return 0;
    };

    // Extract employee information from the nested structure if needed
    const employeeInfo = typeof employeeData.employeeId === 'object' ? employeeData.employeeId : employeeData;

    // Get employee name
    const name = employeeData.empName ||
        (employeeInfo.firstName && employeeInfo.lastName
            ? `${employeeInfo.firstName} ${employeeInfo.lastName}`
            : 'N/A');

    // Get employee ID - ensure it's always a string for React rendering
    const id = typeof employeeData.employeeId === 'string'
        ? employeeData.employeeId
        : typeof employeeInfo.employeeId === 'string'
            ? employeeInfo.employeeId
            : 'N/A';

    // Get employee type
    const employeeType = employeeInfo.employeeType || 'N/A';

    // Get hourly rate
    const hourlyRate = formatNumber(employeeInfo.hourlyRate || 0);

    // Get overtime rate
    const overtimeRate = formatNumber(employeeInfo.overTimeRate || 0);

    // Calculate overtime hourly rate if not provided
    const overtimeHourlyRate = formatNumber(employeeData.overtimeHourlyRate || (hourlyRate * overtimeRate) || 0);

    // Get total hours
    const totalHours = formatNumber(employeeInfo.totalHours || 0);

    // Calculate shift hours if not provided
    const shiftHours = employeeData.shiftHours || totalHours.toString() || '0';

    // Calculate shift total amount if not provided
    const shiftTotalAmount = formatNumber(employeeData.shiftTotalAmount || (hourlyRate * parseFloat(shiftHours)) || 0);

    // Get overtime hours
    const overtimeHours = employeeData.overtimeHours || '0';

    // Calculate overtime total amount if not provided
    const overtimeTotalAmount = formatNumber(employeeData.overtimeTotalAmount || (overtimeHourlyRate * parseFloat(overtimeHours)) || 0);

    // Calculate subtotal if not provided
    const subtotal = formatNumber(employeeData.subtotal || shiftTotalAmount + overtimeTotalAmount || 0);

    // Get deductions
    const deductions = employeeData.deductions ||
        (typeof employeeData.employeeId === 'object' && employeeData.employeeId.deductions) ||
        [];

    // Calculate deduction amounts and total deductions
    const deductionDetails = deductions.map(deduction => {
        const amount = (subtotal * deduction.amount) / 100;
        return {
            name: deduction.name,
            percentage: deduction.amount,
            amount: amount,
        };
    });

    const totalDeductions = deductionDetails.reduce((sum, d) => sum + d.amount, 0);

    // Calculate total if not provided
    const total = formatNumber(employeeData.total || (subtotal - totalDeductions) || 0);

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={onClose}
            title="Pay Slip"
            width="max-w-lg"
            zIndex={1009}
            footer={null}
        >
            <div>
                {/* Split layout */}
                <div className="flex flex-col md:flex-row">
                    {/* Left Side - Employee Information */}
                    <div className="w-full md:w-1/2 bg-gray-50 p-6">
                        <h3 className="font-medium mb-4">Employee Information</h3>
                        <div className="space-y-4">
                            <div>
                                <p className="text-gray-500">Name</p>
                                <p>{name}</p>
                            </div>
                            <div>
                                <p className="text-gray-500">Employee ID</p>
                                <p>{id}</p>
                            </div>
                            <div>
                                <p className="text-gray-500">Designation</p>
                                <p>{employeeType}</p>
                            </div>
                        </div>
                    </div>

                    {/* Right Side - Deductions */}
                    <div className="w-full md:w-1/2 p-6">
                        <h3 className="font-medium mb-4">Deductions</h3>
                        {deductionDetails.length > 0 ? (
                            <>
                                <div className="space-y-4">
                                    {deductionDetails.map((deduction, idx) => (
                                        <div key={idx} className="flex justify-between items-center">
                                            <p className="text-gray-500">{deduction.name}</p>
                                            <p>${deduction.amount.toFixed(2)} ({deduction.percentage}%)</p>
                                        </div>
                                    ))}
                                </div>
                                <div className="flex justify-between items-center mt-4 font-medium border-t pt-4 border-gray-200">
                                    <p>Total Deductions</p>
                                    <p className="text-red-500">-${totalDeductions.toFixed(2)}</p>
                                </div>
                            </>
                        ) : (
                            <div className="flex items-center text-gray-500 mb-4">
                                <Info size={20} className="mr-2" />
                                <p>No Deductions Available</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Earning Description Table */}
                <div className="px-6">
                    <div className="grid grid-cols-3 py-3 text-gray-500 text-sm">
                        <div>Earning Description</div>
                        <div>Hours/Rate</div>
                        <div className="text-right">Amount</div>
                    </div>
                    <div className="border-t border-gray-200">
                        <div className="grid grid-cols-3 py-3">
                            <div>Regular Hours</div>
                            <div>{shiftHours} / ${hourlyRate.toFixed(2)}</div>
                            <div className="text-right">${shiftTotalAmount.toFixed(2)}</div>
                        </div>
                        <div className="grid grid-cols-3 py-3 border-t border-gray-200">
                            <div>Overtime Hours</div>
                            <div>{overtimeHours} / ${overtimeHourlyRate.toFixed(2)}</div>
                            <div className="text-right">${overtimeTotalAmount.toFixed(2)}</div>
                        </div>
                    </div>
                </div>

                {/* Total Earning */}
                <div className="px-6 py-4 bg-gray-50">
                    <div className="flex justify-between items-center">
                        <h3 className="font-medium">Total Earning:</h3>
                        <p className="text-xl font-bold text-orange-500">${total.toFixed(2)}</p>
                    </div>
                </div>

                {/* Payment Date and Period */}
                <div className="px-6 py-4 flex justify-between items-center border-t border-gray-200">
                    <div>
                        <p className="text-gray-500">Payment Date</p>
                        <p>{paymentDate}</p>
                    </div>
                    <div>
                        <p className="text-gray-500">Pay Period</p>
                        <p>{payPeriod}</p>
                    </div>
                </div>

                {/* Buttons */}
                <div className="px-6 py-4 flex justify-end gap-4 border-t border-gray-200">
                    <button
                        onClick={handlePrint}
                        className="flex items-center justify-center gap-2 px-8 py-2 bg-black text-white font-medium rounded-full hover:bg-gray-800 transition-colors"
                        type="button"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <polyline points="6 9 6 2 18 2 18 9"></polyline>
                            <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                            <rect x="6" y="14" width="12" height="8"></rect>
                        </svg>
                        Print
                    </button>
                    <button
                        onClick={onClose}
                        className="px-8 py-2 border border-orange-500 text-orange-500 font-medium rounded-full hover:bg-orange-50 transition-colors"
                        type="button"
                    >
                        Cancel
                    </button>
                </div>
            </div>
        </CustomModal>
    );
};

export default PaySlip;