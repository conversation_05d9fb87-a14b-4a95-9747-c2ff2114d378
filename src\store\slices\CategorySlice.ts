
import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import { categoryApi } from '../api/CategoryApi';


export interface Category {
  id: string;
  name: string;
  parentCategory?: string;
  pictureUrl?: string;
  totalMenuItems?: number;
  status?: boolean;
}

interface CategoryState {
  categories: Category[];
  isLoading: boolean;
  error: string | null;
}

const initialState: CategoryState = {
  categories: [],
  isLoading: false,
  error: null,
};

const categorySlice = createSlice({
  name: 'category',
  initialState,
  reducers: {
    setCategories: (state, action: PayloadAction<Category[]>) => {
      state.categories = action.payload;
    },
    clearCategories: (state) => {
      state.categories = [];
    },
  },
  extraReducers: (builder) => {
    // --- GET CATEGORIES ---
    builder
      .addMatcher(
        categoryApi.endpoints.getCategories.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        categoryApi.endpoints.getCategories.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          state.categories = action.payload;
        }
      )
      .addMatcher(
        categoryApi.endpoints.getCategories.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error?.message || 'Failed to fetch categories';
        }
      );

    // --- POST CATEGORY ---
    builder
      .addMatcher(
        categoryApi.endpoints.postCategory.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        categoryApi.endpoints.postCategory.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.categories.push({
            id: payload._id,
            name: payload.name,
            parentCategory: payload.parentId?.name ?? undefined,
            pictureUrl: payload.category_pic || '',
            totalMenuItems: 0,
            status: payload.active === 'true',
          });
        }
      )
      .addMatcher(
        categoryApi.endpoints.postCategory.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create category';
        }
      );

    // --- DELETE CATEGORY ---
    builder
      .addMatcher(
        categoryApi.endpoints.deleteCategory.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        categoryApi.endpoints.deleteCategory.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const categoryId = action.meta.arg.originalArgs as string;
          state.categories = state.categories.filter(
            (category) => category.id !== categoryId
          );
        }
      )
      .addMatcher(
        categoryApi.endpoints.deleteCategory.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete category';
        }
      );

    // --- PUT CATEGORY (UPDATE) ---
    // builder
    //   .addMatcher(
    //     categoryApi.endpoints.putCategory.matchPending,
    //     (state) => {
    //       state.isLoading = true;
    //       state.error = null;
    //     }
    //   )
    //   .addMatcher(
    //     categoryApi.endpoints.putCategory.matchFulfilled,
    //     (state, { payload }) => {
    //       state.isLoading = false;
    //       const updated = {
    //         id: payload._id,
    //         name: payload.name,
    //         parentCategory: payload.parentId?.name ?? undefined,
    //         pictureUrl: payload.category_pic || '',
    //         totalMenuItems: payload.totalMenuItems || 0,
    //         status: payload.active === 'true',
    //       };
    //       state.categories = state.categories.map(cat =>
    //         cat.id === updated.id ? updated : cat
    //       );
    //     }
    //   )
    //   .addMatcher(
    //     categoryApi.endpoints.putCategory.matchRejected,
    //     (state, { error }) => {
    //       state.isLoading = false;
    //       state.error = error.message || 'Failed to update category';
    //     }
    //   );
  },
});

export const { setCategories, clearCategories } = categorySlice.actions;

export const selectCategories = (state: RootState) => state.category.categories;
export const selectIsLoading = (state: RootState) => state.category.isLoading;
export const selectError = (state: RootState) => state.category.error;

export default categorySlice.reducer;