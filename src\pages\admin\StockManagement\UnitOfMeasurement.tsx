import { useState, useEffect } from "react";
import { Search, Edit, Trash2, ChevronLeft, ChevronRight, Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  useGetUnitOfMeasurementsQuery,
  useDeleteUnitOfMeasurementMutation,
} from "../../../store/api/unitOfMeasurementApi";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import Swal from "sweetalert2";

export default function UnitOfMeasurement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const navigate = useNavigate();
  const userId = localStorage.getItem('userId') || '';

  const { data: unitsData, isLoading, error, refetch } = useGetUnitOfMeasurementsQuery(userId);
  const [deleteUnitOfMeasurement, { isLoading: isDeleting }] = useDeleteUnitOfMeasurementMutation();

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleDelete = async (id: string) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this Unit of Measurement?",
      icon: "warning",
      cancelButtonText: "No, cancel!",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#6b7280",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        await deleteUnitOfMeasurement(id).unwrap();
        await Swal.fire(
          "Deleted!",
          "Unit of measurement has been deleted successfully.",
          "success"
        );
        refetch();
      } catch (error) {
        console.error('Failed to delete unit of measurement:', error);
        Swal.fire("Error!", "Failed to delete unit of measurement.", "error");
      }
    }
  };

  const handleAddUnit = () => {
    navigate('/admin/stock-management/unit-of-measurement/unit-of-measurement-form');
  };

  const handleEdit = (id: string) => {
    navigate(`/admin/stock-management/unit-of-measurement/unit-of-measurement-form/${id}`);
  };

  // Filter units based on search term
  const filteredUnits = unitsData?.filter(unit =>
    unit.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    unit.code.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentUnits = filteredUnits.slice(indexOfFirstItem, indexOfLastItem);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  useEffect(() => {
    if (error) {
      Swal.fire("Error!", "Failed to fetch units of measurement.", "error");
      console.error('Error fetching units of measurement:', error);
    }
  }, [error]);

  return (
    <div className="p-4 bg-gray-50">
      <div className="flex justify-between items-center md:flex-row flex-col p-2 mb-2 border border-gray-200 bg-white rounded-2xl">
        <h1 className="text-3xl p-3 font-bold text-gray-800">Unit of Measurement</h1>
        <div className="flex md:flex-row flex-col-reverse gap-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Unit of Measurement"
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchTerm}
              onChange={handleSearch}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400 h-5 w-5" />
          </div>
          <button
            onClick={handleAddUnit}
            className="bg-orange-500 hover:bg-orange-600 text-white font-medium px-4 py-2 rounded-lg flex items-center gap-1"
          >
            Add Unit of Measurement
            <Plus size={20} />
          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-start bg-white h-screen pt-[20vh]">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
            <p className="text-gray-600 font-medium">Loading Unit of measurement...</p>
          </div>
        </div>
      ) : error ? (
        <p className="text-center text-red-500 py-8">Failed to load units of measurement.</p>
      ) : (
        <>
          <div className="bg-white shadow-sm border border-gray-200 rounded-2xl">
            <div className="overflow-x-auto rounded-2xl">
              <table className="w-full">
                <thead className="bg-orange-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">
                      No
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">
                      Code
                    </th>
                    <th className="px-6 py-3 text-right text-sm font-semibold text-gray-700">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {currentUnits.length > 0 ? (
                    currentUnits.map((unit, index) => (
                      <tr key={unit.id} className="hover:bg-orange-50">
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {indexOfFirstItem + index + 1}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {unit.name}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {unit.code}
                        </td>
                        <td className="px-6 py-4 text-sm flex gap-2 justify-end">
                          <button
                            onClick={() => handleEdit(unit.id)}
                            className="text-blue-500 hover:text-blue-700"
                            disabled={isDeleting}
                          >
                            <Edit
                              id="edit-icon"
                              data-tooltip-id="edit-tooltip"
                              data-tooltip-content="Edit"
                              size={20}
                              className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                            />
                            <Tooltip
                              id="edit-tooltip"
                              place="bottom"
                              className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                            />
                          </button>
                          <button
                            onClick={() => handleDelete(unit.id)}
                            className={`text-red-500 hover:text-red-700 ${isDeleting ? "cursor-not-allowed opacity-50" : ""}`}
                            disabled={isDeleting}
                          >
                            <Trash2
                              id="delete-icon"
                              data-tooltip-id="delete-tooltip"
                              data-tooltip-content="Delete"
                              size={20}
                              className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                            />
                            <Tooltip
                              id="delete-tooltip"
                              place="bottom"
                              className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                            />
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                        No units of measurement found.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Pagination */}
          {filteredUnits.length > 0 && (
            <div className="flex justify-start items-center space-x-2 p-4">
              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  currentPage === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft size={18} className="text-gray-600" />
              </button>

              {Array.from(
                { length: Math.ceil(filteredUnits.length / itemsPerPage) },
                (_, i) => i + 1
              ).map((number) => (
                <button
                  key={number}
                  onClick={() => paginate(number)}
                  className={`w-8 h-8 rounded-md flex items-center justify-center border ${
                    currentPage === number
                      ? "bg-orange-500 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  {number}
                </button>
              ))}

              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  indexOfLastItem >= filteredUnits.length
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(currentPage + 1)}
                disabled={indexOfLastItem >= filteredUnits.length}
              >
                <ChevronRight size={18} className="text-gray-600" />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}