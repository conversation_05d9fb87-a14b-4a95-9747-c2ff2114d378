import React, { useState } from "react";
import CustomModal from "../../CustomModal";
import { IoReceiptOutline } from "react-icons/io5";
import { useGetEmployeesQuery, useGetCustomersQuery } from "../../../store/api/pos/customer";
// import XReportSearchResults from "./XReportSearchResults";
import { ChevronDown } from "lucide-react";
import XReportSearchResults from "./XReportSearchResults";

interface XReportSalesSummaryProps {
  isOpen: boolean;
  onClose: () => void;
}

// Define the type for employee data from the API
type Employee = {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNo: string;
  address: string;
  employeeId: string;
  userId: string;
  role: string;
  hourlyRate: number;
  employeeType: string;
  employeeStartTime: string;
  employeeEndTime: string;
  totalHours: number;
  overTimeRate: number;
  startDate: string;
  createdAt: string;
  updatedAt: string;
  [key: string]: any; // For any additional properties
}

const XReportSalesSummary: React.FC<XReportSalesSummaryProps> = ({
  isOpen,
  onClose,
}) => {
  const [timeRange, setTimeRange] = useState("Today");

  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [startTime, setStartTime] = useState("");
  const [endTime, setEndTime] = useState("");
  const [showOnlySelectedCustomer, setShowOnlySelectedCustomer] = useState(false);
  const [selectedEmployeeIds, setSelectedEmployeeIds] = useState<string[]>([]);

  // Customer selection state
  const [isCustomerDropdownOpen, setIsCustomerDropdownOpen] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [selectedCustomerName, setSelectedCustomerName] = useState<string>("");

  // Custom date range flag - used in handleSearch
  const [isCustomDateRange, setIsCustomDateRange] = useState(false);

  // State for search results modal
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchResultParams, setSearchResultParams] = useState<any>(null);

  const userId = localStorage.getItem("userId") || "";
  const { data: employees, isLoading, error } = useGetEmployeesQuery(userId) as {
    data: Employee[] | undefined;
    isLoading: boolean;
    error: any
  };

  // Fetch customers data
  const { data: customers, isLoading: isLoadingCustomers } = useGetCustomersQuery(userId);

  // Handle customer selection
  const handleCustomerSelect = (customer: any) => {
    setSelectedCustomerId(customer.id);
    setSelectedCustomerName(`${customer.firstName} ${customer.lastName}`);
    setIsCustomerDropdownOpen(false);
  };

  // Handle date range change
  const handleDateRangeChange = () => {
    // If both dates are set and timeRange is Custom, set isCustomDateRange flag
    if (startDate && endDate && timeRange === "Custom") {
      setIsCustomDateRange(true);
    }
  };

  // Clear customer selection
  const clearCustomerSelection = () => {
    setSelectedCustomerId(null);
    setSelectedCustomerName("");
  };

  const handleSearch = () => {
    // Get the selected employee details if any employees are selected
    const selectedEmployees = employees?.filter(emp => selectedEmployeeIds.includes(emp._id)) || [];

    console.log("Selected employees:", selectedEmployees);
    console.log("Time range:", timeRange);
    console.log("Date range:", { startDate, endDate });
    console.log("Time selection:", { startTime, endTime });
    console.log("Selected customer:", selectedCustomerId);

    // Make sure isCustomDateRange is set if we have dates and Custom timeRange
    if (startDate && endDate && timeRange === "Custom") {
      setIsCustomDateRange(true);
    }

    // Prepare search parameters for the results modal
    const searchParams = {
      timeRange,
      employee: selectedEmployees.length > 0
        ? selectedEmployees.map(emp => `${emp.firstName} ${emp.lastName}`).join(', ')
        : 'Admin',
      employeeId: selectedEmployees.length === 1 ? selectedEmployees[0]._id : null,
      selectedEmployees: selectedEmployees.map(emp => ({
        _id: emp._id,
        firstName: emp.firstName,
        lastName: emp.lastName
      })),
      dateRange: startDate && endDate ? { startDate, endDate } : null,
      timeSelection: startTime && endTime ? { startTime, endTime } : null,
      showOnlySelectedCustomer,
      selectedCustomerId,
      isCustomDateRange
    };

    console.log('Search with parameters:', searchParams);

    // Set the search parameters and show the search results modal
    setSearchResultParams(searchParams);
    setShowSearchResults(true);
  };

  // Function to close the search results modal
  const handleCloseSearchResults = () => {
    setShowSearchResults(false);
  };

  const handleEmployeeSelection = (employeeId: string) => {
    setSelectedEmployeeIds(prev => {
      if (prev.includes(employeeId)) {
        return prev.filter(id => id !== employeeId);
      } else {
        return [...prev, employeeId];
      }
    });
  };

  return (
    <>
      <CustomModal
        isOpen={isOpen}
        onClose={onClose}
        title="X Report-Sales Summary"
        width="max-w-6xl"
      >
        <div className="p-6">
          {/* Top Filters */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex gap-4">
              <select
                value={timeRange}
                onChange={(e) => {
                  setTimeRange(e.target.value);
                  // If Custom is selected, set isCustomDateRange flag
                  if (e.target.value === "Custom") {
                    setIsCustomDateRange(true);
                  } else {
                    setIsCustomDateRange(false);
                  }
                }}
                className="border border-orange text-orange rounded-full px-4 py-2"
              >
                <option>Today</option>
                <option>Yesterday</option>
                <option>This Week</option>
                <option>This Month</option>
                <option>Custom</option>
              </select>

              {/* Customer Selection Dropdown */}
              <div className="relative">
                <div
                  className="border border-orange text-orange rounded-full px-4 py-2 flex items-center gap-2 cursor-pointer"
                  onClick={() => setIsCustomerDropdownOpen(!isCustomerDropdownOpen)}
                >
                  <span>{selectedCustomerName || "Select Customer"}</span>
                  <ChevronDown size={16} />
                </div>

                {isCustomerDropdownOpen && (
                  <div className="absolute z-50 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                    <div
                      className="px-4 py-2 hover:bg-gray-100 cursor-pointer border-b"
                      onClick={clearCustomerSelection}
                    >
                      <span className="text-gray-500">Clear Selection</span>
                    </div>

                    {isLoadingCustomers ? (
                      <div className="px-4 py-2 text-gray-500">Loading customers...</div>
                    ) : customers && customers.length > 0 ? (
                      customers.map((customer: any) => (
                        <div
                          key={customer.id}
                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                          onClick={() => handleCustomerSelect(customer)}
                        >
                          {customer.firstName} {customer.lastName}
                        </div>
                      ))
                    ) : (
                      <div className="px-4 py-2 text-gray-500">No customers found</div>
                    )}
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex gap-2 items-center">
                <input
                  type="date"
                  className="border border-gray-300 rounded-md p-2"
                  value={startDate}
                  onChange={(e) => {
                    setStartDate(e.target.value);
                    handleDateRangeChange();
                  }}
                  placeholder="Start date"
                />
                <span className="text-gray-400">→</span>
                <input
                  type="date"
                  className="border border-gray-300 rounded-md p-2"
                  value={endDate}
                  onChange={(e) => {
                    setEndDate(e.target.value);
                    handleDateRangeChange();
                  }}
                  placeholder="End date"
                />
              </div>
              <div className="flex gap-2 items-center">
                <input
                  type="time"
                  className="border border-gray-300 rounded-md p-2"
                  value={startTime}
                  onChange={(e) => setStartTime(e.target.value)}
                  placeholder="Start Time"
                />
                <span className="text-gray-400">→</span>
                <input
                  type="time"
                  className="border border-gray-300 rounded-md p-2"
                  value={endTime}
                  onChange={(e) => setEndTime(e.target.value)}
                  placeholder="End Time"
                />
              </div>
            </div>
          </div>

          {/* Show Only Selected Customer Toggle */}
          <div className="flex items-center justify-between p-4 border border-[#E4E4E4] rounded-lg mb-4 bg-gray-50">
            <div className="flex items-center gap-2">
              <IoReceiptOutline className="text-gray-500" size={20} />
              <span className="text-gray-500">Show only Selected customer</span>
            </div>
            <input
              type="checkbox"
              checked={showOnlySelectedCustomer}
              onChange={(e) => setShowOnlySelectedCustomer(e.target.checked)}
              className="w-5 h-5 rounded-full"
            />
          </div>

          {/* Employees Grid */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            {isLoading ? (
              <div className="col-span-2 text-center py-4">Loading employees...</div>
            ) : error ? (
              <div className="col-span-2 text-center py-4 text-red-500">Error loading employees</div>
            ) : employees && employees.length > 0 ? (
              employees.map((employee) => (
                <div
                  key={employee._id}
                  className={`flex items-center justify-between p-4 border border-[#E4E4E4] rounded-lg ${!showOnlySelectedCustomer ? 'bg-gray-50' : 'bg-white'}`}
                >
                  <div className="flex items-center gap-2">
                    <IoReceiptOutline className="text-gray-500" size={20} />
                    <span className="text-gray-500">{`${employee.firstName} ${employee.lastName}`}</span>
                  </div>
                  <input
                    type="checkbox"
                    className="w-5 h-5 rounded-full"
                    checked={selectedEmployeeIds.includes(employee._id)}
                    onChange={() => handleEmployeeSelection(employee._id)}
                    disabled={!showOnlySelectedCustomer}
                  />
                </div>
              ))
            ) : (
              <div className="col-span-2 text-center py-4">No employees found</div>
            )}
          </div>

          {/* Show Products Toggle */}


          {/* Action Buttons */}
          <div className="flex justify-end gap-4 font-bold">
            <button
              onClick={handleSearch}
              className="px-14 py-3 bg-orange text-white rounded-full cursor-pointer "
            >
              Search
            </button>
            <button
              onClick={onClose}
              className="px-14 py-3 border border-orange text-orange rounded-full cursor-pointer"
            >
              Cancel
            </button>
          </div>
        </div>
      </CustomModal>

      {/* Search Results Modal */}
      {showSearchResults && searchResultParams && (
        <XReportSearchResults
          isOpen={showSearchResults}
          onClose={handleCloseSearchResults}
          searchParams={searchResultParams}
        />
      )}
    </>
  );
};

export default XReportSalesSummary;
