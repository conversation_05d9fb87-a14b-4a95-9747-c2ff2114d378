import React, { useState, useEffect } from "react";
import CustomModal from "../../CustomModal";
import { FiSearch } from "react-icons/fi";
import { IoCheckmarkCircleOutline } from "react-icons/io5";
import { useGetDueOrdersQuery, useUpdateOrderItemMutation } from "../../../store/api/pos/orderapi";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Swal from "sweetalert2";

interface ReceivedPaymentsProps {
  isOpen: boolean;
  onClose: () => void;
}

const ReceivedPayments: React.FC<ReceivedPaymentsProps> = ({
  isOpen,
  onClose,
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [processingOrderId, setProcessingOrderId] = useState<string | null>(null);
  const itemsPerPage = 5; // Changed from 10 to 5

  const userId = localStorage.getItem("userId") || "";
  const { data, isLoading, error } = useGetDueOrdersQuery(userId);
  const [updateOrderItem] = useUpdateOrderItemMutation();

  // Reset pagination when search term or date range changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, startDate, endDate]);

  // Calculate total pages based on filtered data
  const getTotalPages = () => {
    if (!data || !Array.isArray(data)) return 1;

    const filteredData = data.filter(order => {
      // Filter by search term
      if (searchTerm) {
        const searchTermLower = searchTerm.toLowerCase().trim();
        
        const matchesSearch =
          // Search in OrderNumber (receipt number)
          (order.OrderNumber && order.OrderNumber.toLowerCase().includes(searchTermLower)) ||
          // Search in customerName (handles full names with spaces)
          (order.customerName && order.customerName.toLowerCase().includes(searchTermLower)) ||
          // Additional search: split search term and check if all parts match
          (order.customerName && searchTermLower.split(' ').every(term => 
            order.customerName.toLowerCase().includes(term)
          ));

        if (!matchesSearch) return false;
      }

      // Filter by date range
      if (startDate && endDate && order.createdAt) {
        try {
          const orderDate = new Date(order.createdAt);
          if (orderDate < startDate || orderDate > endDate) {
            return false;
          }
        } catch (e) {
          return false;
        }
      }

      return true;
    });

    return Math.ceil(filteredData.length / itemsPerPage);
  };

  const totalPages = getTotalPages();

  const handlePrevPage = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };

  const handleDateChange = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    setStartDate(start);
    setEndDate(end);
  };

  const clearDateFilter = () => {
    setStartDate(null);
    setEndDate(null);
  };

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage === 1 ? 'text-gray-400' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={handlePrevPage}
          disabled={currentPage === 1}
          type="button"
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage === totalPages || totalPages === 0 ? 'text-gray-400' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={handleNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
          type="button"
        >
          Next →
        </button>
      </div>
      <button
        onClick={onClose}
        className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
        type="button"
      >
        Cancel
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Received Payments"
      width="max-w-6xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search and Filter Bar */}
        <div className="flex mb-6 border border-[#E4E4E4] rounded-lg overflow-hidden">
          {/* Search Input */}
          <div className="flex-1 relative flex items-center px-4 py-2">
            <div className="flex-1 flex items-center">
              <FiSearch className="text-black mr-2" size={20} />
              <input
                type="text"
                placeholder="Search by Receipt Number or Customer Name"
                className="w-full py-2 focus:outline-none text-base"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              )}
            </div>
            <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
          </div>
          <div className="relative mx-15 flex items-center">
            <button
              onClick={clearDateFilter}
              className={`px-4 py-2 text-sm ${startDate || endDate ? 'text-orange-500' : 'text-gray-400'}`}
              disabled={!startDate && !endDate}
              type="button"
            >
              Clear
            </button>
          </div>
          {/* Date Range Picker */}
          <div className="px-4 py-2 flex items-center">
            <DatePicker
              selected={startDate}
              onChange={handleDateChange}
              startDate={startDate}
              endDate={endDate}
              selectsRange
              className="py-2 focus:outline-none text-base"
              placeholderText="Select Date Range"
              dateFormat="MM/dd/yyyy"
            />
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left border-b border-gray-200">
                <th className="pb-3 text-gray-500 font-normal">
                  Receipt Number
                </th>
                <th className="pb-3 text-gray-500 font-normal">
                  Receipt Total
                </th>
                <th className="pb-3 text-gray-500 font-normal">Due Amount</th>
                <th className="pb-3 text-gray-500 font-normal">Customer</th>
                <th className="pb-3 text-gray-500 font-normal">Date</th>
                <th className="pb-3 text-gray-500 font-normal">Actions</th>
              </tr>
            </thead>
            <tbody>
              {(() => {
                if (isLoading) {
                  return (
                    <tr>
                      <td colSpan={6} className="py-8 text-center">
                        <div className="flex justify-center items-center">
                          <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
                          <div className="ml-3 text-orange-500 font-medium">
                            Loading...
                          </div>
                        </div>
                      </td>
                    </tr>
                  );
                }

                if (error) {
                  return (
                    <tr>
                      <td colSpan={6} className="py-4 text-center text-red-500">
                        Error loading data: {(error as any)?.data?.message || 'Unknown error'}
                      </td>
                    </tr>
                  );
                }

                if (!data) {
                  return (
                    <tr>
                      <td colSpan={6} className="py-4 text-center">No data received from API</td>
                    </tr>
                  );
                }

                if (!Array.isArray(data)) {
                  return (
                    <tr>
                      <td colSpan={6} className="py-4 text-center">Data is not an array: {JSON.stringify(data)}</td>
                    </tr>
                  );
                }

                if (data.length === 0) {
                  return (
                    <tr>
                      <td colSpan={6} className="py-4 text-center">No Data was Found</td>
                    </tr>
                  );
                }

                // Filter data based on search term and date range
                const filteredData = data.filter(order => {
                  // Filter by search term
                  if (searchTerm) {
                    const searchTermLower = searchTerm.toLowerCase().trim();
                    
                    const matchesSearch =
                      // Search in OrderNumber (receipt number)
                      (order.OrderNumber && order.OrderNumber.toLowerCase().includes(searchTermLower)) ||
                      // Search in customerName (handles full names with spaces)
                      (order.customerName && order.customerName.toLowerCase().includes(searchTermLower)) ||
                      // Additional search: split search term and check if all parts match
                      (order.customerName && searchTermLower.split(' ').every(term => 
                        order.customerName.toLowerCase().includes(term)
                      ));

                    if (!matchesSearch) return false;
                  }

                  // Filter by date range
                  if (startDate && endDate && order.createdAt) {
                    try {
                      const orderDate = new Date(order.createdAt);
                      if (orderDate < startDate || orderDate > endDate) {
                        return false;
                      }
                    } catch (e) {
                      console.log("Error parsing date:", e);
                      return false;
                    }
                  }

                  return true;
                });

                // Get current page items
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = Math.min(startIndex + itemsPerPage, filteredData.length);
                const currentPageData = filteredData.slice(startIndex, endIndex);

                // Render the current page data
                return currentPageData.map((order: any) => (
                  <tr key={order._id} className="border-b border-gray-200">
                    <td className="py-4">{order.OrderNumber || 'N/A'}</td>
                    <td className="py-4">${(order.grandTotal || 0).toFixed(2)}</td>
                    <td className="py-4">${(order.dueamount || 0).toFixed(2)}</td>
                    <td className="py-4">{order.customerName || 'Guest'}</td>
                    <td className="py-4">{order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'N/A'}</td>
                    <td className="py-4 text-center">
                      {(order.dueamount || 0) > 0 ? (
                        <div
                          className={`inline-flex items-center space-x-2 rounded-md px-3 py-1 ${processingOrderId === order._id
                            ? "bg-green-50 text-green-400 cursor-wait"
                            : "text-green-500 cursor-pointer hover:bg-green-50"
                            }`}
                          onClick={async () => {
                            if (processingOrderId) return; // Prevent multiple clicks

                            try {
                              setProcessingOrderId(order._id);

                              // Prepare the data for the API call
                              const updatedData = {
                                _id: order._id,
                                dueamount: 0,
                                recieveamount: order.grandTotal || 0,
                                userId: userId,
                                PaymentStatus: 'completed'
                              };

                              // Call the API to update the order
                              await updateOrderItem({
                                id: order._id,
                                body: updatedData
                              }).unwrap();

                              // Show success message with SweetAlert2
                              Swal.fire({
                                icon: 'success',
                                title: 'Payment Processed',
                                text: `Payment of $${(order.grandTotal || 0).toFixed(2)} has been processed successfully!`,
                                confirmButtonColor: '#FF9800',
                                timer: 3000,
                                timerProgressBar: true
                              });

                              // The API call will automatically invalidate the cache and refresh the data
                            } catch (error) {
                              console.error("Error updating order:", error);

                              // Show error message with SweetAlert2
                              Swal.fire({
                                icon: 'error',
                                title: 'Payment Failed',
                                text: 'There was an error processing the payment. Please try again.',
                                confirmButtonColor: '#FF9800'
                              });
                            } finally {
                              setProcessingOrderId(null);
                            }
                          }}
                        >
                          {processingOrderId === order._id ? (
                            <>
                              <div className="w-5 h-5 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
                              <span className="font-medium">Processing...</span>
                            </>
                          ) : (
                            <div className="group relative flex items-center space-x-2">
                              <IoCheckmarkCircleOutline className="w-5 h-5" />
                              <span className="font-medium">Pay</span>
                              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none">
                                Pay Amount
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="inline-flex items-center px-4 py-1 bg-green-100 text-green-600 rounded-full text-sm">
                          Completed
                        </span>
                      )}
                    </td>
                  </tr>
                ));
              })()}
            </tbody>
          </table>
        </div>
      </div>
    </CustomModal>
  );
};

export default ReceivedPayments;