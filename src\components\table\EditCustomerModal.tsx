import React from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import CustomModal from "../CustomModal";
import { useUpdateCustomerMutation } from "../../store/api/pos/customer";
import Swal from 'sweetalert2';

interface EditCustomerProps {
  isOpen: boolean;
  onClose: () => void;
  customerData: any;
}

interface CustomerFormValues {
  FirstName: string;
  LastName: string;
  Phone: string;
  Address: string;
  City: string;
  State: string;
  Email: string;
  isActive: boolean;
}

// Phone regex pattern for North American format: (************* or ************ or 1234567890
const phoneRegExp = /^(\+?\d{0,2})?[\s\-]?\(?\d{3}\)?[\s\-]?\d{3}[\s\-]?\d{4}$/;

const validationSchema = Yup.object({
  FirstName: Yup.string()
    .min(2, "First name must be at least 2 characters")
    .required("First name is required"),
  LastName: Yup.string()
    .min(2, "Last name must be at least 2 characters")
    .required("Last name is required"),
  Phone: Yup.string()
    .matches(phoneRegExp, "Phone number is not valid")
    .required("Phone number is required"),
  Address: Yup.string().required("Address is required"),
  City: Yup.string().required("City is required"),
  State: Yup.string().required("State is required"),
  Email: Yup.string()
    .email("Invalid email format")
    .required("Email is required"),
});

// Custom error message component with better styling
const ErrorMessageWrapper = ({ children }: { children: React.ReactNode }) => (
  <div className="flex items-center text-red-500 text-xs mt-1 ml-2 bg-red-50 px-2 py-1 rounded-md">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-3 w-3 mr-1"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
      />
    </svg>
    {children}
  </div>
);

const EditCustomerModal: React.FC<EditCustomerProps> = ({ isOpen, onClose, customerData }) => {
  const [updateCustomer] = useUpdateCustomerMutation();

  // Prepare initial values from customer data
  const initialValues: CustomerFormValues = {
    FirstName: customerData?.FirstName || customerData?.firstName || "",
    LastName: customerData?.LastName || customerData?.lastName || "",
    Phone: customerData?.Phone || customerData?.phone || "",
    Address: customerData?.Address || customerData?.address || "",
    City: customerData?.City || customerData?.city || "",
    State: customerData?.State || customerData?.state || "",
    Email: customerData?.Email || customerData?.email || "",
    isActive: customerData?.isActive ?? true,
  };

  const handleSubmit = async (values: CustomerFormValues) => {
    try {
      let timerInterval: ReturnType<typeof setInterval>;
      Swal.fire({
        title: "Updating customer...",
        html: "Please wait while we update the customer information.",
        timer: 2000,
        timerProgressBar: true,
        didOpen: () => {
          Swal.showLoading();
          const timer = Swal?.getPopup()?.querySelector("b");
          timerInterval = setInterval(() => {
            if (timer) {
              timer.textContent = `${Swal.getTimerLeft()}`;
            }
          }, 100);
        },
        willClose: () => {
          clearInterval(timerInterval);
        }
      });

      // Prepare the update data
      const updateData = {
        _id: customerData._id || customerData.id,
        data: {
          FirstName: values.FirstName,
          LastName: values.LastName,
          Phone: values.Phone,
          Address: values.Address,
          City: values.City,
          State: values.State,
          Email: values.Email,
          isActive: values.isActive,
        }
      };

      console.log("Updating customer with data:", updateData);
      await updateCustomer(updateData).unwrap();

      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: 'Customer updated successfully',
        timer: 2000,
        showConfirmButton: false,
      });

      onClose();
    } catch (error) {
      console.error("Error updating customer:", error);
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: 'Failed to update customer. Please try again.',
        showConfirmButton: true,
      });
    }
  };

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Edit Customer"
      width="max-w-4xl"
      footer={null}
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize={true}
      >
        {({ isSubmitting, errors, touched }) => (
          <Form>
            <div className="p-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block font-semibold mb-1 text-sm text-[#9C9C9C]">
                    Customer ID
                  </label>
                  <input
                    className="w-full p-3 px-4 bg-[#E4E4E4] border border-[#E4E4E4] rounded-full focus:outline-none"
                    value={customerData?.CustomerId || customerData?.id || ""}
                    disabled
                    readOnly
                  />
                </div>
                <div>
                  <label className="block font-semibold mb-1 text-sm text-[#9C9C9C]">
                    First Name
                  </label>
                  <Field
                    name="FirstName"
                    className={`w-full p-3 px-4 border ${errors.FirstName && touched.FirstName
                      ? "border-red-500"
                      : "border-[#E4E4E4]"
                      } rounded-full focus:outline-none`}
                    placeholder="Enter First Name"
                  />
                  <ErrorMessage
                    name="FirstName"
                    render={(msg: any) => <ErrorMessageWrapper>{msg}</ErrorMessageWrapper>}
                  />
                </div>
                <div>
                  <label className="block font-semibold mb-1 text-sm text-[#9C9C9C]">
                    Last Name
                  </label>
                  <Field
                    name="LastName"
                    className={`w-full p-3 px-4 border ${errors.LastName && touched.LastName
                      ? "border-red-500"
                      : "border-[#E4E4E4]"
                      } rounded-full focus:outline-none`}
                    placeholder="Enter Last Name"
                  />
                  <ErrorMessage
                    name="LastName"
                    render={(msg: any) => <ErrorMessageWrapper>{msg}</ErrorMessageWrapper>}
                  />
                </div>
                <div>
                  <label className="block font-semibold mb-1 text-sm text-[#9C9C9C]">
                    Phone Number
                  </label>
                  <Field
                    name="Phone"
                    className={`w-full p-3 px-4 border ${errors.Phone && touched.Phone
                      ? "border-red-500"
                      : "border-[#E4E4E4]"
                      } rounded-full focus:outline-none`}
                    placeholder="Enter Phone Number (e.g. ************)"
                  />
                  <ErrorMessage
                    name="Phone"
                    render={(msg: any) => <ErrorMessageWrapper>{msg}</ErrorMessageWrapper>}
                  />
                </div>
                <div>
                  <label className="block font-semibold mb-1 text-sm text-[#9C9C9C]">
                    Address
                  </label>
                  <Field
                    name="Address"
                    className={`w-full p-2 px-4 border ${errors.Address && touched.Address
                      ? "border-red-500"
                      : "border-[#E4E4E4]"
                      } rounded-full focus:outline-none`}
                    placeholder="Enter Address"
                  />
                  <ErrorMessage
                    name="Address"
                    render={(msg: any) => <ErrorMessageWrapper>{msg}</ErrorMessageWrapper>}
                  />
                </div>
                <div>
                  <label className="block font-semibold mb-1 text-sm text-[#9C9C9C]">
                    City
                  </label>
                  <Field
                    name="City"
                    className={`w-full p-3 px-4 border ${errors.City && touched.City
                      ? "border-red-500"
                      : "border-[#E4E4E4]"
                      } rounded-full focus:outline-none`}
                    placeholder="Enter City"
                  />
                  <ErrorMessage
                    name="City"
                    render={(msg: any) => <ErrorMessageWrapper>{msg}</ErrorMessageWrapper>}
                  />
                </div>
                <div>
                  <label className="block font-semibold mb-1 text-sm text-[#9C9C9C]">
                    State / Province
                  </label>
                  <Field
                    name="State"
                    className={`w-full p-2 px-4 border ${errors.State && touched.State
                      ? "border-red-500"
                      : "border-[#E4E4E4]"
                      } rounded-full focus:outline-none`}
                    placeholder="Enter State"
                  />
                  <ErrorMessage
                    name="State"
                    render={(msg: any) => <ErrorMessageWrapper>{msg}</ErrorMessageWrapper>}
                  />
                </div>
                <div>
                  <label className="block font-semibold mb-1 text-sm text-[#9C9C9C]">
                    Email
                  </label>
                  <Field
                    name="Email"
                    type="email"
                    className={`w-full p-3 px-4 border ${errors.Email && touched.Email
                      ? "border-red-500"
                      : "border-[#E4E4E4]"
                      } rounded-full focus:outline-none`}
                    placeholder="Enter Email Address"
                  />
                  <ErrorMessage
                    name="Email"
                    render={(msg: any) => <ErrorMessageWrapper>{msg}</ErrorMessageWrapper>}
                  />
                </div>
              </div>

              <div className="mt-6">
                <label className="flex items-center space-x-2">
                  <Field
                    type="checkbox"
                    name="isActive"
                    className="form-checkbox accent-orange h-5 w-5"
                  />
                  <span className="text-sm text-[#9C9C9C] font-bold">
                    Is Active
                  </span>
                </label>
              </div>
            </div>

            <div className="px-6 pb-4">
              <div className="flex justify-center font-bold space-x-4 mt-1">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-10 cursor-pointer py-2 border border-orange-500 rounded-full text-orange-500 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-6 cursor-pointer py-2 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-colors disabled:bg-orange-300"
                >
                  Update Customer
                </button>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </CustomModal>
  );
};

export default EditCustomerModal;
