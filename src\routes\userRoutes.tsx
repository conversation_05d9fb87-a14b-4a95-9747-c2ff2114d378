import React from "react";
import { Route, Routes } from "react-router-dom";
import Home from "../pages/Home.tsx";
import Tables from "../pages/Table.tsx";
import OnBordingPage from "../pages/onBording.tsx"
import UserTypeSelection from "../pages/onBordingUserType.tsx";
import CreateAccount from "../pages/SignUp.tsx";
import LoginPage from "../pages/Login.tsx";

const UserRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<OnBordingPage />} />
      <Route path="/role-type" element={<UserTypeSelection />}/>
      <Route path="/create-account" element={<CreateAccount />} />
      <Route path="/auth" element={<LoginPage />} />
      <Route path="/pos/pos-dashboard" element={<Home />} />
      <Route path="/tables" element={<Tables />} />
    </Routes>
  );
};

export default UserRoutes;
