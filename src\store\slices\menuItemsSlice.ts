import { createSlice } from '@reduxjs/toolkit';
// import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { Product } from '../api/menuitemApi';
import { productsApi } from '../api/menuitemApi';

interface MenuState {
  items: Product[];
  isLoading: boolean;
  error: string | null;
}

const initialState: MenuState = {
  items: [],
  isLoading: false,
  error: null,
};

const menuSlice = createSlice({
  name: 'menu',
  initialState,
  reducers: {
    setMenuItems: (state, action) => {
      state.items = action.payload;
    },
    clearMenuItems: (state) => {
      state.items = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Get Menu Items
      .addMatcher(productsApi.endpoints.getProducts.matchPending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addMatcher(productsApi.endpoints.getProducts.matchFulfilled, (state, action) => {
        state.isLoading = false;
        state.items = action.payload;
      })
      .addMatcher(productsApi.endpoints.getProducts.matchRejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch menu items';
      })

      // Add Menu Item
      .addMatcher(productsApi.endpoints.postProduct.matchPending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addMatcher(productsApi.endpoints.postProduct.matchFulfilled, (state) => {
        state.isLoading = false;
      })
      .addMatcher(productsApi.endpoints.postProduct.matchRejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to add menu item';
      })

      // Update Menu Item
      .addMatcher(productsApi.endpoints.updateProduct.matchPending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addMatcher(productsApi.endpoints.updateProduct.matchFulfilled, (state) => {
        state.isLoading = false;
      })
      .addMatcher(productsApi.endpoints.updateProduct.matchRejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update menu item';
      })

      // Delete Menu Item
      .addMatcher(productsApi.endpoints.deleteProduct.matchPending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addMatcher(productsApi.endpoints.deleteProduct.matchFulfilled, (state, action) => {
        state.isLoading = false;
        const deletedId = action.meta.arg.originalArgs;
        state.items = state.items.filter((item) => item.id !== deletedId);
      })
      .addMatcher(productsApi.endpoints.deleteProduct.matchRejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to delete menu item';
      });
  },
});

export const { setMenuItems, clearMenuItems } = menuSlice.actions;

export const selectMenuItems = (state: RootState) => state.menuItems.items;
export const selectMenuLoading = (state: RootState) => state.menuItems.isLoading;
export const selectMenuError = (state: RootState) => state.menuItems.error;

export default menuSlice.reducer;
