import { useState, useRef, useEffect } from 'react';
import { Calendar, ChevronLeft, ChevronRight, Download, Trash2 } from 'lucide-react';

export default function TaxCollectedPage() {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const datePickerRef = useRef<HTMLDivElement | null>(null);

  // Sample tax data
  const taxData = [
    { id: 1, date: '24th October 2024', taxes: ['GST (5%)', 'VAT (3%)'], orders: 12, amount: 250.00 },
    { id: 2, date: '24th October 2024', taxes: ['GST (5%)', 'VAT (3%)'], orders: 12, amount: 250.00 },
    { id: 3, date: '24th October 2024', taxes: ['GST (5%)', 'VAT (3%)'], orders: 12, amount: 250.00 },
    { id: 4, date: '24th October 2024', taxes: ['GST (5%)', 'VAT (3%)'], orders: 12, amount: 250.00 },
  ];

  const toggleDatePicker = () => {
    setIsDatePickerOpen(!isDatePickerOpen);
  };

  const applyDates = () => {
    // Handle applying the selected dates
    setIsDatePickerOpen(false);
  };

  // Close datepicker when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
        setIsDatePickerOpen(false);
      }
    }
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [datePickerRef]);

  return (
    <div className="max-w-6xl mx-auto p-4">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-gray-700">Tax Collected</h1>
        <div className="flex gap-4">
          <div className="flex relative">
            <button 
              onClick={toggleDatePicker}
              className="flex items-center gap-2 px-4 py-2 bg-gray-100 rounded-l-lg border border-gray-300 text-gray-600"
            >
              {startDate ? startDate : 'Start date'} → {endDate ? endDate : 'End date'}
              <Calendar size={18} />
            </button>
            
            {/* Date Picker Dropdown */}
            {isDatePickerOpen && (
              <div 
                ref={datePickerRef}
                className="absolute top-12 right-0 bg-white p-4 rounded-lg shadow-lg border border-gray-200 z-10 w-96"
              >
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Start Date</label>
                    <div className="relative">
                      <input 
                        type="text" 
                        placeholder="mm/dd/yyyy" 
                        className="w-full p-2 border border-gray-300 rounded-md"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                      />
                      <button className="absolute right-3 top-2.5 text-gray-500">
                        <Calendar size={20} />
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">End Date</label>
                    <div className="relative">
                      <input 
                        type="text" 
                        placeholder="mm/dd/yyyy" 
                        className="w-full p-2 border border-gray-300 rounded-md"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                      />
                      <button className="absolute right-3 top-2.5 text-gray-500">
                        <Calendar size={20} />
                      </button>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-4 mt-4">
                  <button 
                    onClick={applyDates}
                    className="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-md flex-1"
                  >
                    Apply
                  </button>
                  <button 
                    onClick={() => setIsDatePickerOpen(false)}
                    className="border border-orange-300 text-orange-500 py-2 px-4 rounded-md flex-1"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </div>
          
          <button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center gap-2">
            <span>Download CSV</span>
            <Download size={18} />
          </button>
        </div>
      </div>

      {/* Table */}
      <div className=" rounded-lg overflow-hidden">
        {/* Table Header */}
        <div className="grid grid-cols-4 p-4 bg-orange-50 font-medium text-gray-700 border-b border-orange-100">
          <div className="flex items-center">
            <span className="mr-8">No</span>
            <span>Date</span>
          </div>
          <div>Taxes Applied</div>
          <div>Total Orders</div>
          <div>Amount</div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-gray-200">
          {taxData.map((item) => (
            <div key={item.id} className="grid grid-cols-4 p-4 items-center hover:bg-orange-50">
              <div className="flex items-center">
                <span className="w-8 text-gray-500">01</span>
                <span className="text-gray-700">{item.date}</span>
              </div>
              <div className="text-gray-700">
                {item.taxes.map((tax, index) => (
                  <span key={index}>
                    {tax}
                    {index < item.taxes.length - 1 && ' '}
                  </span>
                ))}
              </div>
              <div className="text-gray-700">{item.orders}</div>
              <div className="flex justify-between items-center">
                <span className="text-gray-700">${item.amount.toFixed(2)}</span>
                <button className="text-red-500 hover:text-red-700">
                  <Trash2 size={18} />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center  mt-8 gap-2">
        <button 
          className="p-2 border border-gray-300 rounded hover:bg-gray-100"
          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
          disabled={currentPage === 1}
        >
          <ChevronLeft size={16} />
        </button>
        <button className="h-8 w-8 bg-orange-500 text-white flex items-center justify-center rounded-md">
          1
        </button>
        <button 
          className="p-2 border border-gray-300 rounded hover:bg-gray-100"
          onClick={() => setCurrentPage(prev => prev + 1)}
        >
          <ChevronRight size={16} />
        </button>
      </div>

    </div>
  );
}