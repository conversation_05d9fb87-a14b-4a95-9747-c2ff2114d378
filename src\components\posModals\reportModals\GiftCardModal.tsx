import React, { useState } from 'react';
import { Search } from 'lucide-react';
import CustomModal from '../../CustomModal';

interface GiftCardModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (cardNumber: string, customerName: string, balance: number) => void;
}

interface GiftCard {
  cardNo: string;
  customerName: string;
  balance: number;
}

const GiftCardModal: React.FC<GiftCardModalProps> = ({ isOpen, onClose, onApply }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const cardsPerPage = 3;

  // Sample gift card data
  const giftCards: GiftCard[] = [
    { cardNo: '00937-1123-123', customerName: 'Jack Sparrow', balance: 45000 },
    { cardNo: '00937-1123-456', customerName: '<PERSON> Wick', balance: 50000 },
    { cardNo: '00937-1123-321', customerName: 'Jason Bourne', balance: 20000 },
  ];

  // Filter cards based on search term
  const filteredCards = giftCards.filter(card =>
    card.cardNo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    card.customerName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination
  const indexOfLastCard = currentPage * cardsPerPage;
  const indexOfFirstCard = indexOfLastCard - cardsPerPage;
  const currentCards = filteredCards.slice(indexOfFirstCard, indexOfLastCard);
  const totalPages = Math.ceil(filteredCards.length / cardsPerPage);

  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  const handleApply = (card: GiftCard) => {
    onApply(card.cardNo, card.customerName, card.balance);
    onClose();
  };

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Select Gift Card"
      width="max-w-2xl"
      zIndex={1000000}
    >
      <div className="p-6">
        {/* Search input */}
        <div className="relative mb-6">
          <div className="flex items-center border border-gray-200 rounded-full bg-white overflow-hidden">
            <Search className="ml-4 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-3 pl-2 focus:outline-none"
            />
          </div>
        </div>

        {/* Gift card table */}
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Card No</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Customer Name</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Balance</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentCards.map((card, index) => (
                <tr key={index} className="border-b border-gray-200">
                  <td className="px-4 py-5 text-sm text-gray-700">{card.cardNo}</td>
                  <td className="px-4 py-5 text-sm text-gray-700">{card.customerName}</td>
                  <td className="px-4 py-5 text-sm text-gray-700">${card.balance.toLocaleString()}</td>
                  <td className="px-4 py-5">
                    <button
                      onClick={() => handleApply(card)}
                      className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-full text-sm font-medium transition-colors"
                    >
                      Apply
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-8">
          <div className="flex items-center">
            <button
              onClick={handlePrevPage}
              disabled={currentPage === 1}
              className={`px-5 py-2 rounded-l-full border border-r-0 ${currentPage === 1
                ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                : 'border-gray-300 text-gray-700 hover:bg-gray-100'
                }`}
            >
              ← Previous
            </button>
            <div className="w-12 h-10 flex items-center justify-center border-t border-b border-gray-300 bg-white">
              {currentPage}
            </div>
            <button
              onClick={handleNextPage}
              disabled={currentPage === totalPages || totalPages === 0}
              className={`px-5 py-2 rounded-r-full border border-l-0 ${currentPage === totalPages || totalPages === 0
                ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                : 'border-gray-300 text-gray-700 hover:bg-gray-100'
                }`}
            >
              Next →
            </button>
          </div>
          <button
            onClick={onClose}
            className="px-8 py-3 bg-white border border-gray-200 text-orange-500 rounded-full hover:bg-gray-50 font-medium"
          >
            Cancel
          </button>
        </div>
      </div>
    </CustomModal>
  );
};

export default GiftCardModal;