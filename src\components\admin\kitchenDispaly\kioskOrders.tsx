import { useState } from 'react';

type OrderStatus = 'New' | 'Preparing' | 'Ready';

interface MenuItem {
  name: string;
  quantity: number;
  modifiers: {
    size: string;
    topping: string;
    notes?: string;
  };
}

interface Order {
  id: string;
  table: string;
  status: OrderStatus;
  items: MenuItem[];
}

export default function KioskOrders() {
  // Sample orders data
  const [orders, setOrders] = useState<Order[]>([
    {
      id: '001',
      table: 'Table#2A',
      status: 'New',
      items: [
        {
          name: 'Chicken Samosa',
          quantity: 1,
          modifiers: {
            size: 'Large',
            topping: 'Extra Garlic',
            notes: 'Please keep the spiciness low'
          }
        }
      ]
    },
    {
      id: '002',
      table: 'Table#2A',
      status: 'New',
      items: [
        {
          name: "Beef Rolls",
          quantity: 1,
          modifiers: {
            size: "Large",
            topping: "Extra Garlic",
            notes: "No onions please"
          }
        }   
      ]
    },
    {
      id: '003',
      table: 'Table#1B',
      status: 'Preparing',
      items: [
        {
          name: 'Pizza',
          quantity: 1,
          modifiers: {
            size: 'Medium',
            topping: 'Extra Cheese'
          }
        }
      ]
    },
    {
      id: '004',
      table: 'Table#3C',
      status: 'Ready',
      items: [
        {
          name: 'Burger',
          quantity: 1,
          modifiers: {
            size: 'Regular',
            topping: 'No Pickles'
          }
        }
      ]
    }
  ]);

  const [expandedOrders, setExpandedOrders] = useState<Record<string, boolean>>(() => {
    const initialExpanded: Record<string, boolean> = {};
    orders.forEach(order => {
      initialExpanded[order.id] = true; 
    });
    return initialExpanded;
  });
  
  const toggleOrder = (orderId: string) => {
    setExpandedOrders(prev => ({
      ...prev,
      [orderId]: !prev[orderId],
    }));
  };

  // Filter orders by status
  const newOrders = orders.filter(order => order.status === 'New');
  const preparingOrders = orders.filter(order => order.status === 'Preparing');
  const readyOrders = orders.filter(order => order.status === 'Ready');

  // Drag and drop functionality
  const [dragOrder, setDragOrder] = useState<string | null>(null);

  const handleDragStart = (e: React.DragEvent, orderId: string) => {
    setDragOrder(orderId);
    e.dataTransfer.setData('text/plain', orderId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = '#f9fafb';
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = '';
    }
  };

  const handleDrop = (e: React.DragEvent, newStatus: OrderStatus) => {
    e.preventDefault();
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = '';
    }
    
    const orderId = e.dataTransfer.getData('text/plain');
    const orderToMove = orders.find(order => order.id === orderId);
    
    if (orderToMove && orderToMove.status !== newStatus) {
      setOrders(prevOrders => prevOrders.map(order => 
        order.id === orderId ? { ...order, status: newStatus } : order
      ));
    }
    
    setDragOrder(null);
  };

  const handleDragEnd = () => {
    setDragOrder(null);
  };

  const changeOrderStatus = (orderId: string, newStatus: OrderStatus) => {
    setOrders(prevOrders => prevOrders.map(order => 
      order.id === orderId ? { ...order, status: newStatus } : order
    ));
  };

  const removeOrder = (orderId: string) => {
    setOrders(prevOrders => prevOrders.filter(o => o.id !== orderId));
  };

  return (
  <div className="flex flex-col bg-gray-50 p-2 h-screen">
    <div className="flex flex-1 gap-4 overflow-hidden">
      {/* New Orders Column */}
      <div
        className="flex-1 flex flex-col border border-gray-300 rounded-lg overflow-hidden h-[400px]"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={(e) => handleDrop(e, 'New')}
      >
        <div className="bg-orange-50 p-3 font-bold text-lg">
          New Orders ({newOrders.length})
        </div>
        <div className="flex-1 overflow-y-auto p-2 space-y-3 scrollbar-hide" style={{ maxHeight: "calc(100% - 56px)" }}>
          {newOrders.map(order => (
            <OrderCard
              key={order.id}
              order={order}
              expanded={expandedOrders[order.id]}
              onToggle={() => toggleOrder(order.id)}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              dragActive={dragOrder === order.id}
            >
              <button
                className="w-full py-2 bg-orange-500 text-white font-medium mt-2 rounded"
                onClick={() => changeOrderStatus(order.id, 'Preparing')}
              >
                Start Preparing
              </button>
            </OrderCard>
          ))}
        </div>
      </div>

      {/* Preparing Orders Column */}
      <div
        className="flex-1 flex flex-col border border-gray-300 rounded-lg overflow-hidden h-[400px]"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={(e) => handleDrop(e, 'Preparing')}
      >
        <div className="bg-orange-50 p-3 font-bold text-lg">
          Preparing ({preparingOrders.length})
        </div>
        <div className="flex-1 overflow-y-auto p-2 space-y-3 scrollbar-hide" style={{ maxHeight: "calc(100% - 56px)" }}>
          {preparingOrders.map(order => (
            <OrderCard
              key={order.id}
              order={order}
              expanded={expandedOrders[order.id]}
              onToggle={() => toggleOrder(order.id)}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              dragActive={dragOrder === order.id}
            >
              <button
                className="w-full py-2 bg-orange-500 text-white font-medium mt-2 rounded"
                onClick={() => changeOrderStatus(order.id, 'Ready')}
              >
                Mark as Ready
              </button>
            </OrderCard>
          ))}
        </div>
      </div>

      {/* Ready Orders Column */}
      <div
        className="flex-1 flex flex-col border border-gray-300 rounded-lg overflow-hidden h-[400px]"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={(e) => handleDrop(e, 'Ready')}
      >
        <div className="bg-orange-50 p-3 font-bold text-lg">
          Ready ({readyOrders.length})
        </div>
        <div className="flex-1 overflow-y-auto p-2 space-y-3 scrollbar-hide" style={{ maxHeight: "calc(100% - 56px)" }}>
          {readyOrders.map(order => (
            <OrderCard
              key={order.id}
              order={order}
              expanded={expandedOrders[order.id]}
              onToggle={() => toggleOrder(order.id)}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              dragActive={dragOrder === order.id}
            >
              <button
                className="w-full py-2 bg-orange-500 text-white font-medium mt-2 rounded"
                onClick={() => removeOrder(order.id)}
              >
                Mark as Done
              </button>
            </OrderCard>
          ))}
        </div>
      </div>
    </div>
  </div>
);
}

// Order Card Component
function OrderCard({ 
  order, 
  expanded, 
  onToggle, 
  onDragStart, 
  onDragEnd,
  dragActive,
  children 
}: {
  order: Order;
  expanded: boolean;
  onToggle: () => void;
  onDragStart: (e: React.DragEvent, id: string) => void;
  onDragEnd: () => void;
  dragActive: boolean;
  children: React.ReactNode;
}) {
  return (
    <div 
      className={`bg-white rounded-md shadow overflow-hidden cursor-move transition-opacity ${dragActive ? 'opacity-50' : ''}`}
      draggable="true"
      onDragStart={(e) => onDragStart(e, order.id)}
      onDragEnd={onDragEnd}
    >
      <div 
        className="flex justify-between items-center p-3 bg-gray-50 border-b border-gray-200"
        onClick={onToggle}
      >
        <div className="flex items-center">
          <div className="text-orange-500 mr-2">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <div>
            <span className="font-medium">#{order.id}</span>
            <span className="text-sm text-red-400 ml-2">{order.table}</span>
          </div>
        </div>
        <button className="text-gray-500">
          <svg 
            className={`w-5 h-5 transition-transform duration-200 ${expanded ? 'rotate-180' : ''}`}
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
      </div>
      
      {expanded && (
        <div className="p-3">
          {order.items.map((item, idx) => (
            <div key={idx} className="mb-3">
              <div className="flex justify-between">
                <span className="font-medium">{item.name}</span>
                <span className="text-gray-600">{item.quantity}x</span>
              </div>
              {item.modifiers.size && (
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Size:</span>
                  <span>{item.modifiers.size}</span>
                </div>
              )}
              {item.modifiers.topping && (
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Topping:</span>
                  <span>{item.modifiers.topping}</span>
                </div>
              )}
              {item.modifiers.notes && (
                <div className="text-sm text-gray-600 mt-1 italic">Note: {item.modifiers.notes}</div>
              )}
            </div>
          ))}
          {children}
        </div>
      )}
    </div>
  );
}