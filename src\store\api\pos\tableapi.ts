import { baseApi } from '../baseApi';

// Define interfaces for Table and Site


export const tableApi = baseApi.injectEndpoints({
    endpoints: (builder) => ({
        getTables: builder.query<any[], string>({
            query: (userId) => `/tables?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
        getSites: builder.query<any[], string>({
            query: (userId) => `/site?userId=${userId}`,
            providesTags: ['pos'],
            transformResponse: (response: any[]) => response,
        }),
    }),
    overrideExisting: false,
});

// Export hooks
export const {
    useGetTablesQuery,
    useGetSitesQuery,
} = tableApi;
