import { useState, useEffect } from 'react';
import { Edit, Trash2, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface PaymentItem {
  id: string;
  name: string;
  isActive: boolean | string;
  defaultPayment: string;
}
import {
  useGetPaymentTypesQuery,
  useDeletePaymentTypeMutation,
} from "../../../store/api/paymentTypesApi";
import { toast } from 'react-toastify';

export default function PaymentTypes() {
  const [showModal, setShowModal] = useState(false);
  const [paymentTypeToDelete, setPaymentTypeToDelete] = useState<string | null>(null);
  const navigate = useNavigate();
  const userId = localStorage.getItem('userId') || '';

  // Fetch payment types using the API
  const { data: paymentTypes = [], isLoading, error, refetch } = useGetPaymentTypesQuery(userId);
  const [deletePaymentType, { isLoading: isDeleting }] = useDeletePaymentTypeMutation();

  const handleDeletePaymentType = (id: string) => {
    setPaymentTypeToDelete(id);
    setShowModal(true);
  };

  const confirmDelete = async () => {
    if (paymentTypeToDelete) {
      try {
        await deletePaymentType(paymentTypeToDelete).unwrap();
        toast.success('Payment type deleted successfully');
        refetch();
        setShowModal(false);
      } catch (error) {
        console.error('Failed to delete payment type:', error);
        toast.error('Failed to delete payment type');
        setShowModal(false);
      }
    }
  };

  const handleEdit = (id: string) => {
    navigate(`/admin/administration/payment-types-form/${id}`);
  };

  const handleAddPaymentType = () => {
    navigate('/admin/administration/payment-types-form');
  };

  useEffect(() => {
    if (error) {
      toast.error('Failed to fetch payment types');
      console.error('Error fetching payment types:', error);
    }
  }, [error]);


  const getPaymentIcon = (type: PaymentItem) => {
    const name = type.name.toLowerCase();
    
    if (name.includes('stripe')) {
      return (
        <div className="bg-indigo-500 text-white rounded-lg p-3 flex items-center justify-center w-12 h-12">
          <span className="text-2xl font-bold">S</span>
        </div>
      );
    } else if (name.includes('terminal')) {
      return (
        <div className="bg-white rounded-lg p-2 flex items-center justify-center w-12 h-12">
          <div className="bg-gray-800 rounded-md p-1 w-full h-full flex items-center justify-center">
            <div className="bg-gray-700 w-6 h-1 mb-3"></div>
          </div>
        </div>
      );
    } else if (name.includes('cash')) {
      return (
        <div className="bg-green-500 text-white rounded-lg p-2 flex items-center justify-center w-12 h-12 relative">
          <span className="text-xl font-bold">$</span>
          <div className="absolute bottom-0 right-0 translate-x-1/4 translate-y-1/4">
            <div className="bg-yellow-400 rounded-full w-5 h-5 border-2 border-white"></div>
            <div className="bg-yellow-400 rounded-full w-4 h-4 border-2 border-white absolute -top-2 -left-1"></div>
          </div>
        </div>
      );
    } else {
     
      return (
        <div className="bg-gray-500 text-white rounded-lg p-3 flex items-center justify-center w-12 h-12">
          <span className="text-2xl font-bold">{type.name.charAt(0)}</span>
        </div>
      );
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen p-2">
      <div className="w-full">
        {/* Delete Confirmation Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-50">
            <div className="bg-white p-8 rounded-xl shadow-xl w-full max-w-md text-center relative">
              <div className="flex justify-center mb-4">
                <div className="bg-orange-100 text-orange-500 p-4 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M12 5c.512 0 1.023.195 1.414.586C13.805 6.977 14 7.488 14 8s-.195 1.023-.586 1.414A1.993 1.993 0 0112 10a1.993 1.993 0 01-1.414-.586A1.993 1.993 0 0110 8c0-.512.195-1.023.586-1.414A1.993 1.993 0 0112 5z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-2xl font-semibold text-gray-800">Are you sure?</h3>
              <p className="mt-2 text-gray-600">Do you want to delete this payment type?</p>
              <div className="mt-6 flex justify-center gap-4">
                <button
                  onClick={confirmDelete}
                  className="bg-orange-600 hover:bg-orange-700 text-white px-5 py-2 rounded-md font-medium"
                  disabled={isDeleting}
                >
                  Yes, delete it!
                </button>
                <button
                  onClick={() => setShowModal(false)}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-5 py-2 rounded-md font-medium"
                >
                  No, cancel!
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Payment Types</h1>
          <button
            onClick={handleAddPaymentType}
            className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            Add Payment Type
            <Plus size={20} />
          </button>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <p className="text-gray-600">Loading payment types...</p>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center h-40">
            <p className="text-red-500">Failed to load payment types. Please try again later.</p>
          </div>
        ) : paymentTypes.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <p className="text-gray-500">No payment types found.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {paymentTypes.map((type) => (
              <div key={type.id} className="bg-white rounded-lg shadow p-4 flex items-start">
                <div className="mr-4">{getPaymentIcon(type)}</div>
                <div className="flex-grow">
                  <h3 className="font-semibold text-gray-800">{type.name}</h3>
                  <p className={`text-sm ${String(type.isActive) === 'true' ? 'text-green-500' : 'text-gray-500'}`}>
                    {String(type.isActive) === 'true' ? 'Active' : 'Inactive'}
                  </p>
                  {type.defaultPayment === 'true' && (
                    <p className="text-sm text-blue-500">Default</p>
                  )}
                  <div className="flex mt-4 gap-4">
                    <button 
                      className="text-blue-500 hover:text-blue-700 flex items-center gap-1"
                      onClick={() => handleEdit(type.id)}
                    >
                      <Edit size={16} />
                      <span className="text-sm">Edit</span>
                    </button>
                    <button 
                      className="text-red-500 hover:text-red-700 flex items-center gap-1"
                      onClick={() => handleDeletePaymentType(type.id)}
                      disabled={isDeleting}
                    >
                      <Trash2 size={16} />
                      <span className="text-sm">Delete</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
