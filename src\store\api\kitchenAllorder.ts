import { baseApi } from "./baseApi";

export const kitchenOrdersApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getPoskitchendisplay: builder.query<any[], void>({
      query: () => {
        const userId = localStorage.getItem('userId');
        if (!userId) {
          throw new Error('User ID not found in localStorage');
        }
        return `/pos-kitchen-orders/?userId=${userId}`;
      },
      providesTags: ['PosKitchenOrders']
    }),
    
    getOnlinekitchendisplay: builder.query<any[], void>({
      query: () => {
        const userId = localStorage.getItem('userId');
        if (!userId) {
          throw new Error('User ID not found in localStorage');
        }
        return `/online-kitchen-orders?userId=${userId}`;
      },
      providesTags: ['OnlineKitchenOrders']
    }),
    
    updatePosOrderStatus: builder.mutation({
      query: ({ orderId, status }) => ({
        url: `/orderitem/${orderId}`,
        method: 'PUT',  
        body: status
      }),
      invalidatesTags: ['PosKitchenOrders']
    }),
    
    updateOnlineOrderStatus: builder.mutation({
      query: ({ orderId, status }) => ({
        url: `/sub-orderitem/${orderId}`,
        method: 'PUT',  
        body: status
      }),
      invalidatesTags: ['OnlineKitchenOrders']
    })
  }),
});

export const { 
  useGetPoskitchendisplayQuery,
  useGetOnlinekitchendisplayQuery,
  useUpdatePosOrderStatusMutation,
  useUpdateOnlineOrderStatusMutation
} = kitchenOrdersApi;