import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { SiteManagement } from '../api/siteManagementApi';
import { siteManagementApi } from '../api/siteManagementApi';

interface SiteManagementState {
  sites: SiteManagement[];
  isLoading: boolean;
  error: string | null;
}

const initialState: SiteManagementState = {
  sites: [],
  isLoading: false,
  error: null,
};

const siteManagementSlice = createSlice({
  name: 'siteManagement',
  initialState,
  reducers: {
    setSites: (state, action: PayloadAction<SiteManagement[]>) => {
      state.sites = action.payload;
    },
    clearSites: (state) => {
      state.sites = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET Sites List
      .addMatcher(
        siteManagementApi.endpoints.getSites.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.getSites.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.sites = payload;
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.getSites.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch sites';
        }
      )

      // GET Single Site
      .addMatcher(
        siteManagementApi.endpoints.getSite.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.getSite.matchFulfilled,
        (state) => {
          state.isLoading = false;
          // No need to update state since getSite is for a single site view
          // The data will be used directly from the query hook
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.getSite.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch site';
        }
      )

      // DELETE Site
      .addMatcher(
        siteManagementApi.endpoints.deleteSite.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.deleteSite.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const siteId = action.meta.arg.originalArgs as string;
          state.sites = state.sites.filter(
            (site) => site._id !== siteId
          );
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.deleteSite.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete site';
        }
      )

      // POST Site
      .addMatcher(
        siteManagementApi.endpoints.postSite.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.postSite.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          // Only push if we have valid data
          if (payload && payload._id) {
            state.sites.push({
              _id: payload._id,
              siteName: payload.siteName,
              numberOfTables: payload.numberOfTables,
              briefDescription: payload.briefDescription,
              isActive: payload.isActive,
              siteImage: payload.siteImage,
              userId: payload.userId,
            });
          }
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.postSite.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create site';
        }
      )

      // PUT Site
      .addMatcher(
        siteManagementApi.endpoints.putSite.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.putSite.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          
          // Handle the case where the payload contains the site data
          if (payload && payload._id) {
            const updated = {
              _id: payload._id,
              siteName: payload.siteName,
              numberOfTables: payload.numberOfTables,
              briefDescription: payload.briefDescription,
              isActive: payload.isActive,
              siteImage: payload.siteImage,
              userId: payload.userId,
              tables: payload.tables || [],
            };
            state.sites = state.sites.map((site) =>
              site._id === updated._id ? updated : site
            );
          } else if (payload && payload.data && payload.data._id) {
            // Handle the case where payload.data contains the site data
            const updated = {
              _id: payload.data._id,
              siteName: payload.data.siteName,
              numberOfTables: payload.data.numberOfTables,
              briefDescription: payload.data.briefDescription,
              isActive: payload.data.isActive,
              siteImage: payload.data.siteImage,
              userId: payload.data.userId,
              tables: payload.data.tables || [],
            };
            state.sites = state.sites.map((site) =>
              site._id === updated._id ? updated : site
            );
          } else {
            // If we get here, the payload structure is not as expected
            console.error('Invalid payload structure for site update:', payload);
          }
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.putSite.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update site';
        }
      )
      
      // Search Update Site
      .addMatcher(
        siteManagementApi.endpoints.searchUpdateSite.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.searchUpdateSite.matchFulfilled,
        (state) => {
          state.isLoading = false;
          // Since this will cause a refetch of the data via invalidateTags,
          // we don't need to manually update the state here
        }
      )
      .addMatcher(
        siteManagementApi.endpoints.searchUpdateSite.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update site by search';
        }
      );
  },
});

export const { setSites, clearSites } = siteManagementSlice.actions;

export const selectSites = (state: RootState) => state.siteManagement.sites;
export const selectSiteLoading = (state: RootState) => state.siteManagement.isLoading;
export const selectSiteError = (state: RootState) => state.siteManagement.error;

export default siteManagementSlice.reducer;