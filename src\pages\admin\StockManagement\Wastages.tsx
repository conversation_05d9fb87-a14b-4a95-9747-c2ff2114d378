import { useState, useEffect } from "react";
import { Eye, Trash2, ChevronLeft, ChevronRight, Plus, Search } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useGetWastagesQuery, useDeleteWastageMutation } from "../../../store/api/wastagesApi";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import Swal from "sweetalert2";

export default function Wastages() {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const navigate = useNavigate();
  const userId = localStorage.getItem('userId') || '';

  const { data: wastagesData, isLoading, error, refetch } = useGetWastagesQuery(userId);
  const [deleteWastage, { isLoading: isDeleting }] = useDeleteWastageMutation();

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleDelete = async (id: string) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this wastage record?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ea580c",
      cancelButtonColor: "#6b7280",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!"
    });

    if (result.isConfirmed) {
      try {
        await deleteWastage(id).unwrap();
        await Swal.fire(
          "Deleted!",
          "Wastage record has been deleted successfully.",
          "success"
        );
        refetch();
      } catch (error) {
        console.error('Failed to delete wastage:', error);
        Swal.fire("Error!", "Failed to delete wastage record.", "error");
      }
    }
  };

  const handleAddWastage = () => {
    navigate('/admin/stock-management/wastages/wastages-form');
  };

  const handleView = (id: string) => {
    navigate(`/admin/stock-management/wastages/wastage-details/${id}`);
  };

  // Filter wastages with null checks
  const filteredWastages = wastagesData?.filter(wastage => {
    const ingredientName = wastage.IngredientName?.IngredientName || '';
    const reason = wastage.ReasonOfWastage || '';
    
    return (
      ingredientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reason.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }) || [];

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentWastages = filteredWastages.slice(indexOfFirstItem, indexOfLastItem);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  useEffect(() => {
    if (error) {
      Swal.fire("Error!", "Failed to fetch wastages.", "error");
      console.error('Error fetching wastages:', error);
    }
  }, [error]);

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="p-4 bg-gray-50">
      <div className="flex justify-between items-center md:flex-row flex-col p-2 mb-2 border border-gray-200 bg-white rounded-2xl">
        <h1 className="md:text-3xl text-xl p-3 font-bold text-gray-800">Wastages</h1>
        <div className="flex md:flex-row flex-col-reverse gap-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Wastages"
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchTerm}
              onChange={handleSearch}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400 h-5 w-5" />
          </div>
          <button
            onClick={handleAddWastage}
            className="bg-orange-500 hover:bg-orange-600 text-white font-medium px-4 py-2 rounded-lg flex items-center gap-1"
          >
            Add Wastages
            <Plus size={20} />
          </button>
        </div>
      </div>

      {isLoading ? (
       <div className="flex justify-center items-start bg-white h-screen pt-[20vh]">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
            <p className="text-gray-600 font-medium">Loading Wastages...</p>
          </div>
        </div>
      ) : error ? (
        <p className="text-center text-red-500 py-8">Failed to load wastages.</p>
      ) : (
        <>
          <div className="bg-white shadow-sm border border-gray-200 rounded-2xl">
            <div className="overflow-x-auto rounded-2xl">
              <table className="w-full">
                <thead className="bg-orange-50">
                  <tr>
                    <th className="px-6 py-6 text-left text-sm font-semibold text-gray-700">
                      No
                    </th>
                    <th className="px-6 py-6 text-left text-sm font-semibold text-gray-700">
                      Ingredient
                    </th>
                    <th className="px-6 py-6 text-left text-sm font-semibold text-gray-700">
                      Date
                    </th>
                    <th className="px-6 py-6 text-left text-sm font-semibold text-gray-700">
                      Quantity
                    </th>
                    <th className="px-6 py-6 text-right text-sm font-semibold text-gray-700">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {currentWastages.length > 0 ? (
                    currentWastages.map((wastage, index) => (
                      <tr key={wastage.id} className="hover:bg-orange-50">
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {indexOfFirstItem + index + 1}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {wastage.IngredientName?.IngredientName || 'N/A'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {formatDate(wastage.createdAt)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {wastage.Quantity}
                        </td>
                        <td className="px-6 py-4 text-sm flex gap-4 justify-end">
                          <button
                            onClick={() => handleView(wastage.id)}
                            className="text-orange-500 hover:text-orange-700"
                            disabled={isDeleting}
                          >
                            <Eye 
                              id="view-icon"
                              data-tooltip-id="view-tooltip"
                              data-tooltip-content="View wastages"
                              size={20} 
                            />
                            <Tooltip
                              id="view-tooltip"
                              place="bottom"
                              className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                            />
                          </button>
                          <button
                            onClick={() => handleDelete(wastage.id)}
                            className={`text-red-500 hover:text-red-700 ${isDeleting ? "cursor-not-allowed opacity-50" : ""}`}
                            disabled={isDeleting}
                          >
                            <Trash2
                              id="delete-icon"
                              data-tooltip-id="delete-tooltip"
                              data-tooltip-content="Delete"
                              size={20}
                              className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                            />
                            <Tooltip
                              id="delete-tooltip"
                              place="bottom"
                              className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                            />
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                        No wastages found.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Pagination */}
          {filteredWastages.length > 0 && (
            <div className="flex justify-start items-center space-x-2 p-4">
              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  currentPage === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft size={18} className="text-gray-600" />
              </button>

              {Array.from(
                { length: Math.ceil(filteredWastages.length / itemsPerPage) },
                (_, i) => i + 1
              ).map((number) => (
                <button
                  key={number}
                  onClick={() => paginate(number)}
                  className={`w-8 h-8 rounded-md flex items-center justify-center border ${
                    currentPage === number
                      ? "bg-orange-500 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  {number}
                </button>
              ))}

              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  indexOfLastItem >= filteredWastages.length
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(currentPage + 1)}
                disabled={indexOfLastItem >= filteredWastages.length}
              >
                <ChevronRight size={18} className="text-gray-600" />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}