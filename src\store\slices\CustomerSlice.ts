import { createSlice } from '@reduxjs/toolkit';
// import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { Customer } from '../api/customerApi';
import { customerApi } from '../api/customerApi';

interface CustomerState {
  customers: Customer[];
  isLoading: boolean;
  error: string | null;
}

const initialState: CustomerState = {
  customers: [],
  isLoading: false,
  error: null,
};

const customerSlice = createSlice({
  name: 'customer',
  initialState,
  reducers: {
    // Local reducers for manual operations if needed
    setCustomers: (state, action) => {
      state.customers = action.payload;
    },
    clearCustomers: (state) => {
      state.customers = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle getCustomers query
      .addMatcher(
        customerApi.endpoints.getCustomers.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        customerApi.endpoints.getCustomers.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          state.customers = action.payload;
        }
      )
      .addMatcher(
        customerApi.endpoints.getCustomers.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to fetch customers';
        }
      )
      
      // Handle postCustomer mutation
      .addMatcher(
        customerApi.endpoints.postCustomer.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        customerApi.endpoints.postCustomer.matchFulfilled,
        (state) => {
          state.isLoading = false;
          // RTK Query will automatically invalidate the cache and trigger a refetch
        }
      )
      .addMatcher(
        customerApi.endpoints.postCustomer.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to add customer';
        }
      )
      
      // Handle putCustomer mutation
      .addMatcher(
        customerApi.endpoints.putCustomer.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        customerApi.endpoints.putCustomer.matchFulfilled,
        (state) => {
          state.isLoading = false;
          // RTK Query will automatically invalidate the cache and trigger a refetch
        }
      )
      .addMatcher(
        customerApi.endpoints.putCustomer.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to update customer';
        }
      )
      
      // Handle deleteCustomer mutation
      .addMatcher(
        customerApi.endpoints.deleteCustomer.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        customerApi.endpoints.deleteCustomer.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          // Filter out the deleted customer
          const deletedId = action.meta.arg.originalArgs;
          state.customers = state.customers.filter(
            (customer) => customer._id !== deletedId
          );
        }
      )
      .addMatcher(
        customerApi.endpoints.deleteCustomer.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to delete customer';
        }
      );
  },
});

// Export actions
export const { setCustomers, clearCustomers } = customerSlice.actions;

// Export selectors
export const selectCustomers = (state: RootState) => state.customer.customers;
export const selectCustomersLoading = (state: RootState) => state.customer.isLoading;
export const selectCustomersError = (state: RootState) => state.customer.error;

// Export reducer
export default customerSlice.reducer;