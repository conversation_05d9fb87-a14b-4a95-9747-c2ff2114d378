import { useState } from "react";
import { IoMdAdd } from "react-icons/io";
import { FiMinus } from "react-icons/fi";
import { HiOutlineDocumentPlus } from "react-icons/hi2";
import { RiDeleteBin5Line } from "react-icons/ri";
import { useDispatch } from "react-redux";
import { increaseQuantity, decreaseQuantity, removeItem, applyItemDiscount, toggleItemRefundSelection } from "../store/slices/cartSlice";
import type { ModifierOption } from "../types/modifiers";
import AdditionalItemModal from "./posModals/reportModals/AdditionalItemModal";

type Props = {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  modifiers?: ModifierOption[];
  hasModifiers?: boolean;
  note?: string;
  discountType?: string;
  discountAmount?: string;
  originalPrice?: number; // Original price without modifiers
  selectedForRefund?: boolean;
  isRefundMode?: boolean;
};

const InvoiceItem = ({ id, name, price, quantity, image, modifiers, hasModifiers, note, discountType, discountAmount, originalPrice, selectedForRefund, isRefundMode }: Props) => {
  const dispatch = useDispatch();
  const [isAdditionalModalOpen, setIsAdditionalModalOpen] = useState(false);

  const handleApplyDiscount = (
    discountType: string,
    discountAmount: string,
    note: string,
    selectedModifiers?: ModifierOption[],
    totalPrice?: number
  ) => {
    // Calculate the final price including modifiers if not provided
    let finalPrice: number;

    if (totalPrice !== undefined) {
      // If totalPrice is provided, use it directly
      finalPrice = totalPrice;
    } else {
      // Otherwise start with the base price
      finalPrice = price;

      // Add modifier prices if any
      if (selectedModifiers && selectedModifiers.length > 0) {
        selectedModifiers.forEach(modifier => {
          if (modifier.selectedProperty) {
            finalPrice += Number(modifier.selectedProperty.price);
          }
        });
      }

      // Apply discount if any
      if (discountType === "fixed" && discountAmount) {
        // Fixed amount discount
        const amountOff = parseFloat(discountAmount);
        if (!isNaN(amountOff)) {
          finalPrice = Math.max(0, finalPrice - amountOff);
        }
      } else if (discountType === "percentage" && discountAmount) {
        // Percentage discount
        const percentageDiscount = parseFloat(discountAmount) / 100;
        if (!isNaN(percentageDiscount)) {
          finalPrice = finalPrice * (1 - percentageDiscount);
        }
      }
    }

    // Dispatch action to update the item price with discount info and modifiers
    dispatch(applyItemDiscount({
      id,
      newPrice: finalPrice,
      discountType,
      discountAmount,
      note,
      modifiers: selectedModifiers || [],
      selectedModifiers: selectedModifiers || []
    }));
  };

  return (
    <div className={`border-2 rounded-lg p-2 shadow-sm ${isRefundMode && selectedForRefund
      ? 'border-orange-500 bg-blue-50'
      : 'border-gray-100'
      }`}>
      <div className="flex justify-between items-center py-2">
        <div className="flex items-center">
          {isRefundMode && (
            <input
              type="checkbox"
              checked={selectedForRefund || false}
              onChange={() => dispatch(toggleItemRefundSelection(id))}
              className={`mr-2 w-4 h-4 rounded focus:ring-orange-500 ${selectedForRefund
                ? 'text-orange-500 bg-orange-500 border-orange-500'
                : 'text-orange-500 border-gray-300'
                }`}
            />
          )}
          <img src={image} alt={name} className="w-19 h-19 rounded-xl" />
        </div>
        <div className="flex flex-col items-start justify-center flex-grow px-2">
          <span className="text-sm font-bold text-black pb-2">{name}</span>
          <span className="text-lg text-orange font-bold">
            ${price?.toFixed(2)}
          </span>
        </div>

        <div className="flex items-center item gap-2">
          <span
            className="h-9 w-9 flex items-center justify-center bg-[#E4E4E4] hover:bg-[#d6d6d6] rounded-4xl cursor-pointer"
            onClick={() => dispatch(decreaseQuantity(id))}
          >
            <FiMinus size={18} />
          </span>
          <span className="text-sm font-bold text-black">{quantity}</span>
          <span
            className="h-9 w-9 flex items-center justify-center bg-black hover:bg-[#333333] rounded-4xl cursor-pointer"
            onClick={() => dispatch(increaseQuantity(id))}
          >
            <IoMdAdd size={20} color="white" />
          </span>
        </div>
      </div>
      <div className="flex justify-evenly items-center py-2">
        <div
          className="flex items-center font-bold text-orange cursor-pointer hover:text-orange-600 transition-colors"
          onClick={() => setIsAdditionalModalOpen(true)}
        >
          <span className="mr-1">Additional</span>
          <HiOutlineDocumentPlus size={20} />
        </div>
        <div
          className="flex items-center font-bold text-orange cursor-pointer hover:text-orange-600 transition-colors"
          onClick={() => dispatch(removeItem(id))}
        >
          <span className="mr-1">Delete</span>
          <RiDeleteBin5Line size={20} />
        </div>
      </div>

      {/* Additional Item Modal */}
      <AdditionalItemModal
        isOpen={isAdditionalModalOpen}
        onClose={() => setIsAdditionalModalOpen(false)}
        onApply={handleApplyDiscount}
        itemName={name}
        itemPrice={price}
        modifiers={modifiers}
        hasModifiers={hasModifiers}
        initialNote={note}
        initialDiscountType={discountType}
        initialDiscountAmount={discountAmount}
        originalPrice={originalPrice}
      />


    </div>
  );
};

export default InvoiceItem;