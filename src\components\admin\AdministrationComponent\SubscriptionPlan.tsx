import { useState } from 'react';
import { Calendar, ChevronLeft, ChevronRight, Download, Upload } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const SubscriptionPlanPage = () => {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
    const navigate = useNavigate();

  // Mock data for transaction history
  const transactions = [
    {
      no: '01',
      name: '<PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      plan: 'Basic Plan',
      receivedPayments: '$20.00',
      totalCount: '05',
      startDate: '25 Dec 2023'
    },
    {
      no: '02',
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      plan: 'Popular Plan',
      receivedPayments: '$36.00',
      totalCount: '03',
      startDate: '01 March 2024'
    },
    {
      no: '03',
      name: 'Intiza<PERSON>',
      email: '<EMAIL>',
      plan: 'Best Plan',
      receivedPayments: '$60.00',
      totalCount: '06',
      startDate: '01 June 2024'
    }
  ];

  const toggleDatePicker = () => {
    setShowDatePicker(!showDatePicker);
  };

  const handleApply = () => {
    // Handle applying date range
    setShowDatePicker(false);
  };

  const handleCancel = () => {
    setShowDatePicker(false);
  };

 const handleUpgradeSubscription = () => {
    navigate('/admin/administration/upgrade-subscription-plan');
  };

  return (
    <div className="p-2 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-xl font-semibold text-gray-800">Subscription</h1>
        <button onClick={handleUpgradeSubscription} className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md flex items-center">
          Upgrade Subscription
          <Upload className="w-4 h-4 ml-2" />
        </button>
      </div>

      {/* Subscription Info Card */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div className="flex flex-wrap md:flex-nowrap justify-between items-center gap-4">
          <div>
            <h2 className="text-xl font-bold text-gray-800">Best Plan</h2>
            <p className="text-2xl font-bold">$5<span className="text-sm text-gray-500 font-normal">/ Per Month</span></p>
          </div>
          
          <div className="border-l pl-4 border-gray-200">
            <p className="text-sm text-gray-600">Trial Period</p>
            <p className="font-semibold">7 Days Remaining</p>
          </div>
          
          <div className="border-l pl-4 border-gray-200">
            <div className="flex items-center gap-2">
              <div className="w-8 h-6 bg-orange-500 rounded"></div>
              <div>
                <p className="text-sm text-gray-600">Mastercard</p>
                <p className="font-semibold">••••••••••3456</p>
              </div>
            </div>
          </div>
          
          <div className="border-l pl-4 border-gray-200">
            <p className="text-sm text-gray-600">Next Payment Due</p>
            <p className="font-semibold">23 Nov, 2024</p>
          </div>
          
          <div className="bg-orange-50 text-orange-500 px-4 py-2 rounded-md font-medium">
            Currently Active
          </div>
        </div>
      </div>

      {/* Transaction History */}
      <div>
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Transaction History</h2>
        
        <div className="flex flex-wrap md:flex-nowrap justify-between items-center mb-4 gap-4">
          <div className="relative">
            <div 
              onClick={toggleDatePicker} 
              className="flex items-center justify-between border border-gray-300 rounded-md py-2 px-3 w-64 cursor-pointer bg-white"
            >
              <span className="text-gray-500">{startDate && endDate ? `${startDate} — ${endDate}` : 'Start date — End date'}</span>
              <Calendar className="w-5 h-5 text-gray-400" />
            </div>
            
            {showDatePicker && (
              <div className="absolute top-full left-0 mt-2 bg-white shadow-lg rounded-md p-4 z-10 w-96">
                <div className="flex gap-4 mb-4">
                  <div className="w-1/2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <div className="relative">
                      <input 
                        type="text" 
                        placeholder="mm/dd/yyyy" 
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                        className="border border-gray-300 rounded-md py-2 px-3 w-full"
                      />
                      <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    </div>
                  </div>
                  <div className="w-1/2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <div className="relative">
                      <input 
                        type="text" 
                        placeholder="mm/dd/yyyy" 
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                        className="border border-gray-300 rounded-md py-2 px-3 w-full"
                      />
                      <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <button 
                    onClick={handleApply}
                    className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600"
                  >
                    Apply
                  </button>
                  <button 
                    onClick={handleCancel}
                    className="border border-orange-500 text-orange-500 px-4 py-2 rounded-md hover:bg-orange-50"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </div>
          
          <button className="flex items-center gap-2 text-orange-500 border border-orange-500 rounded-md px-4 py-2 hover:bg-orange-50">
            Download CSV
            <Download className="w-4 h-4" />
          </button>
        </div>
        
        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-orange-50">
                <th className="text-left py-3 px-4 font-medium text-gray-800">No</th>
                <th className="text-left py-3 px-4 font-medium text-gray-800">Name</th>
                <th className="text-left py-3 px-4 font-medium text-gray-800">Email</th>
                <th className="text-left py-3 px-4 font-medium text-gray-800">Plan</th>
                <th className="text-left py-3 px-4 font-medium text-gray-800">Received Payments</th>
                <th className="text-left py-3 px-4 font-medium text-gray-800">Total Count</th>
                <th className="text-left py-3 px-4 font-medium text-gray-800">Start Date</th>
              </tr>
            </thead>
            <tbody>
              {transactions.map((transaction, index) => (
                <tr key={index} className="border-b border-gray-100">
                  <td className="py-4 px-4 text-gray-800">{transaction.no}</td>
                  <td className="py-4 px-4 text-gray-800">{transaction.name}</td>
                  <td className="py-4 px-4 text-blue-500">{transaction.email}</td>
                  <td className="py-4 px-4">
                    <span className={`${
                      transaction.plan === 'Best Plan' ? 'text-orange-500' : 
                      transaction.plan === 'Popular Plan' ? 'text-blue-500' : 'text-gray-800'
                    }`}>
                      {transaction.plan}
                    </span>
                  </td>
                  <td className="py-4 px-4 text-gray-800">{transaction.receivedPayments}</td>
                  <td className="py-4 px-4 text-gray-800">{transaction.totalCount}</td>
                  <td className="py-4 px-4 text-gray-800">{transaction.startDate}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* Pagination */}
        <div className="flex items-center  mt-6">
          <button className="w-8 h-8 flex items-center justify-center rounded-md border border-gray-300 mr-2">
            <ChevronLeft className="w-4 h-4" />
          </button>
          
          <button className="w-8 h-8 flex items-center justify-center rounded-md bg-orange-500 text-white font-medium mx-1">
            1
          </button>
          
          <button className="w-8 h-8 flex items-center justify-center rounded-md border border-gray-300 ml-2">
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionPlanPage;