import { useState } from 'react';
import { Search, Edit, Trash2, ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface SMSMessage {
  id: string;
  number: string;
  message: string;
  status: 'Published' | 'Unpublished';
}

export default function SmsMarketing() {
  const [messages, setMessages] = useState<SMSMessage[]>([
    { id: '01', number: '<PERSON><PERSON>', message: 'Message', status: 'Published' },
    { id: '02', number: '<PERSON>zan', message: 'Message', status: 'Unpublished' },
  ]);
  
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');

  const handleEdit = (id: string) => {
    // Implement edit functionality
    console.log(`Editing message with ID: ${id}`);
  };

  const handleDelete = (id: string) => {
    // Implement delete functionality
    setMessages(messages.filter(message => message.id !== id));
  };

  const navigate = useNavigate();
  const handleCreateCampaign = () => {
    // Implement create campaign functionality
    console.log('Creating new SMS campaign');
    navigate('/admin/marketing/sms-marketing/sms-marketing-form');
  };

  return (
    <>
    <div className="bg-gray-50 min-h-screen">
      <div className='p-4'>
        <div className="flex items-center justify-between md:flex-row flex-col bg-white rounded-2xl border border-gray-200 p-2 mb-6">
          <h1 className="text-3xl p-3 font-bold text-gray-800">SMS Marketing</h1>
          <div className="flex md:flex-row flex-col-reverse gap-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search SMS"
                className="pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
            </div>
            <button 
              onClick={handleCreateCampaign}
              className="bg-orange-500 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-orange-600 transition-colors"
            >
              Create SMS Campaign
              <Plus size={18} />
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg overflow-x-auto">
          <table className="w-full">
            <thead className="bg-orange-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-600">No</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-600">Numbers</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-600">Message</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-600">Status</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-600">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {messages.map((message) => (
                <tr key={message.id} className="hover:bg-orange-50">
                  <td className="px-6 py-4 text-sm text-gray-700">{message.id}</td>
                  <td className="px-6 py-4 text-sm text-gray-700">{message.number}</td>
                  <td className="px-6 py-4 text-sm text-gray-600 hover:underline cursor-pointer">{message.message}</td>
                  <td className="px-6 py-4">
                    <span 
                      className={`px-3 py-1 inline-flex text-xs leading-5 font-medium rounded-full ${
                        message.status === 'Published' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      {message.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-right text-sm space-x-3">
                    <button 
                      onClick={() => handleEdit(message.id)}
                      className="text-blue-500 hover:text-blue-700"
                    >
                      <Edit size={18} />
                    </button>
                    <button 
                      onClick={() => handleDelete(message.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 size={18} />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
        </div>
      </div>
      <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200">
            <div className="flex items-center space-x-2">
              <button 
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                className="p-1 rounded-md border border-gray-300 hover:bg-gray-100"
                disabled={currentPage === 1}
              >
                <ChevronLeft size={16} />
              </button>
              <button className="px-3 py-1 rounded-md bg-orange-500 text-white">
                {currentPage}
              </button>
              <button 
                onClick={() => setCurrentPage(currentPage + 1)}
                className="p-1 rounded-md border border-gray-300 hover:bg-gray-100"
              >
                <ChevronRight size={16} />
              </button>
            </div>
          </div>
    </div>
   
    </>
  );
}