import { useState } from 'react';
import { PlusCircle, XCircle } from 'lucide-react';

// interface StripeConnectionProps {
//   onConnect: () => void;
//   onDisconnect: () => void;
// }

export default function StripeConnection() {
  const [isConnected, setIsConnected] = useState(true);
  const [email, setEmail] = useState('<EMAIL>');
  const [connectionDate, setConnectionDate] = useState('Nov 04, 2024');

  const handleConnect = () => {
    setIsConnected(false);
    setEmail('<EMAIL>');
    setConnectionDate('Nov 04, 2024');
    // onConnect();
  };

  const handleDisconnect = () => {
    setIsConnected(false);
    setEmail('');
    setConnectionDate('');
    // onDisconnect();
  };

  const EmptyStateIllustration = () => (
    <svg width="150" height="170" viewBox="0 0 150 170" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M89.3 89.5C89.3 89.5 89.7 96.1 86.6 102.7C83.5 109.3 80.3 112 80.3 112L89.3 114.8L102.5 111L107.5 103.8L105.3 94L96.7 89.5H89.3Z" fill="#FF6B00"/>
      <path d="M89.3 89.5C89.3 89.5 89.7 96.1 86.6 102.7C83.5 109.3 80.3 112 80.3 112L89.3 114.8L102.5 111L107.5 103.8L105.3 94L96.7 89.5H89.3Z" fill="url(#pattern0)"/>
      <path d="M71.1 144.7L68.7 162.1H75.5L80.5 145.7L71.1 144.7Z" fill="black"/>
      <path d="M101.2 144.7L98.8 162.1H105.6L110.6 145.7L101.2 144.7Z" fill="black"/>
      <path d="M83.2 72.3C82.5 70.5 80.5 69.6 78.7 70.3C76.9 71 76 73 76.7 74.8C77.4 76.6 79.4 77.5 81.2 76.8C83 76.1 83.9 74.1 83.2 72.3Z" fill="white"/>
      <path d="M78.8 107.4C77.5 109.2 76.3 110.8 75.2 112.1L78.7 119.9C78.7 119.9 86.7 127.9 85.6 132.4C84.5 136.9 74.5 146 74.5 146C74.5 146 80.6 147.4 98.7 147.4C116.8 147.4 110.9 130.5 110.9 130.5C110.9 130.5 113 124.6 102.3 116.6L99.1 112.5C99.1 112.5 95 112.4 94 107.3C93 102.2 92.9 70.8 92.9 70.8C92.9 70.8 88.8 63.7 82.5 66.1C76.2 68.5 76 82 76 85.9C76 89.8 80.5 105 78.8 107.4Z" fill="white"/>
      <path d="M94.9 41.9C94.9 41.9 98.4 42.8 98.4 47.3C98.4 51.8 97.7 59.3 97.7 59.3L92.1 59.8L88.2 60.4L83.6 57.2L84.2 49L90 41.4L94.9 41.9Z" fill="#FF6B00"/>
      <path d="M85.5 41.3C85.5 41.3 82.8 40.3 81.9 42.4C81 44.5 78.4 54.8 78.4 54.8L72 60.4L78.2 65.5L96.9 58.8L97.9 47.4C97.9 47.4 96.9 37.6 90.5 38.1C84.1 38.6 85.5 41.3 85.5 41.3Z" fill="black"/>
      <path d="M73 95.2L73.6 104L58.8 111.6C58.8 111.6 38.3 121.7 42.7 123C47.1 124.3 86.5 118.6 86.5 118.6L90.2 105.9L73 95.2Z" fill="black"/>
      <path d="M91.8 66.1C96.7 65.7 103.3 77.5 103.3 77.5C103.3 77.5 118.6 64.4 117.4 84.8C116.2 105.2 102.9 102.9 102.9 102.9C102.9 102.9 99.6 105.5 103.7 109.2C103.7 109.2 110.8 116.2 98.8 114.9C98.8 114.9 98.5 111.2 96.6 109C96.6 109 93.1 123 95.1 125.1C97.1 127.2 99.9 129.2 98.8 132.1C97.7 135 96 134.9 96 134.9" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
      <path d="M69.9 66.1C65 65.7 58.4 77.5 58.4 77.5C58.4 77.5 43.1 64.4 44.3 84.8C45.5 105.2 58.8 102.9 58.8 102.9C58.8 102.9 62.1 105.5 58 109.2C58 109.2 50.9 116.2 62.9 114.9C62.9 114.9 63.2 111.2 65.1 109C65.1 109 68.6 123 66.6 125.1C64.6 127.2 61.8 129.2 62.9 132.1C64 135 65.7 134.9 65.7 134.9" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
      <defs>
        <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
          <use href="#image0_0_1" transform="scale(0.05)"/>
        </pattern>
        <image id="image0_0_1" width="20" height="20" href="data:image/png,"/>
      </defs>
    </svg>
  );

  return (
    <div className="w-full bg-white p-6 rounded-lg">
      <div className="flex justify-between md:flex-row flex-col items-center mb-8">
        <h1 className="text-3xl p-3 font-bold text-gray-800">Stripe Connection</h1>
        <div className="flex space-x-3">
          <button
            onClick={handleConnect}
            className="flex items-center bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors"
          >
            Connect Stripe Account <PlusCircle className="ml-2 h-5 w-5" />
          </button>
          <button
            onClick={handleDisconnect}
            className="flex items-center bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors"
          >
            Disconnect Stripe Account <XCircle className="ml-2 h-5 w-5" />
          </button>
        </div>
      </div>

      {isConnected ? (
        <div className="flex items-center justify-between md:flex-row flex-col p-4 bg-gray-50 rounded-lg shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0 w-12 h-12 bg-indigo-500 rounded-md flex items-center justify-center text-white font-bold text-xl">
              S
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-700">Stripe Account</p>
              <p className="text-sm text-gray-600">{email}</p>
            </div>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span className="text-sm text-gray-600">Connected on "{connectionDate}"</span>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="mb-6">
            <EmptyStateIllustration />
          </div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">Nothing to Show!</h2>
          <p className="text-center text-gray-600 max-w-md">
            Please Connect a Stripe Account before you access this module.
          </p>
        </div>
      )}
    </div>
  );
}