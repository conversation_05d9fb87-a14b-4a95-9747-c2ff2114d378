import { baseApi } from "./baseApi";

// Define the types that match the backend response structure
export interface Property {
  name: string;
  totalQuantity: number | string; 
  price: number | string;
  _id?: string;
}

export interface ModifierItem {
  name: string;
  properties?: Property[];
  _id?: string;
}

export interface Product {
  name: string;
  _id?: string;
}

// This should match the actual structure returned by the API
export interface Modifier {
  _id: string;
  productId?: Product;
  Modifier?: ModifierItem[];
  isActive: boolean;
}

export interface ModifierPayload {
  Modifier: {
    name: string;
    properties: {
      name: string;
      totalQuantity: number; 
      price: number;
    }[];
  }[];
  isActive: boolean;
  productId: string;
  userId?: string;
  message?: string;
}

export const modifiersApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getModifiersByUser: builder.query<Modifier[], void>({
      query: () => {
        const userId = localStorage.getItem('userId') || '';
        return {
          url: '/modifiers',
          params: { userId }
        };
      },
      providesTags: ["Modifier"],
    }),
    
    addModifiers: builder.mutation<{ message: string }, ModifierPayload>({
      query: (modifiersData) => ({
        url: '/modifiers',
        method: 'POST',
        body: modifiersData,
      }),
      invalidatesTags: ["Modifier"],
    }),
    
    updateModifier: builder.mutation<{ message: string }, { id: string; data: ModifierPayload }>({
      query: ({ id, data }) => ({
        url: `/modifier/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (_result, _error, { id }) => [{ type: "Modifier", id }, "Modifier"],
    }),
    
    deleteModifier: builder.mutation<{ message: string }, string>({
      query: (id) => ({
        url: `/modifiers/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ["Modifier"],
    }),
  }),
});

export const { 
  useAddModifiersMutation,
  useGetModifiersByUserQuery,
  useUpdateModifierMutation,
  useDeleteModifierMutation
} = modifiersApi;