import React, { useState } from "react";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import {
  Search,
  Edit,
  Trash2,
  Plus,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  useGetParentCategoriesQuery,
  useDeleteParentCategoryMutation,
} from "../../../store/api/parentCategoryApi";
import Swal from "sweetalert2";
import { ClipLoader } from "react-spinners";

const AdminParentCategories: React.FC = () => {
  const navigate = useNavigate();
  const userId = localStorage.getItem("userId") || "";

  const {
    data: categories = [],
    isLoading,
    error,
    refetch,
  } = useGetParentCategoriesQuery(userId);


  const [deleteParentCategory, { isLoading: isDeleting }] =
    useDeleteParentCategoryMutation();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const filteredCategories = categories.filter((category) =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentCategories = filteredCategories.slice(
    indexOfFirstItem,
    indexOfLastItem
  );

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  const handleAddCategory = () => {
    navigate("/admin/catalog/parent-categories/parent-categories-form");
  };

  const handleDeleteCategory = async (categoryId: string) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Are you sure to delete this parent category!",
      icon: "warning",
      cancelButtonText: "No, cancel!",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#6b7280",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        await deleteParentCategory(categoryId).unwrap();
        await Swal.fire(
          "Deleted!",
          "Parent category has been deleted.",
          "success"
        );
        refetch();
      } catch (error) {
        Swal.fire("Error!", "Failed to delete parent category.", "error");
      }
    }
  };

  return (
    <div className="p-2 mx-auto bg-gray-50">
      <div className="mb-6">
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row justify-between items-center p-2 bg-white m-2 rounded-3xl border border-gray-200 shadow">
          <h1 className="text-3xl font-bold p-3 text-gray-800">
            Parent Categories
          </h1>
          <div className="flex flex-col md:flex-row items-center gap-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search Parent Category"
                className="pl-10 pr-4 py-2 border border-gray-300 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <div className="absolute left-3 top-2.5 text-gray-400">
                <Search size={20} />
              </div>
            </div>

            <button
              onClick={handleAddCategory}
              className="bg-orange-500 cursor-pointer hover:bg-orange-600 text-white font-medium py-2 px-4 rounded-md flex items-center"
            >
              Add Parent Category <Plus size={20} className="ml-1" />
            </button>
          </div>
        </div>
      </div>

      {/* Category Table */}
      {isLoading ? (
        <div className="flex justify-center items-start bg-white h-screen pt-[20vh]">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
            <p className="text-gray-600 font-medium">
              Loading Parent Categories...
            </p>
          </div>
        </div>
      ) : error ? (
        <div className="text-center p-4 bg-red-50 rounded-md">
          <p className="text-red-500 font-medium">
            Failed to load categories. Please try again.
          </p>
        </div>
      ) : (
        <>
          <div className="bg-white rounded-md shadow overflow-x-auto">
            <table className="min-w-full bg-orange-50">
              <thead className="bg-orange-50">
                <tr>
                  <th className="px-6 py-6 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider w-20">
                    No
                  </th>
                  <th className="px-6 py-6 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Parent Category Name
                  </th>
                  <th className="px-6 py-6 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Total Sub Categories
                  </th>
                  <th className="px-6 py-6 text-right text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentCategories.length > 0 ? (
                  currentCategories.map((category, index) => (
                    <tr key={category.id} className="hover:bg-orange-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                        {indexOfFirstItem + index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-sm">
                        {category.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                        {category.subCategoriesCount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          className="text-blue-400 hover:text-blue-600 mr-3"
                          onClick={() =>
                            navigate(
                              `/admin/catalog/parent-categories/parent-categories-form/${category.id}`
                            )
                          }
                        >
                          <div>
                            <Edit
                              id="edit-icon"
                              data-tooltip-id="edit-tooltip"
                              data-tooltip-content="Edit"
                              size={20}
                              className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                            />
                            <Tooltip
                              id="edit-tooltip"
                              place="bottom"
                              className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                            />
                          </div>
                        </button>

                        <button
                          className={`text-red-400 hover:text-red-600 ${
                            isDeleting ? "cursor-not-allowed opacity-50" : ""
                          }`}
                          onClick={() => handleDeleteCategory(category.id)}
                          disabled={isDeleting}
                        >
                          {isDeleting ? (
                            <ClipLoader size={20} color="#EF4444" />
                          ) : (
                            <div>
                              <Trash2
                                id="delete-icon"
                                data-tooltip-id="delete-tooltip"
                                data-tooltip-content="Delete"
                                size={20}
                                className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                              />
                              <Tooltip
                                id="delete-tooltip"
                                place="bottom"
                                className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                              />
                            </div>
                          )}
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={4}
                      className="px-6 py-4 text-center text-gray-500"
                    >
                      No parent categories found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {filteredCategories.length > 0 && (
            <div className="flex items-center justify-start mt-4 gap-2">
              <button
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
                className={`flex items-center justify-center w-8 h-8 rounded-md border ${
                  currentPage === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-50"
                }`}
              >
                <ChevronLeft size={16} />
              </button>

              {/* Page number buttons */}
              {Array.from(
                { length: Math.ceil(filteredCategories.length / itemsPerPage) },
                (_, i) => i + 1
              ).map((number) => (
                <button
                  key={number}
                  onClick={() => paginate(number)}
                  className={`w-8 h-8 rounded-md flex items-center justify-center border ${
                    currentPage === number
                      ? "bg-orange-500 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  {number}
                </button>
              ))}

              <button
                onClick={() => paginate(currentPage + 1)}
                disabled={indexOfLastItem >= filteredCategories.length}
                className={`flex items-center justify-center w-8 h-8 rounded-md border ${
                  indexOfLastItem >= filteredCategories.length
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-50"
                }`}
              >
                <ChevronRight size={16} />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default AdminParentCategories;
