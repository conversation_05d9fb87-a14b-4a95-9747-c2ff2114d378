import React, { useState, useEffect } from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import CustomModal from "../../CustomModal";
import type { ModifierOption, ModifierProperty } from "../../../types/modifiers";

interface AdditionalItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (discountType: string, discountAmount: string, note: string, selectedModifiers?: ModifierOption[], totalPrice?: number) => void;
  itemName: string;
  itemPrice: number;
  modifiers?: ModifierOption[];
  hasModifiers?: boolean;
  initialNote?: string;
  initialDiscountType?: string;
  initialDiscountAmount?: string;
  originalPrice?: number; // Original price without modifiers
}

const AdditionalItemModal: React.FC<AdditionalItemModalProps> = ({
  isOpen,
  onClose,
  onApply,
  itemName,
  itemPrice,
  modifiers = [],
  hasModifiers = false,
  initialNote = "",
  initialDiscountType = "",
  initialDiscountAmount = "",
  originalPrice
}) => {
  // Use originalPrice if provided, otherwise use itemPrice
  const baseItemPrice = originalPrice !== undefined ? originalPrice : itemPrice;
  const [discountType, setDiscountType] = useState<string>(initialDiscountType);
  const [discountAmount, setDiscountAmount] = useState<string>(initialDiscountAmount);
  const [note, setNote] = useState<string>(initialNote);
  const [discountTypeError, setDiscountTypeError] = useState<string>("");
  const [finalPrice, setFinalPrice] = useState<number>(itemPrice);
  const [selectedModifiers, setSelectedModifiers] = useState<ModifierOption[]>([]);


  // Initialize form when modal opens
  useEffect(() => {
    if (isOpen) {
      // Use initial values if provided, otherwise reset
      setDiscountType(initialDiscountType || "");
      setDiscountAmount(initialDiscountAmount || "");
      setNote(initialNote || "");
      setDiscountTypeError("");

      // Initialize selected modifiers, preserving any existing selections
      if (hasModifiers && modifiers && modifiers.length > 0) {
        // Map through the modifiers and keep any existing selectedProperty
        const initialModifiers = modifiers.map(mod => {
          // Make sure we're working with a copy to avoid reference issues
          return {
            ...mod,
            // Keep the selectedProperty if it exists
            selectedProperty: mod.selectedProperty ? { ...mod.selectedProperty } : undefined,
            // Make sure properties is always an array
            properties: mod.properties ? [...mod.properties] : []
          };
        });

        setSelectedModifiers(initialModifiers);
      } else {
        setSelectedModifiers([]);
      }
    }
  }, [isOpen, initialDiscountType, initialDiscountAmount, initialNote, hasModifiers]);

  // Update final price when discount or modifiers change
  useEffect(() => {
    // Start with base price (without modifiers)
    let calculatedPrice = baseItemPrice;

    // Add the price of all selected modifier properties
    selectedModifiers.forEach(modifier => {
      if (modifier.selectedProperty) {
        calculatedPrice += Number(modifier.selectedProperty.price);
      }
    });

    // If no discount, just use the calculated price with modifiers
    if (!discountType || !discountAmount || discountAmount === "") {
      setFinalPrice(calculatedPrice);
      return;
    }

    // Apply discount to the calculated price (base + modifiers)
    let newPrice = calculatedPrice;
    const discountValue = parseFloat(discountAmount);

    if (!isNaN(discountValue)) {
      if (discountType === "percentage") {
        // Ensure percentage is between 0 and 100
        const validPercentage = Math.min(100, Math.max(0, discountValue));
        newPrice = calculatedPrice * (1 - validPercentage / 100);
      } else if (discountType === "fixed") {
        // Ensure discount doesn't make price negative
        newPrice = Math.max(0, calculatedPrice - discountValue);
      }
    }
    setFinalPrice(newPrice);
  }, [discountType, discountAmount, baseItemPrice, selectedModifiers]);

  const handleAddToOrder = () => {
    // Check if user entered discount amount without selecting discount type
    if (!discountType && discountAmount && parseFloat(discountAmount) > 0) {
      setDiscountTypeError("Please select a discount type");
      return;
    }

    // Clear any existing errors
    setDiscountTypeError("");

    // Apply discount and modifiers if any
    if (discountType && discountAmount && parseFloat(discountAmount) > 0) {
      onApply(discountType, discountAmount, note, selectedModifiers, finalPrice);
    } else {
      // No discount applied - pass empty discount values
      onApply("", "", note, selectedModifiers, finalPrice);
    }

    onClose();
  };

  const handleDiscountAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Only allow numbers and a single decimal point
    if (/^(\d*\.?\d*)$/.test(value) || value === "") {
      setDiscountAmount(value);

      // Clear any previous error when user starts typing
      if (discountTypeError) {
        setDiscountTypeError("");
      }
    }
  };

  const handleModifierSelection = (modifierIndex: number, property: ModifierProperty | undefined) => {
    const updatedModifiers = [...selectedModifiers];
    updatedModifiers[modifierIndex] = {
      ...updatedModifiers[modifierIndex],
      selectedProperty: property
    };

    setSelectedModifiers(updatedModifiers);
  };

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title={itemName}
      width="max-w-md"
      zIndex={100}
    >
      <div className="flex flex-col p-4">
        {/* Discount Section */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Discount</h3>

          <div className="flex mb-4">
            <div className="w-1/2 pr-2">
              <div className="text-sm text-gray-500 mb-1">Select Discount Type</div>
              <div className="relative">
                <select
                  value={discountType}
                  onChange={(e) => {
                    setDiscountType(e.target.value);
                    setDiscountTypeError("");
                    // Clear discount amount when discount type is cleared
                    if (e.target.value === "") {
                      setDiscountAmount("");
                    }
                  }}
                  className="w-full border border-gray-300 rounded py-2 px-3 bg-white cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-300 focus:border-orange-300"
                  style={{ appearance: 'none' }}
                >
                  <option value="">Select Type</option>
                  <option value="percentage">Percentage</option>
                  <option value="fixed">Fixed Amount</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="w-1/2 pl-2">
              <div className="text-sm text-gray-500 mb-1">Discount Amount</div>
              <input
                type="text"
                value={discountAmount}
                onChange={handleDiscountAmountChange}
                placeholder="0"
                className="w-full border border-gray-300 rounded py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-300 focus:border-orange-300"
              />
              {discountType === "percentage" && discountAmount && (
                <span className="text-xs text-gray-500 mt-1 block">
                  ${((baseItemPrice + selectedModifiers.reduce((sum, mod) => sum + (mod.selectedProperty?.price || 0), 0)) * (parseFloat(discountAmount) / 100)).toFixed(2)} off
                </span>
              )}
              {discountType === "fixed" && discountAmount && (
                <span className="text-xs text-gray-500 mt-1 block">
                  ${parseFloat(discountAmount).toFixed(2)} off
                </span>
              )}
            </div>
          </div>

          {/* Error message */}
          {discountTypeError && (
            <div className="bg-red-50 p-2 rounded mb-4">
              <p className="text-red-600 text-sm flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                {discountTypeError}
              </p>
            </div>
          )}

          {/* Price comparison */}
          {(discountType && discountAmount && parseFloat(discountAmount) > 0) && (
            <div className="bg-green-50 p-2 rounded mb-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Original Price:</span>
                <span className="text-sm line-through text-gray-500">
                  ${(baseItemPrice + selectedModifiers.reduce((sum, mod) => sum + (mod.selectedProperty?.price || 0), 0)).toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-green-600">Discount:</span>
                <span className="text-sm font-medium text-green-600">
                  {discountType === "percentage" ? `${discountAmount}%` : `$${discountAmount}`} off
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-green-600">Final Price:</span>
                <span className="text-sm font-medium text-green-600">${finalPrice.toFixed(2)}</span>
              </div>
            </div>
          )}
        </div>

        {/* Modifiers Section */}
        {hasModifiers && modifiers && modifiers.length > 0 && (
          <div className="mb-6">
            {selectedModifiers.map((modifier, index) => (
              <div key={index} className="mb-4">
                <h3 className="text-lg font-medium mb-2">{modifier.name}</h3>
                <div className="flex flex-wrap gap-2">
                  {modifier.properties?.map((property) => (
                    <button
                      key={property._id}
                      className={`px-4 py-2 rounded-full border ${modifier.selectedProperty?._id === property._id
                        ? 'bg-orange-500 text-white border-orange-500'
                        : 'border-gray-300 hover:border-orange-300'
                        }`}
                      onClick={() => handleModifierSelection(index, property)}
                    >
                      {property.name} {property.price > 0 && `$${property.price.toFixed(2)}`}
                    </button>
                  ))}
                  <button
                    className={`px-4 py-2 rounded-full border ${!modifier.selectedProperty
                      ? 'bg-orange-500 text-white border-orange-500'
                      : 'border-gray-300 hover:border-orange-300'
                      }`}
                    onClick={() => handleModifierSelection(index, undefined)}
                  >
                    None
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Selected Modifiers Summary */}
        {/* Quantity Section */}


        {hasModifiers && selectedModifiers.some(mod => mod.selectedProperty) && (
          <div className="mb-6">
            {selectedModifiers.map((modifier, index) => (
              modifier.selectedProperty && (
                <div key={index} className="flex justify-between items-center mb-3 bg-gray-100 p-2 rounded">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-700">
                      {modifier.name}
                    </span>
                    <span className="ml-2 text-sm text-gray-500">
                      {modifier.selectedProperty.name}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-700 mr-2">
                      {Number(modifier.selectedProperty.price).toFixed(0)}
                    </span>
                    <button
                      className="text-gray-500 hover:text-red-500 cursor-pointer"
                      onClick={() => handleModifierSelection(index, undefined)}
                    >
                      {/* <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg> */}
                      <FaRegTrashCan className="size-5 fill-amber-950" />
                    </button>
                  </div>
                </div>
              )
            ))}
          </div>
        )}

        {/* Note Section */}
        <div className="mb-6">
          <h3 className="text-sm text-gray-500 mb-1">Add note</h3>
          <textarea
            value={note}
            onChange={(e) => setNote(e.target.value)}
            placeholder="Type your note here..."
            className="w-full border border-gray-300 rounded p-2 h-24 focus:outline-none focus:ring-2 focus:ring-orange-300 focus:border-orange-300 resize-none"
          />
        </div>

        {/* Total Section */}
        <div className="flex justify-between items-center mb-6">
          <div className="text-lg font-medium">Total</div>
          <div className="text-lg font-bold text-orange-500">${finalPrice.toFixed(2)}</div>
        </div>

        {/* Add to Order Button */}
        <button
          onClick={handleAddToOrder}
          className="w-full py-3 bg-orange-500 text-white rounded-full font-medium hover:bg-orange-600 transition-colors"
        >
          Add to order
        </button>
      </div>
    </CustomModal>
  );
};

export default AdditionalItemModal;