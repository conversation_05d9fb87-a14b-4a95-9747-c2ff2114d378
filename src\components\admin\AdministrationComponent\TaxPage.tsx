import { useState, useEffect, type SetStateAction } from 'react';
import { Search, ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  useGetTaxsQuery,
  useDeleteTaxMutation,
} from "../../../store/api/taxApi";
import { toast } from 'react-toastify';
import {Edit, Trash2} from "lucide-react"

export default function TaxPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5); 
  const [showModal, setShowModal] = useState(false);
  const [taxToDelete, setTaxToDelete] = useState<string | null>(null);
  const navigate = useNavigate();

  // Get userId from localStorage
  const userId = localStorage.getItem('userId') || '';

  // Fetch taxes using the API
  const { data: taxes = [], isLoading, error, refetch } = useGetTaxsQuery(userId);
  const [deleteTax, { isLoading: isDeleting }] = useDeleteTaxMutation();

  const handleSearch = (e: { target: { value: SetStateAction<string>; }; }) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); 
  };

  const handleDelete = (id: SetStateAction<string | null>) => {
    setTaxToDelete(id);
    setShowModal(true);
  };

  const confirmDelete = async () => {
    if (taxToDelete) {
      try {
        await deleteTax(taxToDelete).unwrap();
        toast.success('Tax deleted successfully');
        refetch();
        setShowModal(false);
      } catch (error) {
        console.error('Failed to delete tax:', error);
        toast.error('Failed to delete tax');
        setShowModal(false);
      }
    }
  };

  const handleEdit = (id: string) => {
    navigate(`/admin/administration/add-tax-form/${id}`);
  };

  const handleAddTax = () => {
    navigate('/admin/administration/add-tax-form');
  };

  useEffect(() => {
    if (error) {
      toast.error('Failed to fetch taxes');
      console.error('Error fetching taxes:', error);
    }
  }, [error]);

  // Filter taxes based on search query
  const filteredTaxes = taxes.filter(tax =>
    tax.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Pagination logic - Updated to match Supplier page
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentTaxes = filteredTaxes.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredTaxes.length / itemsPerPage);

  // Function to handle pagination
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  return (
    <div className="w-full p-2">
      {/* Delete Confirmation Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-50">
          <div className="bg-white p-8 rounded-xl shadow-xl w-full max-w-md text-center relative">
            <div className="flex justify-center mb-4">
              <div className="bg-orange-100 text-orange-500 p-4 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M12 5c.512 0 1.023.195 1.414.586C13.805 6.977 14 7.488 14 8s-.195 1.023-.586 1.414A1.993 1.993 0 0112 10a1.993 1.993 0 01-1.414-.586A1.993 1.993 0 0110 8c0-.512.195-1.023.586-1.414A1.993 1.993 0 0112 5z" />
                </svg>
              </div>
            </div>
            <h3 className="text-2xl font-semibold text-gray-800">Are you sure?</h3>
            <p className="mt-2 text-gray-600">Do you want to delete this tax?</p>
            <div className="mt-6 flex justify-center gap-4">
              <button
                onClick={confirmDelete}
                className="bg-orange-600 hover:bg-orange-700 text-white px-5 py-2 rounded-md font-medium"
                disabled={isDeleting}
              >
                Yes, delete it!
              </button>
              <button
                onClick={() => setShowModal(false)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-5 py-2 rounded-md font-medium"
              >
                No, cancel!
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center mb-4">
        <h1 className="text-xl font-medium text-gray-800">Tax</h1>

        <div className="flex space-x-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Tax"
              className="pl-10 pr-4 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchQuery}
              onChange={handleSearch}
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
          </div>

          <button
            className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md flex items-center space-x-1"
            onClick={handleAddTax}
          >
            <span>Add Tax</span>
            <Plus className="h-5 w-5" />
          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <p className="text-gray-600">Loading taxes...</p>
        </div>
      ) : error ? (
        <div className="flex justify-center items-center h-40">
          <p className="text-red-500">Failed to load taxes. Please try again later.</p>
        </div>
      ) : filteredTaxes.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <p className="text-gray-500">No taxes found.</p>
        </div>
      ) : (
        <div className="bg-orange-50 rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr className="text-left text-gray-700">
                <th className="px-6 py-4 w-16">No</th>
                <th className="px-6 py-4">Name</th>
                <th className="px-6 py-4">Tax Value</th>
                <th className="px-6 py-4">Status</th>
                <th className="px-6 py-4 w-24"></th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {currentTaxes.map((tax, index) => (
                <tr key={tax.id}>
                  <td className="px-6 py-4 text-blue-600">{indexOfFirstItem + index + 1}</td>
                  <td className="px-6 py-4 font-medium text-gray-800">{tax.name}</td>
                  <td className="px-6 py-4">{tax.taxValue}</td>
                  <td className="px-6 py-4">
                  <span className={`${tax.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} px-4 py-1 rounded-full`}>
                      {tax.active ? 'Active' : 'Inactive'}
                     </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex space-x-2">
                      <button
                        className="text-blue-500 hover:text-blue-700"
                        onClick={() => handleEdit(tax.id)}
                      >
                        <Edit className="h-5 w-5" />
                      </button>
                      <button
                        className="text-red-500 hover:text-red-700"
                        onClick={() => handleDelete(tax.id)}
                        disabled={isDeleting}
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Pagination - Updated to match Supplier page style */}
          {filteredTaxes.length > 0 && (
            <div className="flex justify-start items-center space-x-2 p-4 bg-white border-t border-gray-200">
              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  currentPage === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft size={18} className="text-gray-600" />
              </button>

              {Array.from(
                { length: totalPages },
                (_, i) => i + 1
              ).map((number) => (
                <button
                  key={number}
                  onClick={() => paginate(number)}
                  className={`w-8 h-8 rounded-md flex items-center justify-center border ${
                    currentPage === number
                      ? "bg-orange-500 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  {number}
                </button>
              ))}

              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  indexOfLastItem >= filteredTaxes.length
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(currentPage + 1)}
                disabled={indexOfLastItem >= filteredTaxes.length}
              >
                <ChevronRight size={18} className="text-gray-600" />
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}