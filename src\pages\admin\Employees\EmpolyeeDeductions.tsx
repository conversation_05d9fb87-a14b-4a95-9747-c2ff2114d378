import { useState, useEffect, useMemo } from 'react';
import { Search, Plus, Edit, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  useGetDeductionsQuery,
  useDeleteDeductionMutation,
} from "../../../store/api/EmployeeListApi";
import Swal from 'sweetalert2';
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";

export default function EmployeeDeductions() {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const navigate = useNavigate();

  const { data: deductionsData, isLoading, error, refetch } = useGetDeductionsQuery();
  const [deleteDeduction, { isLoading: isDeleting }] = useDeleteDeductionMutation();

  const handleSearch = (e: any) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleDeleteDeduction = async (id: any) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
    });

    if (result.isConfirmed) {
      try {
        await deleteDeduction(id).unwrap();
        Swal.fire({
          title: 'Deleted!',
          text: 'Deduction has been deleted.',
          icon: 'success',
          confirmButtonColor: '#f97316',
        });
        refetch();
      } catch (error) {
        console.error('Failed to delete deduction:', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete deduction.',
          icon: 'error',
          confirmButtonColor: '#f97316',
        });
      }
    }
  };

  const handleAddDeduction = () => {
    navigate('/admin/employees/employee-deductions/employee-deduction-form');
  };

  const handleEditDeduction = (id: any) => {
    navigate(`/admin/employees/employee-deductions/employee-deduction-form/${id}`);
  };

  // Enhanced filtering logic with better error handling
  const filteredDeductions = useMemo(() => {
    console.log('Search Query:', searchQuery);
    console.log('Deductions Data:', deductionsData);
    
    if (!deductionsData || !Array.isArray(deductionsData)) {
      console.log('No data or data is not an array');
      return [];
    }
    
    if (!searchQuery.trim()) {
      console.log('No search query, returning all data');
      return deductionsData;
    }
    
    const query = searchQuery.toLowerCase().trim();
    console.log('Filtering with query:', query);
    
    const filtered = deductionsData.filter(deduction => {
      try {
        // Safe property access with fallback
        const name = deduction?.name?.toLowerCase() || '';
        const id = deduction?._id?.toLowerCase() || deduction?.id?.toLowerCase() || '';
        
        const matchesName = name.includes(query);
        const matchesId = id.includes(query);
        
        return matchesName || matchesId;
      } catch (error) {
        console.error('Error filtering deduction:', error, deduction);
        return false;
      }
    });
    
    console.log('Filtered Results:', filtered);
    return filtered;
  }, [deductionsData, searchQuery]);

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentDeductions = filteredDeductions.slice(indexOfFirstItem, indexOfLastItem);

  const paginate = (pageNumber: any) => setCurrentPage(pageNumber);

  useEffect(() => {
    if (error) {
      Swal.fire({
        title: 'Error!',
        text: 'Failed to fetch deductions',
        icon: 'error',
        confirmButtonColor: '#f97316',
      });
      console.error('Error fetching deductions:', error);
    }
  }, [error]);

  // Debug logging
  useEffect(() => {
    console.log('Current search state:', {
      searchQuery,
      filteredCount: filteredDeductions.length,
      currentPage,
      currentDeductions: currentDeductions.length
    });
  }, [searchQuery, filteredDeductions, currentPage, currentDeductions]);

  return (
    <div className="p-4 bg-gray-50">
      <div className="flex justify-between items-center md:flex-row flex-col mb-6 border border-gray-200 p-4 rounded-2xl bg-white">
        <h1 className="text-2xl font-bold text-gray-800 mb-2 md:mb-0">Employee Deductions</h1>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Deduction"
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchQuery}
              onChange={handleSearch}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
          </div>
          <button
            onClick={handleAddDeduction}
            className="flex items-center gap-1 cursor-pointer bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Add Deduction
            <Plus size={18} />
          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-start bg-white h-screen pt-[30vh]">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
            <p className="text-gray-600 font-medium">Loading Deductions...</p>
          </div>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-500 mb-4">Failed to load deductions.</p>
          <button 
            onClick={refetch}
            className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg"
          >
            Try Again
          </button>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto rounded-2xl">
            <table className="w-full border border-gray-200 rounded-t-2xl bg-white rounded-2xl">
              <thead className="bg-orange-50">
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-700">No</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Name</th>
                  <th className="text-right py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentDeductions.length > 0 ? (
                  currentDeductions.map((deduction, index) => (
                    <tr key={deduction._id || deduction.id} className="border-b border-gray-200 hover:bg-orange-50">
                      <td className="py-4 px-4 text-gray-800">{indexOfFirstItem + index + 1}</td>
                      <td className="py-4 px-4 text-gray-800">{deduction.name || 'N/A'}</td>
                      <td className="py-4 px-4 flex justify-end gap-2">
                        <button 
                          className="text-blue-500 hover:text-blue-700"
                          onClick={() => handleEditDeduction(deduction._id || deduction.id)}
                          disabled={isDeleting}
                        >
                          <Edit
                            id={`edit-icon-${deduction._id || deduction.id}`}
                            data-tooltip-id="edit-tooltip"
                            data-tooltip-content="Edit"
                            size={20}
                            className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                          />
                          <Tooltip
                            id="edit-tooltip"
                            place="bottom"
                            className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                          />
                        </button>
                        <button 
                          className={`text-red-500 hover:text-red-700 ${isDeleting ? "cursor-not-allowed opacity-50" : ""}`}
                          onClick={() => handleDeleteDeduction(deduction._id || deduction.id)}
                          disabled={isDeleting}
                        >
                          <Trash2
                            id={`delete-icon-${deduction._id || deduction.id}`}
                            data-tooltip-id="delete-tooltip"
                            data-tooltip-content="Delete"
                            size={20}
                            className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                          />
                          <Tooltip
                            id="delete-tooltip"
                            place="bottom"
                            className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                          />
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={3} className="px-6 py-8 text-center text-gray-500">
                      {searchQuery ? `No deductions found matching "${searchQuery}"` : 'No deductions found.'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {filteredDeductions.length > itemsPerPage && (
            <div className="flex justify-start items-center space-x-2 mt-6">
              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  currentPage === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft size={18} className="text-gray-600" />
              </button>

              {Array.from(
                { length: Math.ceil(filteredDeductions.length / itemsPerPage) },
                (_, i) => i + 1
              ).map((number) => (
                <button
                  key={number}
                  onClick={() => paginate(number)}
                  className={`w-8 h-8 rounded-md flex items-center justify-center border ${
                    currentPage === number
                      ? "bg-orange-500 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  {number}
                </button>
              ))}

              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  indexOfLastItem >= filteredDeductions.length
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(Math.min(currentPage + 1, Math.ceil(filteredDeductions.length / itemsPerPage)))}
                disabled={indexOfLastItem >= filteredDeductions.length}
              >
                <ChevronRight size={18} className="text-gray-600" />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}