import { baseApi } from './baseApi';

// Define interfaces for the customer data
export interface Customer {
  _id: string;
  CustomerId: string;
  userId: string;
  FirstName: string;
  LastName: string;
  Phone: string;
  Address: string;
  City: string;
  State: string;
  Email: string;
  isActive: boolean;
 
  CustomerLoyalty?: {
    LastVisit?: string | Date;
    
    CardNo?: string;
    StartDate?: string | Date;
    Points?: number;
    Visits?: number;
    creditLimits?: number;
    ActivateCard?: boolean;
    ExpiresIn?: string | Date;
  };
}
export interface AddCustomerRequest {
  userId: string;
  FirstName: string;
  LastName: string;
  Phone: string;
  Address: string;
  City: string;
  State: string;
  Email: string;
  isActive: boolean;
}

export const customerApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get all customers
    getCustomers: builder.query<Customer[], void>({
      query: () => {
        const userId = localStorage.getItem('userId');
        return {
          url: '/customer',
          params: { userId },
        };
      },
      providesTags: ['Customer'],
    }),
    
    // Add a new customer
    postCustomer: builder.mutation<Customer, AddCustomerRequest>({
      query: (customer) => ({
        url: '/customer',
        method: 'POST',
        body: customer,
      }),
      invalidatesTags: ['Customer'],
    }),
    
    // Update customer
    putCustomer: builder.mutation<Customer, Partial<Customer> & { _id: string }>({
      query: ({ _id, ...customer }) => ({
        url: `/customer/${_id}`,
        method: 'PUT',
        body: customer,
      }),
      invalidatesTags: (_result, _error, { _id }) => [{ type: 'Customer', id: _id }],
    }),
    
    // Delete customer
    deleteCustomer: builder.mutation<void, string>({
      query: (id) => ({
        url: `/customer/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Customer'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetCustomersQuery,
  usePostCustomerMutation,
  usePutCustomerMutation,
  useDeleteCustomerMutation,
} = customerApi;
