import { useState, useEffect, useRef } from 'react';
import { ChevronLeft, X, ChevronDown } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import Swal from 'sweetalert2';
import {
  usePostEmployeeListMutation,
  useGetEmployeesListQuery,
  useGetDeductionsQuery,
  usePutEmployeeListMutation,
  useGetEmployeeListQuery
} from '../../../store/api/EmployeeListApi';

export default function EmployeeForm() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  
  const userId = localStorage.getItem('userId') || '';
  
  // Fixed: Proper typing for dropdown ref
  const [isDeductionDropdownOpen, setIsDeductionDropdownOpen] = useState(false);
  const deductionDropdownRef = useRef<HTMLDivElement>(null);

  const [postEmployeeList, { isLoading: isCreating }] = usePostEmployeeListMutation();
  const [putEmployeeList, { isLoading: isUpdating }] = usePutEmployeeListMutation();
  

  const { data: employeeData, isLoading: isLoadingEmployee } = useGetEmployeeListQuery(id || '', {
    skip: !isEditMode, // Skip fetching if not in edit mode
  });
  
  // Fetch employees list to keep the cache updated (for edit mode) - not needed anymore since we're using the useGetEmployeeListQuery query with the id parameter (see below)
  const { data: _ } = useGetEmployeesListQuery(userId, { skip: !isEditMode }); // Skip fetching if not in edit mode

  // Fetch available deductions
  const { data: deductions = [], isLoading: isLoadingDeductions,refetch
  } = useGetDeductionsQuery();

  const [formData, setFormData] = useState({
    employeeId: '',
    firstName: '',
    lastName: '',
    email: '',
    contactNumber: '',
    address: '',
    employeeType: '',
    hourlyRate: '',
    shiftHours: '',
    arrivalTime: '',
    departureTime: '',
    overtimeHourlyRate: '',
    userId: userId,
    deductions: [] as Array<{ name: string; amount: number; }>
  });

  // Handle clicking outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (deductionDropdownRef.current && !deductionDropdownRef.current.contains(event.target as Node)) {
        setIsDeductionDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Populate form data when employee data is fetched (for edit mode)
  useEffect(() => {
    if (isEditMode && employeeData) {
      setFormData({
        employeeId: employeeData.employeeId || '',
        firstName: employeeData.firstName || '',
        lastName: employeeData.lastName || '',
        email: employeeData.email || '',
        contactNumber: employeeData.phoneNo || '',
        address: employeeData.address || '',
        employeeType: employeeData.employeeType || '',
        hourlyRate: employeeData.hourlyRate?.toString() || '',
        shiftHours: employeeData.totalHours?.toString() || '',
        arrivalTime: employeeData.employeeStartTime || '',
        departureTime: employeeData.employeeEndTime || '',
        overtimeHourlyRate: employeeData.overTimeRate?.toString() || '',
        userId: userId,
        deductions: employeeData.deductions?.map(d => ({
          name:  d.name || '', // Handle both formats
          amount: d.amount || 0
        })) || []
      });
    }
  }, [employeeData, isEditMode, userId]);

  // For debugging purposes
  useEffect(() => {
    if (deductions && deductions.length > 0) {
      console.log('Deductions loaded:', deductions);
    }
  }, [deductions]);

  const handleInputChange = (e: { target: { name: any; value: any; }; }) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle toggling deductions (new multi-checkbox functionality)
  const handleDeductionToggle = (deductionId: string) => {
    console.log('Selected deduction ID:', deductionId);
    
    if (!deductionId) return;
    
    // Find the selected deduction from the list
    const selectedDeduction = deductions.find(d => d._id === deductionId);
    
    console.log('Selected deduction object:', selectedDeduction);
    
    if (selectedDeduction) {
      // Check if this deduction is already added
      const isAlreadyAdded = formData.deductions.some(d => d.name === selectedDeduction.name);
      
      if (isAlreadyAdded) {
        // Remove the deduction if it's already added
        setFormData({
          ...formData,
          deductions: formData.deductions.filter(d => d.name !== selectedDeduction.name)
        });
      } else {
        // Add the new deduction to the form data
        setFormData({
          ...formData,
          deductions: [
            ...formData.deductions,
            {
              name: selectedDeduction.name,
              amount: selectedDeduction.defaultAmount || selectedDeduction.amount || 0,
            }
          ]
        });
      }
    } else {
      console.error('Could not find deduction with ID:', deductionId);
    }
  };

  // Helper function to check if a deduction is selected
  const isDeductionSelected = (deductionId: string) => {
    const deduction = deductions.find(d => d._id === deductionId);
    return deduction ? formData.deductions.some(d => d.name === deduction.name) : false;
  };

  // Removed: handleAddDeduction function as it's not being used and duplicates handleDeductionToggle functionality

  // Handle removing a deduction
  const handleRemoveDeduction = (index: number) => {
    const updatedDeductions = [...formData.deductions];
    updatedDeductions.splice(index, 1);
    
    setFormData({
      ...formData,
      deductions: updatedDeductions
    });
  };

  // Handle changing deduction amount
  const handleDeductionAmountChange = (index: number, amount: number) => {
    const updatedDeductions = [...formData.deductions];
    updatedDeductions[index].amount = amount;
    
    setFormData({
      ...formData,
      deductions: updatedDeductions
    });
  };

  const handleSubmit = async (e: { preventDefault: () => void; }) => {
    e.preventDefault();
    
    try {
      // Transform form data to match API expectations
      const submissionData = {
        employeeId: formData.employeeId,
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phoneNo: formData.contactNumber,
        address: formData.address,
        employeeType: formData.employeeType,
        hourlyRate: parseFloat(formData.hourlyRate) || 0,
        totalHours: parseFloat(formData.shiftHours) || 0,
        employeeStartTime: formData.arrivalTime,
        employeeEndTime: formData.departureTime,
        overTimeRate: parseFloat(formData.overtimeHourlyRate) || 0,
        userId: userId,
        deductions: formData.deductions.map(d => ({
          name: d.name, 
          amount: d.amount
        }))
      };

      console.log(`${isEditMode ? 'Updating' : 'Submitting'} employee data:`, submissionData);
      
      // Call the appropriate API based on mode (create or update)
      if (isEditMode && id) {
        await putEmployeeList({ id, data: submissionData }).unwrap();
         await refetch();
        Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'Employee updated successfully!',
          confirmButtonColor: '#f97316'
        });
           navigate('/admin/employees/employee-list');
      } else {
        await postEmployeeList(submissionData).unwrap();
        Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'Employee added successfully!',
          confirmButtonColor: '#f97316'
        });
      }
      
      
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} employee:`, error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: `Failed to ${isEditMode ? 'update' : 'add'} employee. Please try again.`,
        confirmButtonColor: '#f97316'
      });
    }
  };

  const handleCancel = () => {
    console.log('Form cancelled');
    navigate('/admin/employees/employee-list');
  };

  // Show loading state while fetching employee data in edit mode
  if (isEditMode && isLoadingEmployee) {
    return (
      <div className="flex justify-center items-start bg-white h-screen pt-[40vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading Employees List...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 p-4 shadow">
      <div className="flex items-center mb-6 border border-gray-200 bg-white p-4 rounded-2xl">
        <button 
          className="flex items-center cusrsor-pointer text-gray-700 mr-2"
          onClick={handleCancel}
        >
          <ChevronLeft size={20} />
        </button>
        <h1 className="text-xl font-bold">{isEditMode ? 'Edit Employee' : 'Add Employee'}</h1>
      </div>
      
      <form onSubmit={handleSubmit}>
        {/* Employee Details Section */}
        <div className="rounded-2xl border border-gray-200 bg-white mb-6">
          <h2 className="p-4 text-lg font-medium text-gray-800 rounded-t-2xl bg-orange-50">Employee Details</h2>
          <div className="px-4 pt-2 grid grid-cols-1 md:grid-cols-3 gap-6 mb-2">
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Employee ID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="employeeId"
                placeholder="Enter Employee ID"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.employeeId}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                First Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="firstName"
                placeholder="Enter First Name"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.firstName}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Last Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="lastName"
                placeholder="Enter Last Name"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.lastName}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>
        </div>
        
        {/* Contact Details Section */}
        <div className="border border-gray-200 rounded-2xl bg-white mb-6">
          <h2 className="text-lg font-medium text-gray-800 rounded-t-2xl p-4 bg-orange-50">Contact Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-4">
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Email Address <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                name="email"
                placeholder="Enter Email Address"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Contact Number <span className="text-red-500">*</span>
              </label>
              <input
                type="tel"
                name="contactNumber"
                placeholder="Enter Contact Number"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.contactNumber}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Address <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="address"
                placeholder="Enter Address"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.address}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>
        </div>
        
        {/* Roles and Salary Section */}
        <div className="bg-white border border-gray-200 rounded-2xl mb-6">
          <h2 className="text-lg p-4 font-medium text-gray-800 bg-orange-50 rounded-t-2xl">Roles and Salary</h2>
          <div className="p-4 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Employee Type <span className="text-red-500">*</span>
              </label>
              <select
                name="employeeType"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.employeeType}
                onChange={handleInputChange}
                required
              >
                <option value="">Select employee type</option>
                <option value="waiter">Waiter</option>
                <option value="cashier">Cashier</option>
                <option value="manager">Manager</option>
                <option value="cook">Cook</option>
              </select>
            </div>
            
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Hourly Rate <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="hourlyRate"
                placeholder="Enter Hourly Rate"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.hourlyRate}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Shift Hours <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="shiftHours"
                placeholder="Enter Shift Hours"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.shiftHours}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-4">
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Arrival Time <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="time"
                  name="arrivalTime"
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  value={formData.arrivalTime}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Departure Time <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="time"
                  name="departureTime"
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  value={formData.departureTime}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Overtime Hourly Rate <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="overtimeHourlyRate"
                placeholder="Enter Overtime Hourly Rate"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.overtimeHourlyRate}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>
        </div>
        
        {/* Deductions Section - Updated with Multi-Checkbox Dropdown */}
  
        <div className="bg-white border border-gray-200 rounded-2xl mb-6">
          <h2 className="text-lg p-4 font-medium text-gray-800 bg-orange-50 rounded-t-2xl">Deductions</h2>
          
          <div className="p-4">
            <div className="flex items-center mb-4">
              <label className="text-sm font-medium text-gray-900 mr-2">
                Add Deduction
              </label>
              
              <div className="relative flex-grow max-w-md" ref={deductionDropdownRef}>
                <button
                  type="button"
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 bg-white text-left flex items-center justify-between"
                  onClick={() => setIsDeductionDropdownOpen(!isDeductionDropdownOpen)}
                  disabled={isLoadingDeductions}
                >
                  <span className="text-gray-700">
                    {formData.deductions.length > 0 
                      ? `${formData.deductions.length} deduction(s) selected`
                      : 'Select deductions'
                    }
                  </span>
                  <ChevronDown 
                    size={16} 
                    className={`transform transition-transform ${isDeductionDropdownOpen ? 'rotate-180' : ''}`}
                  />
                </button>

                {isDeductionDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    {deductions && deductions.length > 0 ? (
                      deductions.map((deduction) => (
                        <label
                          key={deduction._id}
                          className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={isDeductionSelected(deduction._id)}
                            onChange={() => handleDeductionToggle(deduction._id)}
                            className="mr-3 h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 rounded"
                          />
                          <span className="text-sm text-gray-900">
                            {deduction.name || 'Unnamed Deduction'}
                          </span>
                        </label>
                      ))
                    ) : (
                      <div className="px-3 py-2 text-sm text-gray-500">
                        No deductions available
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Display selected deductions */}
            {formData.deductions.length > 0 && (
              <div className="space-y-3 mt-4">
                <h3 className="text-sm font-medium text-gray-900">Applied Deductions:</h3>
                {formData.deductions.map((deduction, index) => (
                  <div key={index} className="flex items-center gap-4 p-3 border border-gray-200 rounded-lg bg-gray-50">
                    <div className="font-medium min-w-0 flex-shrink-0">{deduction.name}</div>
                    <div className="flex-grow flex items-center gap-2">
                      <label className="text-sm text-gray-600 whitespace-nowrap">
                        Percentage Deduction for {deduction.name.toLowerCase()}
                      </label>
                      <input
                        type="number"
                        value={deduction.amount}
                        onChange={(e) => handleDeductionAmountChange(index, parseFloat(e.target.value))}
                        className="w-32 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                        placeholder="0"
                        min="0"
                        max="100"
                        step="0.01"
                      />
                      <span className="text-sm text-gray-500">%</span>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveDeduction(index)}
                      className="text-red-500 hover:text-red-700 flex-shrink-0"
                    >
                      <X size={18} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        <div className="flex justify-end gap-4 mt-8">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 border cursor-pointer border-orange-500 text-orange-500 rounded-md hover:bg-orange-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isCreating || isUpdating}
            className={`px-6 py-2 ${(isCreating || isUpdating) ? 'bg-orange-300' : 'bg-orange-500 hover:bg-orange-600'} text-white rounded-md transition-colors cursor-pointer`}
          >
            {isEditMode ? 
              (isUpdating ? 'Updating...' : 'Update Employee') : 
              (isCreating ? 'Adding...' : 'Add Employee')
            }
          </button>
        </div>
      </form>
    </div>
  );
}