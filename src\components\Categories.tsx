import { ChevronDown } from "lucide-react";

type Props = {
  categories: string[];
  selected?: string;
  onSelect: (category: string) => void;
};

const Categories: React.FC<Props> = ({ categories, selected, onSelect }) => {
  return (
    <div>
      <div className="relative max-w-full  flex overflow-x-auto whitespace-nowrap gap-2 px-5 py-4 mt-2 scrollbar-hide 
    "

      >
        {categories.map((cat) => (
          <button
            key={cat}
            onClick={() => onSelect(cat)}
            className={`px-4 py-2 mx-1 cursor-pointer hover:text-orange  hover:font-bold hover:border-orange rounded-full text-nowrap border text-sm flex items-center ${selected === cat && cat === "Discount"
              ? " bg-white text-light-black border-gray-300"
              : selected === cat
                ? "text-orange  font-bold border-orange"
                : "bg-white text-light-black border-gray-300"
              }`}
          >
            {cat}
            {cat === "Discount" && <ChevronDown className="ml-1" size={14} />}
          </button>
        ))}
      </div>
    </div>

  );
};

export default Categories;
