import React, { useEffect, useRef, useState } from "react";
import CustomModal from "../../CustomModal";
import OrderDetailsModal from "../OrderDetailsModal";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format, parseISO } from "date-fns";
import { FiSearch } from "react-icons/fi";
import { useGetLastWeekOrdersQuery } from "../../../store/api/pos/orderapi";

interface LoadReceiptsProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Receipt {
  receiptNumber: string;
  operatorName: string;
  paymentType: string;
  customer: string;
  points: number;
  recordDate: string;
  tax: number;
  amount: number;
  receivedAmount: number;
  dueAmount: number;
  date: string;
  product?: any[];
}

const LoadReceipts: React.FC<LoadReceiptsProps> = ({ isOpen, onClose }) => {
  // Initialize date range to null for no initial filtering
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ]);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;
  const [currentPage, setCurrentPage] = useState(1);
  const ordersPerPage = 5; // Number of orders to display per page
  const userId = localStorage.getItem("userId");
  const { data, isLoading, error } = useGetLastWeekOrdersQuery(userId || "");

  // State for order details modal
  const [isOrderDetailsOpen, setIsOrderDetailsOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      setDateRange([selection.startDate || null, selection.endDate || null]);
      // Reset to first page when date range changes
      setCurrentPage(1);
    }
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "Select Date Range";
    return `${format(startDate, "MMM dd, yyyy")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };

  // Transform API data to Receipt format
  const transformReceiptsData = (): Receipt[] => {
    if (!data || !Array.isArray(data)) return [];

    return data.map((order: any) => {
      // Calculate total tax amount
      const totalTax = order.tax?.reduce((sum: number, tax: any) => sum + (tax.addtax || 0), 0) || 0;

      // Format date
      const orderDate = new Date(order.createdAt);
      const formattedDate = format(orderDate, "yyyy-MM-dd");

      return {
        ...order,
        receiptNumber: order.OrderNumber || "",
        operatorName: "System", // Default value as it's not in the API response
        paymentType: typeof order.paymentType === 'object' && order.paymentType?.name
          ? order.paymentType.name
          : order.paymentType || "Cash", // Handle both object and string formats
        customer: order.customername || "Guest",
        points: 0, // Default value as it's not in the API response
        recordDate: formattedDate,
        tax: totalTax,
        amount: order.grandTotal || 0,
        receivedAmount: order.recieveamount || 0,
        dueAmount: (order.grandTotal || 0) - (order.recieveamount || 0),
        date: formattedDate,
        product: order.product || []
      };
    });
  };

  // Get transformed receipts data
  const receipts = transformReceiptsData();

  // Debug logging
  useEffect(() => {
    if (searchTerm) {
      console.log('Search term:', searchTerm);
      console.log('Available customers:', receipts.map(r => r.customer));
    }
  }, [searchTerm, receipts]);

  useEffect(() => {
    if (startDate && endDate) {
      console.log('Date range:', startDate, 'to', endDate);
      console.log('Available dates:', receipts.map(r => r.date));
    }
  }, [startDate, endDate, receipts]);

  // Enhanced filtering function with both name and date filters
  const filteredReceipts = receipts.filter(receipt => {
    // Name/Receipt Number filter - more flexible search
    const matchesSearch = searchTerm.trim() === "" || 
      receipt.customer?.toLowerCase().includes(searchTerm.toLowerCase().trim()) ||
      receipt.receiptNumber?.toLowerCase().includes(searchTerm.toLowerCase().trim()) ||
      // Split search term and check if each word is found
      searchTerm.toLowerCase().trim().split(/\s+/).every(word => 
        receipt.customer?.toLowerCase().includes(word) || 
        receipt.receiptNumber?.toLowerCase().includes(word)
      );

    // Date range filter
    const matchesDateRange = (() => {
      if (!startDate || !endDate) return true; // If no date range selected, show all
      
      try {
        // Try multiple date formats
        let receiptDate;
        
        if (receipt.date) {
          // Try parsing as ISO string first
          if (receipt.date.includes('T') || receipt.date.includes('-')) {
            receiptDate = new Date(receipt.date);
          } else {
            receiptDate = parseISO(receipt.date);
          }
        } else if (receipt.recordDate) {
          receiptDate = new Date(receipt.recordDate);
        } else {
          return true; // If no date available, include the record
        }
        
        // Validate the date
        if (isNaN(receiptDate.getTime())) {
          console.warn('Invalid date found:', receipt.date || receipt.recordDate);
          return true;
        }
        
        // Set time to start of day for proper comparison
        const startOfDay = new Date(startDate);
        startOfDay.setHours(0, 0, 0, 0);
        
        const endOfDay = new Date(endDate);
        endOfDay.setHours(23, 59, 59, 999);
        
        const receiptDateOnly = new Date(receiptDate);
        receiptDateOnly.setHours(0, 0, 0, 0);
        
        // Check if receipt date is within the selected range
        return receiptDateOnly >= startOfDay && receiptDateOnly <= endOfDay;
        
      } catch (error) {
        console.error('Error parsing date:', error, receipt);
        return true; // If date parsing fails, include the record
      }
    })();

    return matchesSearch && matchesDateRange;
  });

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredReceipts.length / ordersPerPage);
  const indexOfLastReceipt = currentPage * ordersPerPage;
  const indexOfFirstReceipt = indexOfLastReceipt - ordersPerPage;
  const currentReceipts = filteredReceipts.slice(indexOfFirstReceipt, indexOfLastReceipt);

  // Handle page changes
  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  // Clear filters function
  const clearFilters = () => {
    setSearchTerm("");
    setDateRange([null, null]);
    setCurrentPage(1);
  };

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage === 1 ? "text-gray-400 cursor-not-allowed" : "text-[#9C9C9C] cursor-pointer"
            }`}
          onClick={handlePreviousPage}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage === totalPages || totalPages === 0
            ? "text-gray-400 cursor-not-allowed"
            : "text-[#9C9C9C] cursor-pointer"
            }`}
          onClick={handleNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          Next →
        </button>
      </div>
      <button
        onClick={onClose}
        className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
      >
        Cancel
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Load Receipts"
      width="max-w-7xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search and Date Filter */}
        <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
          <div className="relative flex-1 mr-8">
            <div className="flex items-center">
              <FiSearch className="text-gray-400 mr-2" size={20} />
              <input
                type="text"
                placeholder="Search Customer or Receipt Number"
                className="w-full py-2 focus:outline-none text-base"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
          </div>
          <div className="relative mx-15 flex items-center gap-3">
            <button
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
              className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md cursor-pointer"
            >
              {formatDateRange()}
            </button>
            {/* Clear Filters Button */}
            {(searchTerm.trim() || (startDate && endDate)) && (
              <button
                onClick={clearFilters}
                className="bg-gray-100 hover:bg-gray-200 text-gray-600 px-3 py-2 rounded-lg text-sm transition-colors"
              >
                Clear Filters
              </button>
            )}
            {isCalendarOpen && (
              <div
                ref={calendarRef}
                className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
              >
                <DateRangePicker
                  ranges={[
                    {
                      startDate: startDate || new Date(),
                      endDate: endDate || new Date(),
                      key: "selection",
                    },
                  ]}
                  onChange={handleDateRangeChange}
                  direction="horizontal"
                  moveRangeOnFirstSelection={false}
                />
              </div>
            )}
          </div>
        </div>

        {/* Filter Summary */}
        {(searchTerm || (startDate && endDate)) && (
          <div className="mb-4 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
            <div className="flex flex-wrap gap-2 items-center">
              <span>Active filters:</span>
              {searchTerm && (
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                  Search: "{searchTerm}"
                </span>
              )}
              {startDate && endDate && (
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                  Date: {formatDateRange()}
                </span>
              )}
              <span className="ml-2 font-medium">
                ({filteredReceipts.length} of {receipts.length} receipts)
              </span>
            </div>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-10">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-orange"></div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-10 text-red-500">
            Failed to load receipts. Please try again later.
          </div>
        )}

        {/* Empty State */}
        {!isLoading && !error && filteredReceipts.length === 0 && receipts.length > 0 && (
          <div className="text-center py-10 text-gray-500">
            <div className="mb-2">No receipts found matching your filters.</div>
            <button
              onClick={clearFilters}
              className="text-blue-500 hover:text-blue-700 underline"
            >
              Clear all filters
            </button>
          </div>
        )}

        {/* No Data State */}
        {!isLoading && !error && receipts.length === 0 && (
          <div className="text-center py-10 text-gray-500">
            No receipts available.
          </div>
        )}

        {/* Table */}
        {!isLoading && !error && filteredReceipts.length > 0 && (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left border-b border-gray-200">
                  <th className="pb-3 text-gray-500 font-normal">
                    Receipt Number
                  </th>
                  <th className="pb-3 text-gray-500 font-normal">
                    Operator Name
                  </th>
                  <th className="pb-3 text-gray-500 font-normal">Payment Type</th>
                  <th className="pb-3 text-gray-500 font-normal">Customer</th>
                  <th className="pb-3 text-gray-500 font-normal">Points</th>
                  <th className="pb-3 text-gray-500 font-normal">Record Date</th>
                  <th className="pb-3 text-gray-500 font-normal">Tax</th>
                  <th className="pb-3 text-gray-500 font-normal">Amount</th>
                  <th className="pb-3 text-gray-500 font-normal">
                    Received Amount
                  </th>
                  <th className="pb-3 text-gray-500 font-normal">Due Amount</th>
                  <th className="pb-3 text-gray-500 font-normal">Date</th>
                  <th className="pb-3 text-gray-500 font-normal">Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentReceipts.map((receipt, index) => (
                  <tr key={index} className="border-b border-gray-200">
                    <td className="py-4">{receipt.receiptNumber}</td>
                    <td className="py-4">{receipt.operatorName}</td>
                    <td className="py-4">{receipt.paymentType}</td>
                    <td className="py-4">{receipt.customer}</td>
                    <td className="py-4">{receipt.points}</td>
                    <td className="py-4">{receipt.recordDate}</td>
                    <td className="py-4">${receipt.tax.toFixed(2)}</td>
                    <td className="py-4">${receipt.amount.toFixed(2)}</td>
                    <td className="py-4">${receipt.receivedAmount.toFixed(2)}</td>
                    <td className="py-4">${receipt.dueAmount.toFixed(2)}</td>
                    <td className="py-4">{receipt.date}</td>
                    <td className="py-4">
                      <button
                        className="bg-orange text-white px-5 py-2 rounded-full text-sm font-semibold cursor-pointer"
                        onClick={() => {
                          setSelectedOrder(receipt);
                          setIsOrderDetailsOpen(true);
                        }}
                      >
                        View Receipt
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination Info */}
        {!isLoading && !error && filteredReceipts.length > 0 && (
          <div className="text-right mt-4 text-sm text-gray-500">
            Showing {indexOfFirstReceipt + 1} to {Math.min(indexOfLastReceipt, filteredReceipts.length)} of {filteredReceipts.length} receipts
            {filteredReceipts.length !== receipts.length && (
              <span> (filtered from {receipts.length} total)</span>
            )}
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {isOrderDetailsOpen && (
        <OrderDetailsModal
          isOpen={isOrderDetailsOpen}
          onClose={() => setIsOrderDetailsOpen(false)}
          order={selectedOrder}
        />
      )}
    </CustomModal>
  );
};

export default LoadReceipts;