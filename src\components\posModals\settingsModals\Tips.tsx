import React, { useEffect, useRef, useState } from "react";
import CustomModal from "../../CustomModal";
import { AiOutlineEye } from "react-icons/ai";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format } from "date-fns";
import { FiSearch } from "react-icons/fi";

interface TipsProps {
  isOpen: boolean;
  onClose: () => void;
}

interface TipRecord {
  receiptNumber: string;
  operatorName: string;
  amount: number;
  tip: number;
  employee: string;
}

const Tips: React.FC<TipsProps> = ({ isOpen, onClose }) => {
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    new Date(),
    new Date(),
  ]);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;
  const [currentPage, setCurrentPage] = useState(1);

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      setDateRange([selection.startDate || null, selection.endDate || null]);
    }
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "Select Date Range";
    return `${format(startDate, "MMM dd, yyyy ")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };

  const tips: TipRecord[] = [
    {
      receiptNumber: "REC001",
      operatorName: "John Doe",
      amount: 100.75,
      tip: 10.75,
      employee: "Alice Smith",
    },
    {
      receiptNumber: "REC002",
      operatorName: "Jane Doe",
      amount: 150.5,
      tip: 10.5,
      employee: "Bob Johnson",
    },
    {
      receiptNumber: "REC003",
      operatorName: "John Doe",
      amount: 100.75,
      tip: 10.75,
      employee: "Alice Smith",
    },
    {
      receiptNumber: "REC004",
      operatorName: "Jane Doe",
      amount: 150.5,
      tip: 10.5,
      employee: "Bob Johnson",
    },
    {
      receiptNumber: "REC005",
      operatorName: "John Doe",
      amount: 100.75,
      tip: 10.75,
      employee: "Alice Smith",
    },
  ];
  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button className="px-4 py-2 text-sm font-medium text-[#9C9C9C] cursor-pointer"
        onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}>
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button className="px-4 py-2 text-sm font-medium text-[#9C9C9C] cursor-pointer"
        onClick={()=> setCurrentPage((prev) => prev + 1)}>
          Next →
        </button>
      </div>
      <button
        onClick={onClose}
        className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
      >
        Cancel
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Tips"
      width="max-w-5xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search and Date Filter */}
        <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
          <div className="relative flex-1 mr-8">
            <div className="flex items-center">
              <FiSearch className="text-gray-400 mr-2" size={20} />
              <input
                type="text"
                placeholder="Search Customer"
                className="w-full py-2 focus:outline-none text-base"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
          </div>
          <div className="relative mx-15">
            <button
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
              className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md  cursor-pointer"
            >
              {formatDateRange()}
            </button>
            {isCalendarOpen && (
              <div
                ref={calendarRef}
                className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
              >
                <DateRangePicker
                  ranges={[
                    {
                      startDate: startDate || new Date(),
                      endDate: endDate || new Date(),
                      key: "selection",
                    },
                  ]}
                  onChange={handleDateRangeChange}
                  direction="horizontal"
                  // showSelectionPreview={true}
                  moveRangeOnFirstSelection={false}
                />
              </div>
            )}
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left border-b border-gray-200">
                <th className="pb-3 text-gray-500 font-normal">
                  Receipt Number
                </th>
                <th className="pb-3 text-gray-500 font-normal">
                  Operator Name
                </th>
                <th className="pb-3 text-gray-500 font-normal">Amount</th>
                <th className="pb-3 text-gray-500 font-normal">Tip</th>
                <th className="pb-3 text-gray-500 font-normal">Employee</th>
                <th className="pb-3 text-gray-500 font-normal">Actions</th>
              </tr>
            </thead>
            <tbody>
              {tips.map((tip, index) => (
                <tr key={index} className="border-b border-gray-200">
                  <td className="py-4">{tip.receiptNumber}</td>
                  <td className="py-4">{tip.operatorName}</td>
                  <td className="py-4">${tip.amount.toFixed(2)}</td>
                  <td className="py-4">${tip.tip.toFixed(2)}</td>
                  <td className="py-4">{tip.employee}</td>
                  <td className="py-4">
                    <AiOutlineEye
                      className="text-orange-500 cursor-pointer"
                      size={20}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </CustomModal>
  );
};

export default Tips;
