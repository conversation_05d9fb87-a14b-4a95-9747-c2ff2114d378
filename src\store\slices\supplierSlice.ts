import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { Supplier } from '../api/supplierApi';
import { supplierApi } from '../api/supplierApi';

interface SupplierState {
  suppliers: Supplier[];
  isLoading: boolean;
  error: string | null;
}

const initialState: SupplierState = {
  suppliers: [],
  isLoading: false,
  error: null,
};

const supplierSlice = createSlice({
  name: 'supplier',
  initialState,
  reducers: {
    setSuppliers: (state, action: PayloadAction<Supplier[]>) => {
      state.suppliers = action.payload;
    },
    clearSuppliers: (state) => {
      state.suppliers = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET
      .addMatcher(
        supplierApi.endpoints.getSuppliers.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        supplierApi.endpoints.getSuppliers.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.suppliers = payload;
        }
      )
      .addMatcher(
        supplierApi.endpoints.getSuppliers.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch suppliers';
        }
      )

      // DELETE
      .addMatcher(
        supplierApi.endpoints.deleteSupplier.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        supplierApi.endpoints.deleteSupplier.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const supplierId = action.meta.arg.originalArgs as string;
          state.suppliers = state.suppliers.filter(
            (supplier) => supplier.id !== supplierId
          );
        }
      )
      .addMatcher(
        supplierApi.endpoints.deleteSupplier.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete supplier';
        }
      )

      // POST
      .addMatcher(
        supplierApi.endpoints.postSupplier.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        supplierApi.endpoints.postSupplier.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.suppliers.push({
            id: payload._id,
            SupplierID: payload.SupplierID,
            SupplierName: payload.SupplierName,
            EmailAddress: payload.EmailAddress,
            ContactNumber: payload.ContactNumber,
            BussinessAddress: payload.BussinessAddress,
            TaxIdentification: payload.TaxIdentification,
            OtherInformation: payload.OtherInformation,
            ProductOffered: payload.ProductOffered,
          });
        }
      )
      .addMatcher(
        supplierApi.endpoints.postSupplier.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create supplier';
        }
      )

      // PUT
      .addMatcher(
        supplierApi.endpoints.putSupplier.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        supplierApi.endpoints.putSupplier.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          const updated = {
            id: payload.data._id,
            SupplierID: payload.data.SupplierID,
            SupplierName: payload.data.SupplierName,
            EmailAddress: payload.data.EmailAddress,
            ContactNumber: payload.data.ContactNumber,
            BussinessAddress: payload.data.BussinessAddress,
            TaxIdentification: payload.data.TaxIdentification,
            OtherInformation: payload.data.OtherInformation,
            ProductOffered: payload.data.ProductOffered,
          };
          state.suppliers = state.suppliers.map((supplier) =>
            supplier.id === updated.id ? updated : supplier
          );
        }
      )
      .addMatcher(
        supplierApi.endpoints.putSupplier.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update supplier';
        }
      );
  },
});

export const { setSuppliers, clearSuppliers } = supplierSlice.actions;

export const selectSuppliers = (state: RootState) => state.supplier.suppliers;
export const selectSupplierLoading = (state: RootState) => state.supplier.isLoading;
export const selectSupplierError = (state: RootState) => state.supplier.error;

export default supplierSlice.reducer;