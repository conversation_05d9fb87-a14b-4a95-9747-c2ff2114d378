import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type {  Customization } from '../api/customizationApi';
import { customizationApi } from '../api/customizationApi';

interface CustomizationState {
  customizations: Customization[];
  isLoading: boolean;
  error: string | null;
}

const initialState: CustomizationState = {
  customizations: [],
  isLoading: false,
  error: null,
};

const customizationSlice = createSlice({
  name: 'customizations',
  initialState,
  reducers: {
    setCustomizations: (state, action: PayloadAction<Customization[]>) => {
      state.customizations = action.payload;
    },
    clearCustomizations: (state) => {
      state.customizations = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET
      .addMatcher(
        customizationApi.endpoints.getCustomizations.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        customizationApi.endpoints.getCustomizations.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.customizations = payload;
        }
      )
      .addMatcher(
        customizationApi.endpoints.getCustomizations.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch customizations';
        }
      )

      // DELETE
      .addMatcher(
        customizationApi.endpoints.deleteCustomization.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        customizationApi.endpoints.deleteCustomization.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const customizationId = action.meta.arg.originalArgs as string;
          state.customizations = state.customizations.filter(
            (customization) => customization.id !== customizationId
          );
        }
      )
      .addMatcher(
        customizationApi.endpoints.deleteCustomization.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete customization';
        }
      )

      // POST
      .addMatcher(
        customizationApi.endpoints.postCustomization.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        customizationApi.endpoints.postCustomization.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.customizations.push({
            id: payload._id,
            name: payload.name,
            active: payload.active,
            userId: payload.userId
          });
        }
      )
      .addMatcher(
        customizationApi.endpoints.postCustomization.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create customization';
        }
      )

      // PUT
      .addMatcher(
        customizationApi.endpoints.putCustomization.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        customizationApi.endpoints.putCustomization.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          const updated = {
            id: payload.data._id,
            name: payload.data.name,
            active: payload.data.active,
            userId: payload.data.userId
          };
          state.customizations = state.customizations.map((customization) =>
            customization.id === updated.id ? updated : customization
          );
        }
      )
      .addMatcher(
        customizationApi.endpoints.putCustomization.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update customization';
        }
      );
  },
});

export const { setCustomizations, clearCustomizations } = customizationSlice.actions;

export const selectCustomizations = (state: RootState) => state.customizations.customizations;
export const selectCustomizationsLoading = (state: RootState) => state.customizations.isLoading;
export const selectCustomizationsError = (state: RootState) => state.customizations.error;

export default customizationSlice.reducer;