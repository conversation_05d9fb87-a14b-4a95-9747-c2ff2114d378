import { baseApi } from "./baseApi";

export interface Customization {
  id: string;
  name: string;
  active: boolean;
  userId: string;
}

export const customizationApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCustomizations: builder.query<Customization[], string>({
      query: (userId) => `/customization?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          name: item.name,
          active: item.active,
          userId: item.userId
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "Customization" as const, id })),
              { type: "Customization", id: "LIST" },
            ]
          : [{ type: "Customization", id: "LIST" }],
    }),

    getCustomization: builder.query<Customization, string>({
      query: (id) => `/customization/${id}`,
      transformResponse: (response: any) => ({
        id: response._id,
        name: response.name,
        active: response.active,
        userId: response.userId
      }),
      providesTags: (_result, _error, id) => [{ type: "Customization", id }],
    }),

    deleteCustomization: builder.mutation<void, string>({
      query: (id) => ({
        url: `/customization/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            customizationApi.util.invalidateTags([
              { type: "Customization", id },
              { type: "Customization", id: "LIST" }
            ])
          );
        } catch (error) {
          console.error("Failed to delete Customization", error);
        }
      },
    }),

    postCustomization: builder.mutation<any, any>({
      query: (data) => {
        console.log("Sending POST request with data:", data);
        return {
          url: "/customization",
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: [{ type: "Customization", id: "LIST" }],
    }),

    putCustomization: builder.mutation<
      any,
      { id: string; formData: any }
    >({
      query: ({ id, formData }) => ({
        url: `/customization/${id}`,
        method: "PUT",
        body: {
          ...formData,
        },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "Customization", id },
        { type: "Customization", id: "LIST" },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetCustomizationsQuery,
  useGetCustomizationQuery,
  useDeleteCustomizationMutation,
  usePostCustomizationMutation,
  usePutCustomizationMutation,
} = customizationApi;