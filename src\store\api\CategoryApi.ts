import { baseApi } from './baseApi.ts';

export interface Category {
  id: string;
  name: string;
  parentCategory?: string;
  totalMenuItems: number;
  status: boolean;
  pictureUrl: string;
}

export const categoryApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCategories: builder.query<Category[], string>({
      query: (userId) => `/category?userId=${userId}`,
      providesTags: ['Category'],
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          name: item.name,
          parentCategory: item.parentId?.name ?? undefined,
          totalMenuItems: Number(item.totalMenuItems ?? 0),
          status: item.active === 'true',
          pictureUrl: item.category_pic || '',
        }));
      },
    }),

    postCategory: builder.mutation<any, FormData>({
      query: (formData) => {
        const userId = localStorage.getItem('userId');
        if (userId) {
          formData.append('userId', userId);
        }
        return {
          url: '/category',
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: ['Category'],
    }),

    // Add delete mutation similar to parent categories
    deleteCategory: builder.mutation<void, string>({
        query: (id) => ({
          url: `/category/${id}`,
          method: 'DELETE',
        }),
        async onQueryStarted(_, { dispatch, queryFulfilled }) {
          try {
            await queryFulfilled;
            dispatch(categoryApi.util.invalidateTags(['Category']));
          } catch (error) {
            console.error('Failed to delete category', error);
          }
        },
      }),

    // Add update mutation similar to parent categories
putCategory: builder.mutation<any, { id: string; formData: FormData }>({
  query: ({ id, formData }) => ({
    url: `/category/${id}`,
    method: 'PUT',
    body: formData,
  }),
  invalidatesTags: ['Category'],
}),
  }),
  overrideExisting: false,
});

export const {
  useGetCategoriesQuery,
  usePostCategoryMutation,
  useDeleteCategoryMutation,
  usePutCategoryMutation,
} = categoryApi;