import React, { createContext, useContext, useState } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { Calendar, Search, X, Download } from 'lucide-react';
import * as XLSX from 'xlsx';
import Swal from 'sweetalert2';

// Filter Context for sharing filter state across components
interface FilterContextType {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  setDateRange: (range: { startDate: string; endDate: string }) => void;
  downloadData: (data: any[], filename: string) => void;
  clearFilters: () => void;
  ordersData: any[];
  setOrdersData: (data: any[]) => void;
  applyFilters: (data: any[]) => any[];
}

const FilterContext = createContext<FilterContextType | undefined>(undefined);

export const useFilters = () => {
  const context = useContext(FilterContext);
  if (!context) {
    throw new Error('useFilters must be used within FilterProvider');
  }
  return context;
};

const MainOrdersLayout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [dateRange, setDateRange] = useState({
    startDate: "",
    endDate: ""
  });
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Add state for orders data
  const [ordersData, setOrdersData] = useState<any[]>([]);

  // Download function to export data to Excel
  const downloadData = (data: any[], filename: string) => {
    if (!data || data.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data Available',
        text: 'No data available to download',
        confirmButtonColor: '#f97316'
      });
      return;
    }

    try {
      // Create a new workbook
      const wb = XLSX.utils.book_new();
      
      // Convert data to worksheet
      const ws = XLSX.utils.json_to_sheet(data);
      
      // Auto-size columns
      const colWidths: any[] = [];
      const headers = Object.keys(data[0] || {});
      headers.forEach((key, index) => {
        const maxLength = Math.max(
          key.length,
          ...data.map(row => String(row[key] || '').length)
        );
        colWidths[index] = { wch: Math.min(maxLength + 2, 50) };
      });
      ws['!cols'] = colWidths;
      
      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, 'Orders');
      
      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const finalFilename = `${filename}_${timestamp}.xlsx`;
      
      // Download file
      XLSX.writeFile(wb, finalFilename);
      
      // Success alert
      Swal.fire({
        icon: 'success',
        title: 'Download Successful!',
        text: `File ${finalFilename} has been downloaded successfully.`,
        confirmButtonColor: '#f97316',
        timer: 3000,
        timerProgressBar: true
      });
      
      console.log('Download completed successfully');
    } catch (error) {
      console.error('Download failed:', error);
      Swal.fire({
        icon: 'error',
        title: 'Download Failed',
        text: 'Failed to download file. Please try again.',
        confirmButtonColor: '#f97316'
      });
    }
  };

  // Function to apply filters to orders data
  const applyFilters = (data: any[]) => {
    if (!data || data.length === 0) return [];

    let filteredData = [...data];

      if (searchTerm && searchTerm.trim()) {
    const searchLower = searchTerm.toLowerCase().trim();
    filteredData = filteredData.filter((order) => {
      // Search in all relevant fields
      const id = String(order.id || '').toLowerCase();
      const operator = String(order.operator || '').toLowerCase();
      const customerName = String(order.customerName || 'Guest').toLowerCase();
      const table = String(order.table || '').toLowerCase();
      const status = String(order.status || '').toLowerCase();
      const paymentStatus = String(order.paymentStatus || '').toLowerCase();
      
      return (
        id.includes(searchLower) ||
        operator.includes(searchLower) ||
        customerName.includes(searchLower) ||
        table.includes(searchLower) ||
        status.includes(searchLower) ||
        paymentStatus.includes(searchLower)
      );
    });
  }

    // Apply date filter
    if (dateRange.startDate || dateRange.endDate) {
      filteredData = filteredData.filter((order) => {
        if (!order.createdAt) return false;
        
        const orderDate = new Date(order.createdAt);
        const startDate = dateRange.startDate ? new Date(dateRange.startDate) : null;
        const endDate = dateRange.endDate ? new Date(dateRange.endDate) : null;

        // Set end date to end of day for proper comparison
        if (endDate) {
          endDate.setHours(23, 59, 59, 999);
        }

        if (startDate && endDate) {
          return orderDate >= startDate && orderDate <= endDate;
        } else if (startDate) {
          return orderDate >= startDate;
        } else if (endDate) {
          return orderDate <= endDate;
        }
        return true;
      });
    }

    return filteredData;
  };

  const clearFilters = () => {
    setSearchTerm("");
    setDateRange({ startDate: "", endDate: "" });
  };

  const hasActiveFilters = searchTerm.trim() || dateRange.startDate || dateRange.endDate;

  const filterContextValue: FilterContextType = {
    searchTerm,
    setSearchTerm,
    dateRange,
    setDateRange,
    downloadData,
    clearFilters,
    ordersData,
    setOrdersData,
    applyFilters
  };

  // Function to handle download of filtered data with confirmation
  const handleDownload = async () => {
    if (!ordersData || ordersData.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data Available',
        text: 'No orders data available to download.',
        confirmButtonColor: '#f97316'
      });
      return;
    }

    const filteredData = applyFilters(ordersData);
    
    if (filteredData.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Matching Orders',
        text: 'No orders match the current filters.',
        confirmButtonColor: '#f97316'
      });
      return;
    }

    // Show confirmation dialog
    const result = await Swal.fire({
      title: 'Download Orders Data',
      text: `You are about to download ${filteredData.length} order(s). Do you want to continue?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#f97316',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, Download!',
      cancelButtonText: 'Cancel',
      reverseButtons: true
    });

    if (result.isConfirmed) {
      // Prepare data with correct field mapping
      const downloadDataArray = filteredData.map((order: any) => ({
        "Order ID": order.id || '',
        "Customer Name": order.customerName || 'Guest',
        "Operator": order.operator || '',
        "Status": order.status || '',
        "Table": order.table || 'No Table',
        "Products Count": order.products || 0,
        "Total Amount": order.price || order.totalAmount || '0.00',
        "Payment Status": order.paymentStatus || '',
        "Created At": order.createdAt || '',
      }));
      
      downloadData(downloadDataArray, "pos_orders");
    }
  };

  return (
      <FilterContext.Provider value={filterContextValue}>
      <div className="p-3 bg-gray-50 min-h-screen">
        {/* Header with title and controls */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-3 p-2 rounded-2xl bg-white border border-gray-200">
          <h1 className="text-3xl font-bold text-gray-900 mb-4 lg:mb-0">Orders</h1>
          
          {/* Search and Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-3 items-stretch sm:items-center ">
            {/* Date Range Input */}
            <div className="relative">
              <button
                onClick={() => setShowDatePicker(!showDatePicker)}
                className="flex items-center px-4 py-2.5 cursor-pointer bg-gray-50 border border-gray-300 rounded-lg text-gray-500 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 min-w-[200px] justify-between"
              >
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                  <span className="text-sm">
                    {dateRange.startDate && dateRange.endDate 
                      ? `${dateRange.startDate} → ${dateRange.endDate}`
                      : 'Start date → End date'
                    }
                  </span>
                </div>
              </button>
              
              {showDatePicker && (
                <div className="absolute right-0 mt-2 p-4 bg-gray-50 border border-gray-200 rounded-lg shadow-lg z-10 min-w-[300px]">
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="text-sm font-medium text-gray-900">Select Date Range</h3>
                    <button
                      onClick={() => setShowDatePicker(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        From Date
                      </label>
                      <input
                        type="date"
                        value={dateRange.startDate}
                        onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        To Date
                      </label>
                      <input
                        type="date"
                        value={dateRange.endDate}
                        onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                      />
                    </div>
                    
                    <div className="flex gap-2 pt-2">
                      <button
                        onClick={() => {
                          setDateRange({ startDate: "", endDate: "" });
                          setShowDatePicker(false);
                        }}
                        className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                      >
                        Clear
                      </button>
                      <button
                        onClick={() => setShowDatePicker(false)}
                        className="flex-1 px-3 py-2 text-sm bg-orange-500 text-white rounded-md hover:bg-orange-600"
                      >
                        Apply
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Search Input */}
            <div className="relative">
              <input
                type="text"
                placeholder="Search Orders"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full sm:w-64 pl-10 pr-3 py-2.5 border bg-gray-50 border-gray-300 rounded-lg  placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
              {/* Search button */}
              <button className="absolute inset-y-0 right-0 pr-3 flex items-center">
                <Search className="h-4 w-4 text-gray-400 hover:text-gray-600" />
              </button>
            </div>

            {/* Download Button */}
            <button
              onClick={handleDownload}
              disabled={!ordersData || ordersData.length === 0}
              className="flex items-center px-4 py-2.5 cursor-pointer bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              title={ordersData.length === 0 ? "No data to download" : "Download CSV"}
            >
              <Download className="h-4 w-4 mr-2" />
              Download CSV
            </button>

            {/* Clear Filters Button */}
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                <X className="h-4 w-4 mr-1" />
                Clear Filters
              </button>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className= "bg-orange-50 p-2 border border-gray-200 rounded-t-2xl">
<div className="flex flex-col sm:flex-row gap-2  ">
          <div
            onClick={() => navigate("/admin/orders/all")}
            className={`rounded-2xl font-medium text-lg text-gray-800 py-2 px-4 cursor-pointer transition-colors ${
              location.pathname.includes("all") || location.pathname.endsWith("/admin/orders")
                ? "bg-orange-500 text-white"
                : "bg-orange-50 text-black hover:bg-orange-100"
            }`}
          >
            All Orders
          </div>
          <div
            onClick={() => navigate("/admin/orders/completed")}
            className={`rounded-2xl font-medium text-lg text-gray-800 py-2 px-4 cursor-pointer transition-colors ${
              location.pathname.includes("completed")
                ? "bg-orange-500 text-white"
                : "bg-orange-50 text-black hover:bg-orange-100"
            }`}
          >
            Completed Orders
          </div>
        </div>
        </div>
        

        {/* Outlet for nested routes */}
        <Outlet />
      </div>
    </FilterContext.Provider>
  );
};

export default MainOrdersLayout;