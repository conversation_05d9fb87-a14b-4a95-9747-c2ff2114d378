import React, { useState, useMemo } from "react";
import CustomModal from "../../CustomModal";
import { IoSearchOutline } from "react-icons/io5";
import { FiEdit } from "react-icons/fi";
import { useGetCustomersQuery, type Customer, usePutCustomerMutation } from "../../../store/api/customerApi";
import PointsEditModal from "./PointsEditModal";
import Swal from "sweetalert2";

interface PointsAdjustmentProps {
  isOpen: boolean;
  onClose: () => void;
}

const PointsAdjustment: React.FC<PointsAdjustmentProps> = ({
  isOpen,
  onClose,
}) => {
  const { data: customers, isLoading, error, refetch } = useGetCustomersQuery();

  const [putCustomer] = usePutCustomerMutation();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const itemsPerPage = 5; // Changed to 5 items per page as requested

  // Enhanced filter customers with improved name search
  const filteredCustomers = useMemo(() => {
    if (!customers) return [];

    return customers.filter((customer) => {
      // Only include customers who have a card number
      if (!customer.CustomerLoyalty?.ActivateCard) return false;

      // If no search term, return all customers with cards
      if (!searchTerm.trim()) return true;

      const firstName = customer.FirstName?.toLowerCase() || "";
      const lastName = customer.LastName?.toLowerCase() || "";
      const fullName = `${firstName} ${lastName}`.trim();
      const cardNo = customer.CustomerLoyalty.CardNo?.toLowerCase() || "";
      const searchTermLower = searchTerm.toLowerCase().trim();

      // Enhanced search logic
      const matchesSearch = 
        // Search in first name
        firstName.includes(searchTermLower) ||
        // Search in last name  
        lastName.includes(searchTermLower) ||
        // Search in full name
        fullName.includes(searchTermLower) ||
        // Search in card number
        cardNo.includes(searchTermLower) ||
        // Split search term and check if each word matches first or last name
        searchTermLower.split(/\s+/).every(word => 
          firstName.includes(word) || 
          lastName.includes(word) ||
          cardNo.includes(word)
        ) ||
        // Check if search matches "LastName, FirstName" format
        `${lastName}, ${firstName}`.includes(searchTermLower) ||
        // Check if search matches "FirstName LastName" in any order
        (searchTermLower.split(/\s+/).length === 2 && (
          (firstName.includes(searchTermLower.split(/\s+/)[0]) && lastName.includes(searchTermLower.split(/\s+/)[1])) ||
          (firstName.includes(searchTermLower.split(/\s+/)[1]) && lastName.includes(searchTermLower.split(/\s+/)[0]))
        ));

      return matchesSearch;
    });
  }, [customers, searchTerm]);

  // Paginate customers
  const paginatedCustomers = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredCustomers.slice(startIndex, endIndex);
  }, [filteredCustomers, currentPage, itemsPerPage]);

  // Calculate total pages
  const totalPages = Math.ceil((filteredCustomers?.length || 0) / itemsPerPage);

  // Reset to first page when search term changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Loading and error states
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center py-8">
          <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
          <div className="ml-3 text-orange-500">
            Loading
          </div>
        </div>
      );
    }

    if (error) {
      return <div className="text-center py-4 text-red-500">Error loading customers</div>;
    }

    if (!customers || customers.length === 0) {
      return <div className="text-center py-4">No customers found</div>;
    }

    // Show different message for no search results vs no customers with cards
    if (filteredCustomers.length === 0) {
      const customersWithCards = customers.filter(c => c.CustomerLoyalty?.ActivateCard);
      
      if (customersWithCards.length === 0) {
        return <div className="text-center py-4">No customers with loyalty cards found</div>;
      } else if (searchTerm.trim()) {
        return (
          <div className="text-center py-4 text-gray-500">
            <div className="mb-2">No customers found matching "{searchTerm}"</div>
            <button
              onClick={() => setSearchTerm("")}
              className="text-blue-500 hover:text-blue-700 underline text-sm"
            >
              Clear search
            </button>
          </div>
        );
      }
    }

    return (
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="text-left border-b border-gray-200">
              <th className="pb-3 text-gray-500 font-normal">Card</th>
              <th className="pb-3 text-gray-500 font-normal">Person</th>
              <th className="pb-3 text-gray-500 font-normal">Points</th>
              <th className="pb-3 text-gray-500 font-normal">Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedCustomers.map((customer) => (
              <tr key={customer._id} className="border-b border-gray-200">
                <td className="py-4">{customer.CustomerLoyalty?.CardNo || "N/A"}</td>
                <td className="py-4">{`${customer?.FirstName || ""} ${customer?.LastName || ""}`.trim()}</td>
                <td className="py-4">{customer.CustomerLoyalty?.Points || 0}</td>
                <td className="py-4">
                  <button
                    className="flex items-center cursor-pointer gap-2 text-orange-500 border border-orange-500 rounded-full px-6 py-1 hover:bg-orange-50"
                    onClick={() => {
                      setSelectedCustomer(customer);
                      setIsEditModalOpen(true);
                    }}
                  >
                    <FiEdit />
                    Edit
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border border-gray-300 rounded-2xl px-2">
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage === 1 ? 'text-gray-400 cursor-not-allowed' : 'text-[#9C9C9C] cursor-pointer hover:text-orange-500'}`}
          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage === totalPages || totalPages === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-[#9C9C9C] cursor-pointer hover:text-orange-500'}`}
          onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          Next →
        </button>
      </div>
      <div className="text-sm text-gray-500">
        Page {currentPage} of {totalPages || 1} ({filteredCustomers.length} results)
      </div>
      <button
        onClick={onClose}
        className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors hover:bg-orange-50"
      >
        Cancel
      </button>
    </div>
  );

  // Handle updating customer points
  const handleUpdatePoints = async (customerId: string, newPoints: number) => {
    try {
      // Find the customer to update
      const customerToUpdate = customers?.find(customer => customer._id === customerId);

      if (!customerToUpdate) {
        console.error("Customer not found");
        return;
      }

      // Create updated customer object
      const updatedCustomer = {
        ...customerToUpdate,
        CustomerLoyalty: {
          ...customerToUpdate.CustomerLoyalty,
          Points: newPoints
        }
      };

      // Call the API to update the customer
      await putCustomer(updatedCustomer).unwrap();

      // Refetch the customers data to update the UI
      await refetch();

      // Show success message
      Swal.fire({
        icon: 'success',
        title: 'Points Updated',
        text: `Points for ${customerToUpdate.FirstName} ${customerToUpdate.LastName} updated successfully!`,
        timer: 2000,
        showConfirmButton: false,
        position: 'top-end',
        toast: true
      });

      // Close the edit modal
      setIsEditModalOpen(false);
      setSelectedCustomer(null);
    } catch (error) {
      console.error("Error updating customer points:", error);

      // Show error message
      Swal.fire({
        icon: 'error',
        title: 'Update Failed',
        text: 'Failed to update customer points. Please try again.',
        timer: 3000,
        showConfirmButton: false,
        position: 'top-end',
        toast: true
      });
    }
  };

  return (
    <>
      <CustomModal
        isOpen={isOpen}
        onClose={onClose}
        title="Points Adjustment"
        width="max-w-4xl"
        footer={footer}
      >
        <div className="p-6">
          {/* Search Bar */}
          <div className="relative mb-6">
            <IoSearchOutline
              size={25}
              className="absolute font-bold left-3 top-2/5 transform -translate-y-1/2 text-black"
            />
            <input
              type="text"
              placeholder="Search by Name or Card Number"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full pb-5 border-b border-[#E4E4E4] rounded-md focus:outline-none"
            />
            {searchTerm.trim() && (
              <button
                onClick={() => setSearchTerm("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            )}
          </div>

          {/* Search Results Info */}
          {searchTerm.trim() && (
            <div className="mb-4 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
              <span>Search results for: </span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                "{searchTerm}"
              </span>
              <span className="ml-2 font-medium">
                ({filteredCustomers.length} customers found)
              </span>
            </div>
          )}

          {/* Table */}
          {renderContent()}
        </div>
      </CustomModal>

      {/* Points Edit Modal */}
      <PointsEditModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedCustomer(null);
        }}
        customer={selectedCustomer}
        onUpdatePoints={handleUpdatePoints}
      />
    </>
  );
};

export default PointsAdjustment;