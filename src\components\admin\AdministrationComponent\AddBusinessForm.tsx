import { useState, useRef, useEffect } from 'react';
import { Clock } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  usePostAdministrationMutation,
  usePutAdministrationMutation,
  useGetAdministrationQuery
} from '../../../store/api/administrationApi';
import Swal from 'sweetalert2';

export default function AddBusinessForm() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const userId = localStorage.getItem('userId') || '';

  const [postAdministration, { isLoading: isCreating }] = usePostAdministrationMutation();
  const [putAdministration, { isLoading: isUpdating }] = usePutAdministrationMutation();

  const { data: administrationData, isLoading: isLoadingAdministration } = useGetAdministrationQuery(id || '', {
    skip: !isEditMode,
  });

  const [formData, setFormData] = useState<any>({
    name: '',
    line1: '',
    line2: '',
    city: '',
    state: '',
    postalCode: '',
    phoneNumber: '',
    country: '',
    businessType: 'Foods',
    pickupStart: '',
    pickupEnd: '',
    isActive: true,
    deliveryActive: true,
    deliveryStart: '',
    deliveryEnd: '',
    freeUptoKm: '',
    chargesPerKm: '',
    logo: null
  });

  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  useEffect(() => {
    if (isEditMode && administrationData) {
      setFormData({
        name: administrationData.name || '',
        line1: administrationData.Line1 || '',
        line2: administrationData.Line2 || '',
        city: administrationData.City || '',
        state: administrationData.State || '',
        postalCode: administrationData.PostalCode || '',
        phoneNumber: administrationData.Phoneno || '',
        country: administrationData.Country || '',
        businessType: administrationData.businessType || 'Foods',
        pickupStart: administrationData.pickupStartTime || '',
        pickupEnd: administrationData.pickupEndTime || '',
        isActive: administrationData.active !== undefined ? administrationData.active : true,
        deliveryActive: administrationData.delivery !== undefined ? administrationData.delivery : true,
        deliveryStart: administrationData.deliveryStartTime || '',
        deliveryEnd: administrationData.deliveryEndTime || '',
        freeUptoKm: administrationData.ChargesFreeKm?.toString() || '',
        chargesPerKm: administrationData.ChargesperKm?.toString() || '',
        logo: null
      });

      if (administrationData.image) {
        setPreviewUrl(administrationData.image);
      }
    }
  }, [administrationData, isEditMode]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    setFormData((prevData: any) => ({
      ...prevData,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleLogoClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    const file = files[0];
    if (file) {
      const validTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/gif'];
      if (!validTypes.includes(file.type)) {
        Swal.fire({
          title: 'Invalid File Type',
          text: 'Please upload SVG, PNG, JPG, or GIF files only',
          icon: 'error',
          confirmButtonColor: '#ea580c'
        });
        return;
      }

      // Check file size (800x800 max)
      const img = new Image();
      img.onload = () => {
        URL.revokeObjectURL(img.src);
        if (img.width > 800 || img.height > 800) {
          Swal.fire({
            title: 'Image Too Large',
            text: 'Image dimensions should not exceed 800x800 pixels',
            icon: 'error',
            confirmButtonColor: '#ea580c'
          });
          return;
        }

        // Valid file, update state
        setFormData((prev: any) => ({
          ...prev,
          logo: file
        }));
      };

      // Create preview URL for the image
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      img.src = objectUrl;
    }
  };

  const handleSubmit = async () => {
    try {
      // Check for required fields
      const requiredFields = ['name', 'line1', 'line2', 'city', 'state', 'postalCode', 'phoneNumber', 'country'];
      const missingField = requiredFields.find(field => !formData[field]);

      if (missingField) {
        Swal.fire({
          title: 'Required Field Missing',
          text: `Please fill in the ${missingField.replace(/([A-Z])/g, ' $1').toLowerCase()} field`,
          icon: 'warning',
          confirmButtonColor: '#ea580c'
        });
        return;
      }

      // Create FormData object for sending to the API
      const submitData = new FormData();

      // Map form fields to API expected fields
      submitData.append('name', formData.name);
      submitData.append('Line1', formData.line1);
      submitData.append('Line2', formData.line2);
      submitData.append('City', formData.city);
      submitData.append('State', formData.state);
      submitData.append('PostalCode', formData.postalCode);
      submitData.append('Phoneno', formData.phoneNumber);
      submitData.append('Country', formData.country);
      submitData.append('businessType', formData.businessType);
      submitData.append('active', formData.isActive);
      submitData.append('delivery', formData.deliveryActive);
      submitData.append('pickupStartTime', formData.pickupStart);
      submitData.append('pickupEndTime', formData.pickupEnd);
      submitData.append('deliveryStartTime', formData.deliveryStart);
      submitData.append('deliveryEndTime', formData.deliveryEnd);
      submitData.append('ChargesFreeKm', formData.freeUptoKm);
      submitData.append('ChargesperKm', formData.chargesPerKm);
      submitData.append('userId', userId);

      if (formData.logo) {
        submitData.append('image', formData.logo);
      }

      console.log(`${isEditMode ? 'Updating' : 'Creating'} business...`);

      // Show loading state
      Swal.fire({
        title: isEditMode ? 'Updating Business' : 'Creating Business',
        text: 'Please wait...',
        allowOutsideClick: false,
        allowEscapeKey: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });

      if (isEditMode && id) {
        await putAdministration({ id, data: submitData }).unwrap();
        
        Swal.fire({
          title: 'Success!',
          text: 'Business updated successfully!',
          icon: 'success',
          confirmButtonColor: '#ea580c'
        }).then(() => {
          navigate('/admin/administration/business-name');
        });
      } else {
        await postAdministration(submitData).unwrap();
        
        Swal.fire({
          title: 'Success!',
          text: 'Business added successfully!',
          icon: 'success',
          confirmButtonColor: '#ea580c'
        }).then(() => {
          navigate('/admin/administration/business-name');
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      
      Swal.fire({
        title: 'Error!',
        text: `Failed to ${isEditMode ? 'update' : 'add'} business. Please try again.`,
        icon: 'error',
        confirmButtonColor: '#ea580c'
      });
    }
  };

  const handleCancel = () => {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Any unsaved changes will be lost',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes, leave page',
      cancelButtonText: 'No, stay here',
      confirmButtonColor: '#ea580c',
      cancelButtonColor: '#d1d5db'
    }).then((result) => {
      if (result.isConfirmed) {
        navigate('/admin/administration/business-name');
      }
    });
  };

  if (isEditMode && isLoadingAdministration) {
    return (
      <div className="bg-gray-50 p-4 shadow min-h-screen flex justify-center items-center">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
          <span>Loading business data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full p-2 bg-white rounded-lg">
      <div className="space-y-6">
        {/* Business Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="name"
            id="name"
            value={formData.name}
            onChange={handleChange}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
          />
        </div>

        {/* Address Details Section */}
        <div className="p-4 rounded-md">
          <h2 className="text-lg font-medium mb-4">Address Details</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="line1" className="block text-sm font-medium text-gray-700">
                Line 1 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="line1"
                id="line1"
                value={formData.line1}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>

            <div>
              <label htmlFor="line2" className="block text-sm font-medium text-gray-700">
                Line 2 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="line2"
                id="line2"
                value={formData.line2}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>

            <div>
              <label htmlFor="city" className="block text-sm font-medium text-gray-700">
                City <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="city"
                id="city"
                value={formData.city}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>

            <div>
              <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                Phone Number <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="phoneNumber"
                id="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>

            <div>
              <label htmlFor="state" className="block text-sm font-medium text-gray-700">
                State <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="state"
                id="state"
                value={formData.state}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>

            <div>
              <label htmlFor="postalCode" className="block text-sm font-medium text-gray-700">
                Postal Code <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="postalCode"
                id="postalCode"
                value={formData.postalCode}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>

            <div>
              <label htmlFor="country" className="block text-sm font-medium text-gray-700">
                Country <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="country"
                id="country"
                value={formData.country}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>

            <div>
              <label htmlFor="businessType" className="block text-sm font-medium text-gray-700">
                Select Business Type <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <select
                  name="businessType"
                  id="businessType"
                  value={formData.businessType}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 appearance-none"
                >
                  <option value="Foods">Foods</option>
                  <option value="Retail">Retail</option>
                  <option value="Services">Services</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Pickup Section */}
        <div className="p-4 rounded-md">
          <h2 className="text-lg font-medium mb-4">Pickup</h2>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <label htmlFor="pickupStart" className="block text-sm font-medium text-gray-700 mb-1">
                Start Time
              </label>
              <div className="relative">
                <input
                  type="time"
                  name="pickupStart"
                  id="pickupStart"
                  value={formData.pickupStart}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded-md shadow-sm p-2 pl-10"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Clock size={18} className="text-gray-500" />
                </div>
              </div>
            </div>

            <div className="flex-1 relative">
              <label htmlFor="pickupEnd" className="block text-sm font-medium text-gray-700 mb-1">
                End Time
              </label>
              <div className="relative">
                <input
                  type="time"
                  name="pickupEnd"
                  id="pickupEnd"
                  value={formData.pickupEnd}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded-md shadow-sm p-2 pl-10"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Clock size={18} className="text-gray-500" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Permission Toggle */}
        <div className="bg-rose-50 p-4 rounded-md">
          <h2 className="text-lg font-medium mb-4">Permission</h2>
          <label className="inline-flex items-center cursor-pointer">
            <div className={`relative w-12 h-6 rounded-full transition-colors duration-200 ease-in-out ${formData.isActive ? 'bg-orange-500' : 'bg-gray-300'}`}>
              <input
                type="checkbox"
                name="isActive"
                checked={formData.isActive}
                onChange={handleChange}
                className="opacity-0 w-0 h-0"
              />
              <span className={`absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${formData.isActive ? 'translate-x-6' : 'translate-x-0'}`}></span>
            </div>
            <span className="ml-3 text-sm font-medium text-gray-700">
              {formData.isActive ? 'Active' : 'Inactive'}
            </span>
          </label>
        </div>

        {/* Delivery Section */}
        <div className="p-4 rounded-md">
          <h2 className="text-lg font-medium mb-4">Delivery</h2>

          {/* Delivery Toggle */}
          <label className="inline-flex items-center cursor-pointer mb-4">
            <div className={`relative w-12 h-6 rounded-full transition-colors duration-200 ease-in-out ${formData.deliveryActive ? 'bg-orange-500' : 'bg-gray-300'}`}>
              <input
                type="checkbox"
                name="deliveryActive"
                checked={formData.deliveryActive}
                onChange={handleChange}
                className="opacity-0 w-0 h-0"
              />
              <span className={`absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${formData.deliveryActive ? 'translate-x-6' : 'translate-x-0'}`}></span>
            </div>
            <span className="ml-3 text-sm font-medium text-gray-700">
              {formData.deliveryActive ? 'Delivery' : 'No Delivery'}
            </span>
          </label>

          {/* Delivery Times */}
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <label htmlFor="deliveryStart" className="block text-sm font-medium text-gray-700 mb-1">
                Start Time
              </label>
              <div className="relative">
                <input
                  type="time"
                  name="deliveryStart"
                  id="deliveryStart"
                  value={formData.deliveryStart}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded-md shadow-sm p-2 pl-10"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Clock size={18} className="text-gray-500" />
                </div>
              </div>
            </div>

            <div className="flex-1 relative">
              <label htmlFor="deliveryEnd" className="block text-sm font-medium text-gray-700 mb-1">
                End Time
              </label>
              <div className="relative">
                <input
                  type="time"
                  name="deliveryEnd"
                  id="deliveryEnd"
                  value={formData.deliveryEnd}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded-md shadow-sm p-2 pl-10"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Clock size={18} className="text-gray-500" />
                </div>
              </div>
            </div>
          </div>

          {/* Delivery Charges */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="freeUptoKm" className="block text-sm font-medium text-gray-700">
                Free Upto /Km <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="freeUptoKm"
                id="freeUptoKm"
                value={formData.freeUptoKm}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>

            <div>
              <label htmlFor="chargesPerKm" className="block text-sm font-medium text-gray-700">
                Charges Per /Km <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="chargesPerKm"
                id="chargesPerKm"
                value={formData.chargesPerKm}
                onChange={handleChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>
          </div>
        </div>

        {/* Business Logo */}
        <div className="bg-white p-4 rounded-md">
          <h2 className="text-lg font-medium mb-4">Business Logo</h2>
          <div
            className="flex items-center cursor-pointer"
            onClick={handleLogoClick}
          >
            <div className="w-20 h-20 bg-gray-200 border border-gray-300 rounded-md flex items-center justify-center overflow-hidden mr-4">
              {previewUrl ? (
                <img
                  src={previewUrl}
                  alt="Business logo"
                  className="w-full h-full object-cover"
                />
              ) : (
                <img
                  src="/api/placeholder/80/80"
                  alt="No image available"
                  className="w-full h-full object-cover"
                />
              )}
            </div>
            <div>
              <div className="text-blue-500 font-medium">Upload Business Logo</div>
              <div className="text-gray-500 text-xs mt-1">SVG, PNG, JPG, or GIF</div>
              <div className="text-gray-500 text-xs">(max. 800 x 800px)</div>
            </div>

            {/* Hidden file input */}
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleLogoChange}
              accept="image/svg+xml,image/png,image/jpeg,image/gif"
              className="hidden"
            />
          </div>

          {/* Show file name if uploaded */}
          {formData.logo && (
            <div className="mt-2 text-sm text-gray-600">
              Selected file: {formData.logo.name}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4 mt-8">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            disabled={isCreating || isUpdating}
            className={`px-6 py-2 ${(isCreating || isUpdating) ? 'bg-orange-300' : 'bg-orange-500 hover:bg-orange-600'} text-white rounded-md`}
          >
            {isEditMode ?
              (isUpdating ? 'Updating...' : 'Update Business') :
              (isCreating ? 'Adding...' : 'Add Business Details')
            }
          </button>
        </div>
      </div>
    </div>
  );
}