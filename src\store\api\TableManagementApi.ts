import { baseApi } from "./baseApi";

export interface TableManagement {
  _id?: string;
  tableNo: string;
  tableimg?: string;
  tableName: string;
  location: string | object;
  description?: string;
  hasLampixDevice?: boolean;
  userId: string;
  floor: string;
  Status?: string;
  height?: number;
  width?: number;
  x?: number;
  y?: number;
  capacity?: number;
}

export const tableManagementApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getTables: builder.query<TableManagement[], string>({
      query: (userId) => `/Tables?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          _id: item._id,
          tableNo: item.tableNo,
          floor: item.floor,
          tableimg: item.tableimg,
          tableName: item.tableName,
          location: item.location,
          description: item.description,
          hasLampixDevice: item.hasLampixDevice,
          userId: item.userId,
          Status: item.Status,
          height: item.height,
          width: item.width,
          x: item.x,
          y: item.y,
          capacity: item.capacity,
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ _id }) => ({ type: "TableManagement" as const, id: _id })),
              { type: "TableManagement", id: "LIST" },
            ]
          : [{ type: "TableManagement", id: "LIST" }],
    }),

    getTable: builder.query<TableManagement, string>({
      query: (id) => `/Tables/${id}`,
      transformResponse: (response: any) => ({
        _id: response._id,
        tableNo: response.tableNo,
        floor: response.floor,
        tableimg: response.tableimg,
        tableName: response.tableName,
        location: response.location,
        description: response.description,
        hasLampixDevice: response.hasLampixDevice,
        userId: response.userId,
        Status: response.Status,
        height: response.height,
        width: response.width,
        x: response.x,
        y: response.y,
        capacity: response.capacity,
      }),
      providesTags: (_result, _error, id) => [{ type: "TableManagement", id }],
    }),

    deleteTable: builder.mutation<void, string>({
      query: (id) => ({
        url: `/Tables/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            tableManagementApi.util.invalidateTags([
              { type: "TableManagement", id },
            ])
          );
        } catch (error) {
          console.error("Failed to delete table", error);
        }
      },
      invalidatesTags: [{ type: "TableManagement", id: "LIST" }],
    }),

    postTable: builder.mutation<any, FormData>({
      query: (data) => {
        return {
          url: "/Tables",
          method: "POST",
          body: data, // Using FormData for file upload
        };
      },
      invalidatesTags: [{ type: "TableManagement", id: "LIST" }],
    }),

    putTable: builder.mutation<
      any,
      { id: string; data: FormData }
    >({
      query: ({ id, data }) => ({
        url: `/Tables/${id}`,
        method: "PUT",
        body: data, // Using FormData for file upload
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "TableManagement", id },
        { type: "TableManagement", id: "LIST" },
      ],
    }),

    searchUpdateTable: builder.mutation<
      any,
      { queryParams: { tableNo?: string; tableName?: string }; data: FormData }
    >({
      query: ({ queryParams, data }) => {
        const params = new URLSearchParams();
        if (queryParams.tableNo) params.append('tableNo', queryParams.tableNo);
        if (queryParams.tableName) params.append('tableName', queryParams.tableName);
        
        return {
          url: `/Tables?${params.toString()}`,
          method: "PUT",
          body: data,
        };
      },
      invalidatesTags: [{ type: "TableManagement", id: "LIST" }],
    }),

    updateTablePositions: builder.mutation<
      any,
      { 
        siteId: string; 
        tablePositions: Array<{ 
          tableNo: string; 
          x: number; 
          y: number; 
          floor: string;
          _id?: string;
        }> 
      }
    >({
      query: (data) => ({
        url: "/update_positions",
        method: "PUT",
        body: data,
      }),
      invalidatesTags: [{ type: "TableManagement", id: "LIST" }],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetTablesQuery,
  useGetTableQuery,
  useDeleteTableMutation,
  usePostTableMutation,
  usePutTableMutation,
  useSearchUpdateTableMutation,
  useUpdateTablePositionsMutation,
} = tableManagementApi;