import { useState, useEffect } from "react";
import { ChevronLeft, PlusSquare, Edit, Trash2, Calendar } from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import {
  useGetEmployeeListQuery,
  usePutEmployeeListMutation,
} from "../../../store/api/EmployeeListApi";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import Swal from "sweetalert2";

type BonusOrAllowance = {
  id: string;
  name: string;
  amount: string;
  date: string;
};

type FormDataType = {
  employeeName: string;
  hourlyRate: string;
  shiftHours: string;
  overtimeRate: string;
  arrivalTime: string;
  departureTime: string;
  employeeType: string;
  bonuses: BonusOrAllowance[];
  allowances: BonusOrAllowance[];
};

export default function SalaryStructureForm() {
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const navigate = useNavigate();
  const userId = localStorage.getItem("userId") || "";

  const { data: employeeData, isLoading: isLoadingEmployee } =
    useGetEmployeeListQuery(id || "", {
      skip: !isEditMode,
    });

  const [putEmployeeList, { isLoading: isUpdating }] =
    usePutEmployeeListMutation();

  const [showBonusForm, setShowBonusForm] = useState(false);
  const [showAllowanceForm, setShowAllowanceForm] = useState(false);
  const [currentDate] = useState(new Date().toISOString().split("T")[0]);

  // New state for form inputs in bonus and allowance forms
  const [newBonus, setNewBonus] = useState({
    name: "",
    amount: "",
    date: currentDate,
  });
  const [newAllowance, setNewAllowance] = useState({
    name: "",
    amount: "",
    date: currentDate,
  });

  const [formData, setFormData] = useState<FormDataType>({
    employeeName: "",
    hourlyRate: "",
    shiftHours: "",
    overtimeRate: "",
    arrivalTime: "",
    departureTime: "",
    employeeType: "",
    bonuses: [],
    allowances: [],
  });

  const [bonuses, setBonuses] = useState<BonusOrAllowance[]>([]);
  const [allowances, setAllowances] = useState<BonusOrAllowance[]>([]);

  useEffect(() => {
    if (isEditMode && employeeData) {
      const employeeBonuses =
        employeeData.deductions
          ?.filter((d) => d.type === "bonus")
          .map((bonus, index) => ({
            id: (index + 1).toString().padStart(2, "0"),
            name: bonus.name || "",
            amount: `$${bonus.amount?.toFixed(2) || "0.00"}`,
            date: bonus.date || new Date().toLocaleDateString(),
          })) || [];

      const employeeAllowances =
        employeeData.deductions
          ?.filter((d) => d.type === "allowance")
          .map((allowance, index) => ({
            id: (index + 1).toString().padStart(2, "0"),
            name: allowance.name || "",
            amount: `$${allowance.amount?.toFixed(2) || "0.00"}`,
            date: allowance.date || new Date().toLocaleDateString(),
          })) || [];

      setFormData({
        employeeName: `${employeeData.firstName} ${employeeData.lastName}`,
        hourlyRate: employeeData.hourlyRate?.toString() || "",
        shiftHours: employeeData.totalHours?.toString() || "",
        overtimeRate: employeeData.overTimeRate?.toString() || "",
        arrivalTime: employeeData.employeeStartTime || "",
        departureTime: employeeData.employeeEndTime || "",
        employeeType: employeeData.employeeType || "",
        bonuses: employeeBonuses,
        allowances: employeeAllowances,
      });

      setBonuses(employeeBonuses);
      setAllowances(employeeAllowances);
    }
  }, [employeeData, isEditMode]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleNewBonusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewBonus((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleNewAllowanceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewAllowance((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isEditMode || !id) {
      Swal.fire({
        icon: "error",
        title: "Missing Employee ID",
        text: "Cannot update: No employee ID provided",
        confirmButtonText: "OK",
      });
      return;
    }

    try {
      // Format bonuses and allowances for API
      const formattedBonuses = bonuses.map((bonus) => ({
        name: bonus.name,
        amount: parseFloat(bonus.amount.replace("$", "")),
        type: "bonus",
        date: bonus.date,
      }));

      const formattedAllowances = allowances.map((allowance) => ({
        name: allowance.name,
        amount: parseFloat(allowance.amount.replace("$", "")),
        type: "allowance",
        date: allowance.date,
      }));

      // Prepare update data
      const updateData = {
        hourlyRate: parseFloat(formData.hourlyRate) || 0,
        totalHours: parseFloat(formData.shiftHours) || 0,
        overTimeRate: parseFloat(formData.overtimeRate) || 0,
        employeeStartTime: formData.arrivalTime,
        employeeEndTime: formData.departureTime,
        employeeType: formData.employeeType,
        userId: userId,
        deductions: [...formattedBonuses, ...formattedAllowances],
      };

      // Call update API
      await putEmployeeList({ id, data: updateData }).unwrap();

      await Swal.fire({
        title: "Success!",
        text: "Salary structure updated successfully!",
        icon: "success",
        confirmButtonText: "OK",
      });
      navigate("/admin/employees/salary-structure");
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Update Failed",
        text: "Failed to update salary structure. Please try again.",
        confirmButtonText: "Try Again",
      });
    }
  };

  const handleAddBonus = () => {
    setShowBonusForm(true);
    setShowAllowanceForm(false);
  };

  const handleAddAllowance = () => {
    setShowAllowanceForm(true);
    setShowBonusForm(false);
  };

  const handleCancel = () => {
    setShowBonusForm(false);
    setShowAllowanceForm(false);
    // Reset form inputs
    setNewBonus({ name: "", amount: "", date: currentDate });
    setNewAllowance({ name: "", amount: "", date: currentDate });
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleSubmitBonus = () => {
    if (!newBonus.name || !newBonus.amount) {
      Swal.fire({
        icon: "warning",
        title: "Required Fields Missing",
        text: "Please fill in all required fields",
        confirmButtonText: "OK",
      });
      return;
    }

    // Format and add bonus
    const newBonusItem: BonusOrAllowance = {
      id: (bonuses.length + 1).toString().padStart(2, "0"),
      name: newBonus.name,
      amount: newBonus.amount.startsWith("$")
        ? newBonus.amount
        : `$${newBonus.amount}`,
      date: newBonus.date,
    };

    setBonuses([...bonuses, newBonusItem]);
    setShowBonusForm(false);
    setNewBonus({ name: "", amount: "", date: currentDate });
  };

  const handleSubmitAllowance = () => {
    if (!newAllowance.name || !newAllowance.amount) {
      Swal.fire({
        icon: "warning",
        title: "Required Fields Missing",
        text: "Please fill in all required fields",
        confirmButtonText: "OK",
      });
      return;
    }

    // Format and add allowance
    const newAllowanceItem: BonusOrAllowance = {
      id: (allowances.length + 1).toString().padStart(2, "0"),
      name: newAllowance.name,
      amount: newAllowance.amount.startsWith("$")
        ? newAllowance.amount
        : `$${newAllowance.amount}`,
      date: newAllowance.date,
    };

    setAllowances([...allowances, newAllowanceItem]);
    setShowAllowanceForm(false);
    setNewAllowance({ name: "", amount: "", date: currentDate });
  };

  const handleEditBonus = (index: number) => {
    const bonusToEdit = bonuses[index];
    setNewBonus({
      name: bonusToEdit.name,
      amount: bonusToEdit.amount.replace("$", ""),
      date: bonusToEdit.date,
    });
    // Remove the bonus from the list
    const updatedBonuses = [...bonuses];
    updatedBonuses.splice(index, 1);
    setBonuses(updatedBonuses);
    setShowBonusForm(true);
  };

  const handleEditAllowance = (index: number) => {
    const allowanceToEdit = allowances[index];
    setNewAllowance({
      name: allowanceToEdit.name,
      amount: allowanceToEdit.amount.replace("$", ""),
      date: allowanceToEdit.date,
    });
    // Remove the allowance from the list
    const updatedAllowances = [...allowances];
    updatedAllowances.splice(index, 1);
    setAllowances(updatedAllowances);
    setShowAllowanceForm(true);
  };

  const handleDeleteBonus = (index: number) => {
    const updatedBonuses = [...bonuses];
    updatedBonuses.splice(index, 1);
    setBonuses(updatedBonuses);
  };

  const handleDeleteAllowance = (index: number) => {
    const updatedAllowances = [...allowances];
    updatedAllowances.splice(index, 1);
    setAllowances(updatedAllowances);
  };

  if (isEditMode && isLoadingEmployee) {
    return (
      <div className="flex justify-center items-start h-screen pt-[40vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading Employee data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full p-4 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center md:flex-row flex-col p-4 border border-gray-200 rounded-2xl bg-white">
        <div className="flex items-center gap-2 md:mb-0 mb-3">
          <ChevronLeft
            size={20}
            onClick={handleBack}
            className="text-gray-500 cursor-pointer"
          />
          <h1 className="font-bold text-2xl">
            {isEditMode ? "Edit Salary Structure" : "Add Salary Structure"}
          </h1>
        </div>
        <div className="flex items-center md:flex-row flex-col gap-3">
          <button
            className="bg-orange-500 text-white py-2 px-4 rounded-lg flex items-center"
            onClick={handleSubmit}
            disabled={isUpdating}
          >
            {isUpdating
              ? "Updating..."
              : isEditMode
              ? "Update Salary"
              : "Add Salary"}
            <PlusSquare size={16} className="ml-1" />
          </button>
        </div>
      </div>

      {/* Employee Details */}
      <div className="bg-orange-50 font-medium mt-4 border border-gray-200 rounded-2xl overflow-x-auto">
        <h1 className="p-3">Employee Details</h1>
        <div className="bg-white rounded-2xl">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Employee Name
                </th>
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Rate / Hourly
                </th>
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Shift Hours
                </th>
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Overtime Rate / Hourly
                </th>
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Bonuses
                </th>
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Allowances
                </th>
              </tr>
            </thead>
            <tbody>
              <tr className="hover:bg-orange-50">
                <td className="p-3 text-sm ">
                  {formData.employeeName || "Select Employee"}
                </td>
                <td className="p-3 text-sm">
                  ${formData.hourlyRate || "0.00"}
                </td>
                <td className="p-3 text-sm">{formData.shiftHours || "0"}</td>
                <td className="p-3 text-sm">
                  ${formData.overtimeRate || "0.00"}
                </td>
                <td className="p-3 text-sm">{bonuses.length || 0} items</td>
                <td className="p-3 text-sm">{allowances.length || 0} items</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Roles and Salary */}
      <div className="border border-gray-200 rounded-2xl bg-white mt-4">
        <div className="bg-orange-50 font-medium text-orange-900 p-3">
          Roles and Salary
        </div>
        <div className="bg-white p-4 grid md:grid-cols-3 grid-col-2 gap-4 rounded-2xl">
          <div>
            <label className="block text-sm font-medium mb-1">
              Employee Type <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <select
                className="w-full border border-gray-200 rounded p-2 appearance-none"
                name="employeeType"
                value={formData.employeeType}
                onChange={handleInputChange}
              >
                <option value="">Select</option>
                <option value="waiter">Waiter</option>
                <option value="cashier">Cashier</option>
                <option value="manager">Manager</option>
                <option value="cook">Cook</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <ChevronLeft
                  size={16}
                  className="transform rotate-270 text-gray-400"
                />
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              Hourly Rate <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              placeholder="Enter Hourly Rate"
              className="w-full border border-gray-200 p-2 rounded"
              name="hourlyRate"
              value={formData.hourlyRate}
              onChange={handleInputChange}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              Shift Hours <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              placeholder="Enter Shift Hours"
              className="w-full border border-gray-200 p-2 rounded"
              name="shiftHours"
              value={formData.shiftHours}
              onChange={handleInputChange}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              Arrival Time <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="time"
                placeholder="--:-- --"
                className="w-full border border-gray-200 p-2 rounded"
                name="arrivalTime"
                value={formData.arrivalTime}
                onChange={handleInputChange}
              />
              {/* <Calendar size={16} className="absolute right-3 top-3 text-gray-400" /> */}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              Departure Time <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="time"
                placeholder="--:-- --"
                className="w-full border border-gray-200 p-2 rounded"
                name="departureTime"
                value={formData.departureTime}
                onChange={handleInputChange}
              />
              {/* <Calendar size={16} className="absolute right-3 top-3 text-gray-400" /> */}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              Overtime Hourly Rate <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              placeholder="Enter Overtime Hourly Rate"
              className="w-full border border-gray-200 p-2 rounded"
              name="overtimeRate"
              value={formData.overtimeRate}
              onChange={handleInputChange}
            />
          </div>
        </div>
      </div>

      {/* Bonuses Section */}
      <div className="mb-6 mt-6 border border-gray-200 rounded-2xl">
        <div className="bg-orange-50 cursor-pointer p-3 font-medium text-orange-900 rounded-t-2xl">
          Bonuses
        </div>

        <div className="bg-white rounded-b-2xl overflow-x-auto">
          <table className="w-full border-collapse ">
            <thead>
              <tr className="border-b border-b-gray-200">
                <th className="p-3 text-left text-sm font-normal text-gray-500 w-20">
                  No
                </th>
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Name
                </th>
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Amount
                </th>
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Date
                </th>
                <th className="p-3 text-right text-sm font-normal text-gray-500"></th>
              </tr>
            </thead>
            <tbody>
              {bonuses.map((bonus, index) => (
                <tr
                  key={bonus.id}
                  className="border-b border-b-gray-200 hover:bg-orange-50"
                >
                  <td className="p-3 text-sm">{bonus.id}</td>
                  <td className="p-3 text-sm">{bonus.name}</td>
                  <td className="p-3 text-sm">{bonus.amount}</td>
                  <td className="p-3 text-sm">{bonus.date}</td>
                  <td className="p-3 flex justify-end space-x-2">
                    <button
                      className="text-blue-500"
                      onClick={() => handleEditBonus(index)}
                    >
                      <Edit
                        id="edit-icon"
                        data-tooltip-id="edit-tooltip"
                        data-tooltip-content="Edit"
                        size={20}
                        className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                      />
                      <Tooltip
                        id="edit-tooltip"
                        place="bottom"
                        className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                      />
                    </button>
                    <button
                      className="text-red-500"
                      onClick={() => handleDeleteBonus(index)}
                    >
                      <Trash2
                        id="delete-icon"
                        data-tooltip-id="delete-tooltip"
                        data-tooltip-content="Delete"
                        size={20}
                        className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                      />
                      <Tooltip
                        id="delete-tooltip"
                        place="bottom"
                        className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                      />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {showBonusForm && (
            <div className="p-3 border-b border-b-gray-200">
              <div className="flex gap-3 mb-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1 text-left">
                    Bonus Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="name"
                    placeholder="Enter Bonus Name"
                    className="w-full border p-2 rounded"
                    value={newBonus.name}
                    onChange={handleNewBonusChange}
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1 text-left">
                    Bonus Amount <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="amount"
                    placeholder="Enter Bonus Amount"
                    className="w-full border p-2 rounded"
                    value={newBonus.amount}
                    onChange={handleNewBonusChange}
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1 text-left">
                    Date <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="date"
                      name="date"
                      className="w-full border p-2 rounded"
                      value={newBonus.date}
                      onChange={handleNewBonusChange}
                    />
                    <Calendar
                      size={16}
                      className="absolute right-3 top-3 text-gray-400 pointer-events-none"
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  className="bg-orange-500 cursor-pointer text-white py-2 px-4 rounded-lg"
                  onClick={handleSubmitBonus}
                >
                  Add Bonus
                </button>
                <button
                  className="border cursor-pointer  border-orange-500 text-orange-500 py-2 px-4 rounded-lg"
                  onClick={handleCancel}
                >
                  Cancel
                </button>
              </div>
            </div>
          )}

          {!showBonusForm && (
            <div className="flex justify-end p-3">
              <button
                className="bg-orange-500 cursor-pointer text-white py-2 px-4 rounded-lg flex items-center"
                onClick={handleAddBonus}
              >
                Add Bonus <PlusSquare size={16} className="ml-1" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Allowances Section */}
      <div className="mb-6 border border-gray-200 rounded-2xl">
        <div className="bg-orange-50 cursor-pointer p-3 font-medium text-orange-900 rounded-t-2xl">
          Allowances
        </div>

        <div className="bg-white rounded-b-2xl overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b border-b-gray-200">
                <th className="p-3 text-left text-sm font-normal text-gray-500 w-20">
                  No
                </th>
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Name
                </th>
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Amount
                </th>
                <th className="p-3 text-left text-sm font-normal text-gray-500">
                  Date
                </th>
                <th className="p-3 text-right text-sm font-normal text-gray-500"></th>
              </tr>
            </thead>
            <tbody>
              {allowances.map((allowance, index) => (
                <tr
                  key={allowance.id}
                  className="border-b border-b-gray-200 hover:bg-orange-50"
                >
                  <td className="p-3 text-sm">{allowance.id}</td>
                  <td className="p-3 text-sm">{allowance.name}</td>
                  <td className="p-3 text-sm">{allowance.amount}</td>
                  <td className="p-3 text-sm">{allowance.date}</td>
                  <td className="p-3 flex justify-end space-x-2">
                    <button
                      className="text-blue-500"
                      onClick={() => handleEditAllowance(index)}
                    >
                      <Edit
                        id="edit-icon"
                        data-tooltip-id="edit-tooltip"
                        data-tooltip-content="Edit"
                        size={20}
                        className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                      />
                      <Tooltip
                        id="edit-tooltip"
                        place="bottom"
                        className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                      />
                    </button>
                    <button
                      className="text-red-500"
                      onClick={() => handleDeleteAllowance(index)}
                    >
                      <Trash2
                        id="delete-icon"
                        data-tooltip-id="delete-tooltip"
                        data-tooltip-content="Delete"
                        size={20}
                        className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                      />
                      <Tooltip
                        id="delete-tooltip"
                        place="bottom"
                        className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                      />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {showAllowanceForm && (
            <div className="p-3 border-b">
              <div className="flex gap-3 mb-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1 text-left">
                    Allowance Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="name"
                    placeholder="Enter Allowance Name"
                    className="w-full border p-2 rounded"
                    value={newAllowance.name}
                    onChange={handleNewAllowanceChange}
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1 text-left">
                    Allowance Amount <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="amount"
                    placeholder="Enter Allowance Amount"
                    className="w-full border p-2 rounded"
                    value={newAllowance.amount}
                    onChange={handleNewAllowanceChange}
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1 text-left">
                    Date <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="date"
                      name="date"
                      className="w-full border p-2 rounded"
                      value={newAllowance.date}
                      onChange={handleNewAllowanceChange}
                    />
                    <Calendar
                      size={16}
                      className="absolute right-3 top-3 text-gray-400 pointer-events-none"
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  className="bg-orange-500 text-white py-2 px-4 rounded-lg"
                  onClick={handleSubmitAllowance}
                >
                  Add Allowance
                </button>
                <button
                  className="border border-orange-500 text-orange-500 py-2 px-4 rounded-lg"
                  onClick={handleCancel}
                >
                  Cancel
                </button>
              </div>
            </div>
          )}

          {!showAllowanceForm && (
            <div className="flex justify-end p-3">
              <button
                className="bg-orange-500 cursor-pointer text-white py-2 px-4 rounded-lg flex items-center"
                onClick={handleAddAllowance}
              >
                Add Allowance <PlusSquare size={16} className="ml-1" />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
