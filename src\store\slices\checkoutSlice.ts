import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import { checkoutApi } from '../api/checkoutApi';

// Define the state structure based on your checkout API
interface CheckoutState {
  charges: any[];
  customers: any[];
  subscriptions: any[];
  products: any[];
  sellerAccounts: any[];
  totalInvoiceCount: number;
  totalApplicationFeeAmount: number;
  isLoading: boolean;
  error: string | null;
}

const initialState: CheckoutState = {
  charges: [],
  customers: [],
  subscriptions: [],
  products: [],
  sellerAccounts: [],
  totalInvoiceCount: 0,
  totalApplicationFeeAmount: 0,
  isLoading: false,
  error: null,
};

const checkoutSlice = createSlice({
  name: 'checkout',
  initialState,
  reducers: {
    // You can add direct actions if needed
    setCharges: (state, action: PayloadAction<any[]>) => {
      state.charges = action.payload;
    },
    setCustomers: (state, action: PayloadAction<any[]>) => {
      state.customers = action.payload;
    },
    setSubscriptions: (state, action: PayloadAction<any[]>) => {
      state.subscriptions = action.payload;
    },
    setProducts: (state, action: PayloadAction<any[]>) => {
      state.products = action.payload;
    },
    setSellerAccounts: (state, action: PayloadAction<any[]>) => {
      state.sellerAccounts = action.payload;
    },
    clearCheckoutData: (state) => {
      state.charges = [];
      state.customers = [];
      state.subscriptions = [];
      state.products = [];
      state.sellerAccounts = [];
      state.totalInvoiceCount = 0;
      state.totalApplicationFeeAmount = 0;
    },
  },
  extraReducers: (builder) => {
    builder
      // Charges queries
      .addMatcher(
        checkoutApi.endpoints.getCharges.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getCharges.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.charges = payload;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getCharges.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch charges';
        }
      )

      // Charges by account
      .addMatcher(
        checkoutApi.endpoints.getChargesByAccount.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getChargesByAccount.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.charges = payload.paymentIntents || [];
          state.totalApplicationFeeAmount = payload.totalApplicationFeeAmount || 0;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getChargesByAccount.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch charges by account';
        }
      )

      // Get all amounts
      .addMatcher(
        checkoutApi.endpoints.getAllAmounts.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getAllAmounts.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.charges = payload.charges || [];
          state.subscriptions = payload.subscriptions || [];
          state.totalInvoiceCount = payload.totalInvoiceCount || 0;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getAllAmounts.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch all amounts';
        }
      )

      // Customers queries
      .addMatcher(
        checkoutApi.endpoints.getCustomers.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getCustomers.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.customers = payload;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getCustomers.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch customers';
        }
      )

      // Subscriptions queries
      .addMatcher(
        checkoutApi.endpoints.getSubscriptions.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getSubscriptions.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.subscriptions = payload.subscriptions || [];
          state.totalInvoiceCount = payload.totalInvoiceCount || 0;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getSubscriptions.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch subscriptions';
        }
      )

      // Customer subscriptions
      .addMatcher(
        checkoutApi.endpoints.getCustomerSubscriptions.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getCustomerSubscriptions.matchFulfilled,
        (state, {  }) => {
          state.isLoading = false;
          // This doesn't modify the global state, but you could add logic here if needed
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getCustomerSubscriptions.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch customer subscriptions';
        }
      )

      // Cancel subscription
      .addMatcher(
        checkoutApi.endpoints.cancelSubscription.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.cancelSubscription.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const subscriptionId = action.meta.arg.originalArgs as string;
          state.subscriptions = state.subscriptions.filter(
            (subscription) => subscription.id !== subscriptionId
          );
        }
      )
      .addMatcher(
        checkoutApi.endpoints.cancelSubscription.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to cancel subscription';
        }
      )

      // Products queries
      .addMatcher(
        checkoutApi.endpoints.getAppProducts.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getAppProducts.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.products = payload;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getAppProducts.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch products';
        }
      )

      // Seller accounts queries
      .addMatcher(
        checkoutApi.endpoints.getCompletedAccounts.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getCompletedAccounts.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.sellerAccounts = payload;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.getCompletedAccounts.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch seller accounts';
        }
      )

      // Delete connected account
      .addMatcher(
        checkoutApi.endpoints.deleteConnectedAccount.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        checkoutApi.endpoints.deleteConnectedAccount.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const accountId = action.meta.arg.originalArgs as string;
          state.sellerAccounts = state.sellerAccounts.filter(
            (account) => account.id !== accountId
          );
        }
      )
      .addMatcher(
        checkoutApi.endpoints.deleteConnectedAccount.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete connected account';
        }
      );
  },
});

export const { 
  setCharges, 
  setCustomers, 
  setSubscriptions, 
  setProducts, 
  setSellerAccounts, 
  clearCheckoutData 
} = checkoutSlice.actions;

// Selectors
export const selectCharges = (state: RootState) => state.checkout.charges;
export const selectCustomers = (state: RootState) => state.checkout.customers;
export const selectSubscriptions = (state: RootState) => state.checkout.subscriptions;
export const selectProducts = (state: RootState) => state.checkout.products;
export const selectSellerAccounts = (state: RootState) => state.checkout.sellerAccounts;
export const selectTotalInvoiceCount = (state: RootState) => state.checkout.totalInvoiceCount;
export const selectTotalApplicationFeeAmount = (state: RootState) => state.checkout.totalApplicationFeeAmount;
export const selectCheckoutLoading = (state: RootState) => state.checkout.isLoading;
export const selectCheckoutError = (state: RootState) => state.checkout.error;

export default checkoutSlice.reducer;