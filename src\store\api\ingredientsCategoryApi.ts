import { baseApi } from "./baseApi";

export interface IngredientsCategory {
  id: string;
  name: string;
  subCategoriesCount: number;
}

export const ingredientsCategoryApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getIngredientsCategories: builder.query<IngredientsCategory[], string>({
      query: (userId) => `/Ingredientcategorys?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          name: item.name,
          subCategoriesCount: 0,
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "Category" as const, id })),
              { type: "Category", id: "LIST" },
            ]
          : [{ type: "Category", id: "LIST" }],
    }),

    deleteIngredientsCategory: builder.mutation<void, string>({
      query: (id) => ({
        url: `/Ingredientcategorys/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            ingredientsCategoryApi.util.invalidateTags([
              { type: "Category", id },
            ])
          );
        } catch (error) {
          console.error("Failed to delete category", error);
        }
      },
    }),

    postIngredientsCategory: builder.mutation<any, any>({
      query: (data) => {
        console.log("Sending POST request with data:", data);
        return {
          url: "/Ingredientcategorys",
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: [{ type: "Category", id: "LIST" }],
    }),

    putIngredientsCategory: builder.mutation<
      any,
      { id: string; formData: { _id: string; name: string; userId: string } }
    >({
      query: ({ id, formData }) => ({
        url: `/Ingredientcategorys/${id}`,
        method: "PUT",
        body: {
          ...formData,
        },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "Category", id },
        { type: "Category", id: "LIST" },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetIngredientsCategoriesQuery,
  useDeleteIngredientsCategoryMutation,
  usePostIngredientsCategoryMutation,
  usePutIngredientsCategoryMutation,
} = ingredientsCategoryApi;
