import { baseApi } from "./baseApi";

export interface SiteManagement {
  _id?: string;
  siteName: string;
  numberOfTables: number;
  briefDescription: string;
  isActive: boolean;
  siteImage?: string;
  userId: string;
}

export const siteManagementApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getSites: builder.query<SiteManagement[], string>({
      query: (userId) => `/site?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          _id: item._id,
          siteName: item.siteName,
          numberOfTables: item.numberOfTables,
          briefDescription: item.briefDescription,
          isActive: item.isActive,
          siteImage: item.siteImage,
          userId: item.userId,
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ _id }) => ({ type: "SiteManagement" as const, id: _id })),
              { type: "SiteManagement", id: "LIST" },
            ]
          : [{ type: "SiteManagement", id: "LIST" }],
    }),

    getSite: builder.query<SiteManagement, string>({
      query: (id) => `/site/${id}`,
      transformResponse: (item: any) => ({
        _id: item._id,
        siteName: item.siteName,
        numberOfTables: item.numberOfTables,
        briefDescription: item.briefDescription,
        isActive: item.isActive,
        siteImage: item.siteImage,
        userId: item.userId,
      }),
      providesTags: (_result, _error, id) => [{ type: "SiteManagement", id }],
    }),

    deleteSite: builder.mutation<void, string>({
      query: (id) => ({
        url: `/site/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            siteManagementApi.util.invalidateTags([
              { type: "SiteManagement", id },
            ])
          );
        } catch (error) {
          console.error("Failed to delete site", error);
        }
      },
      invalidatesTags: [{ type: "SiteManagement", id: "LIST" }],
    }),

    postSite: builder.mutation<any, FormData>({
      query: (data) => {
        return {
          url: "/site",
          method: "POST",
          body: data, // Using FormData for file upload
        };
      },
      invalidatesTags: [{ type: "SiteManagement", id: "LIST" }],
    }),

    putSite: builder.mutation<
      any,
      { id: string; data: FormData }
    >({
      query: ({ id, data }) => ({
        url: `/site/${id}`,
        method: "PUT",
        body: data, // Using FormData for file upload
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "SiteManagement", id },
        { type: "SiteManagement", id: "LIST" },
      ],
    }),

    // This endpoint isn't in route
    
    searchUpdateSite: builder.mutation<
      any,
      { queryParams: { siteName?: string; siteImage?: string }; data: FormData }
    >({
      query: ({ queryParams, data }) => {
        const params = new URLSearchParams();
        if (queryParams.siteName) params.append('siteName', queryParams.siteName);
        if (queryParams.siteImage) params.append('siteImage', queryParams.siteImage);
        
        return {
          url: `/site?${params.toString()}`,
          method: "PUT",
          body: data,
        };
      },
      invalidatesTags: [{ type: "SiteManagement", id: "LIST" }],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetSitesQuery,
  useGetSiteQuery,
  useDeleteSiteMutation,
  usePostSiteMutation,
  usePutSiteMutation,
  useSearchUpdateSiteMutation,
} = siteManagementApi;