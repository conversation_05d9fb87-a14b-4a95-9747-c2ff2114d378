import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { PaymentTypes } from '../api/paymentTypesApi';
import { paymentTypesApi } from '../api/paymentTypesApi';

interface PaymentTypesState {
  paymentTypes: PaymentTypes[];
  isLoading: boolean;
  error: string | null;
}

const initialState: PaymentTypesState = {
  paymentTypes: [],
  isLoading: false,
  error: null,
};

const paymentTypesSlice = createSlice({
  name: 'paymentTypes',
  initialState,
  reducers: {
    setPaymentTypes: (state, action: PayloadAction<PaymentTypes[]>) => {
      state.paymentTypes = action.payload;
    },
    clearPaymentTypes: (state) => {
      state.paymentTypes = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET
      .addMatcher(
        paymentTypesApi.endpoints.getPaymentTypes.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        paymentTypesApi.endpoints.getPaymentTypes.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.paymentTypes = payload;
        }
      )
      .addMatcher(
        paymentTypesApi.endpoints.getPaymentTypes.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch payment types';
        }
      )

      // DELETE
      .addMatcher(
        paymentTypesApi.endpoints.deletePaymentType.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        paymentTypesApi.endpoints.deletePaymentType.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const paymentTypeId = action.meta.arg.originalArgs as string;
          state.paymentTypes = state.paymentTypes.filter(
            (paymentType) => paymentType.id !== paymentTypeId
          );
        }
      )
      .addMatcher(
        paymentTypesApi.endpoints.deletePaymentType.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete payment type';
        }
      )

      // POST
      .addMatcher(
        paymentTypesApi.endpoints.postPaymentType.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        paymentTypesApi.endpoints.postPaymentType.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.paymentTypes.push({
            id: payload._id,
            name: payload.name,
            paymentsGTypeId: payload.paymentsGTypeId,
            isActive: payload.isActive,
            defaultPayment: payload.defaultPayment,
            showCaption: payload.showCaption,
            userId: payload.userId
          });
        }
      )
      .addMatcher(
        paymentTypesApi.endpoints.postPaymentType.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create payment type';
        }
      )

      // PUT
      .addMatcher(
        paymentTypesApi.endpoints.putPaymentType.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        paymentTypesApi.endpoints.putPaymentType.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          const updated = {
            id: payload.data._id,
            name: payload.data.name,
            paymentsGTypeId: payload.data.paymentsGTypeId,
            isActive: payload.data.isActive,
            defaultPayment: payload.data.defaultPayment,
            showCaption: payload.data.showCaption,
            userId: payload.data.userId
          };
          state.paymentTypes = state.paymentTypes.map((paymentType) =>
            paymentType.id === updated.id ? updated : paymentType
          );
        }
      )
      .addMatcher(
        paymentTypesApi.endpoints.putPaymentType.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update payment type';
        }
      );
  },
});

export const { setPaymentTypes, clearPaymentTypes } = paymentTypesSlice.actions;

export const selectPaymentTypes = (state: RootState) => state.paymentTypes.paymentTypes;
export const selectPaymentTypesLoading = (state: RootState) => state.paymentTypes.isLoading;
export const selectPaymentTypesError = (state: RootState) => state.paymentTypes.error;

export default paymentTypesSlice.reducer;