import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import CustomModal from '../../CustomModal';
import { FiSearch, FiArrowLeft, FiArrowRight } from 'react-icons/fi';
import {
  selectHeldOrdersSortedByDate,
  selectHeldOrdersCount,
  removeHeldOrder,

} from '../../../store/slices/holdorder';
import {
  setCartItems,
  setDiscountAmount,
  setDiscountReason,
  setDiscountType,
  setTaxPercentage,
  setTipAmount,
  setLoyaltyPercentage,
  setLoyaltyFixedAmount,
  setLoyaltyType,
  setSelectedCoupon,
  setCouponOfferAmount,

} from '../../../store/slices/cartSlice';
import { useGetCustomersQuery } from '../../../store/api/pos/customer';
import { setSelectedCustomer } from '../../../store/slices/selectedcustomer';

interface HoldOrdersModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const HoldOrdersModal: React.FC<HoldOrdersModalProps> = ({ isOpen, onClose }) => {
  const dispatch = useDispatch();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [selectedOrderNo, setSelectedOrderNo] = useState<string | null>(null);
  const userId = localStorage.getItem("userId") || ""
  const { data: customers } = useGetCustomersQuery(userId)
  // Get held orders from Redux store
  const heldOrders = useSelector(selectHeldOrdersSortedByDate);
  const totalOrders = useSelector(selectHeldOrdersCount);

  // Pagination settings - changed to 5 orders per page
  const ordersPerPage = 5;

  // Filter orders based on search term
  const filteredOrders = heldOrders.filter(order =>
    order.orderNo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.tableNo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.operatorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.customerId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.amount.toString().includes(searchTerm)
  );

  // Calculate pagination
  const totalPages = Math.ceil(filteredOrders.length / ordersPerPage) || 1;
  const startIndex = (currentPage - 1) * ordersPerPage;
  const endIndex = startIndex + ordersPerPage;
  const currentOrders = filteredOrders.slice(startIndex, endIndex);

  const handleSearch = (): void => {
    setCurrentPage(1); // Reset to first page when searching
  };

  const handlePrevPage = (): void => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = (): void => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleOrderSelect = (orderNo: string): void => {
    setSelectedOrderNo(orderNo);
  };

  const handleLoadOrder = (): void => {
    if (!selectedOrderNo) {
      alert('Please select an order to load');
      return;
    }

    const selectedOrder = heldOrders.find(order => order.orderNo === selectedOrderNo);
    if (!selectedOrder) return;

    // Load the held order data back into the cart
    dispatch(setCartItems(selectedOrder.items));
    dispatch(setDiscountAmount(selectedOrder.discountAmount));
    dispatch(setDiscountReason(selectedOrder.discountReason));
    dispatch(setDiscountType(selectedOrder.discountType));
    dispatch(setTaxPercentage(selectedOrder.taxPercentage));
    dispatch(setTipAmount(selectedOrder.tipAmount));
    dispatch(setLoyaltyPercentage(selectedOrder.loyaltyPercentage));
    dispatch(setLoyaltyFixedAmount(selectedOrder.loyaltyFixedAmount));
    dispatch(setLoyaltyType(selectedOrder.loyaltyType));
    dispatch(setSelectedCoupon(selectedOrder.selectedCoupon));
    dispatch(setCouponOfferAmount(selectedOrder.couponOfferAmount));
    console.log("Customer ID from selected order:", selectedOrder.customerId);
    // Remove the held order after loading it
    dispatch(removeHeldOrder(selectedOrderNo));
    if (selectedOrder.customerId) {
      let customer = customers?.find((customer) => {
        return customer?.id === selectedOrder.customerId
      })
      if (customer) {
        // Map Customer to SelectedCustomer format
        const selectedCustomerData = {
          _id: customer.id,
          CustomerId: customer.id,
          FirstName: customer.firstName,
          LastName: customer.lastName,
          Email: customer.email,
          Phone: customer.phone,
          Address: customer.address,
          City: customer.city,
          State: customer.state,
          isActive: true, // Default value since not available in Customer type
          userId: userId,
          CardNo: customer.cardNo || '',
          LastVisit: customer.lastVisit || '',
          creditLimits: 0, // Default value since not available in Customer type
          timeStamp: new Date().toISOString(), // Default value since not available in Customer type
          __v: 0, // Default value since not available in Customer type
          CustomerLoyalty: customer.CustomerLoyalty
        };
        dispatch(setSelectedCustomer(selectedCustomerData))
      }
    }
    onClose();
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const footer = (
    <div className="flex justify-end gap-4">
      <button
        onClick={onClose}
        className="px-8 py-3 border border-orange-500 text-orange-500 font-medium rounded-full hover:bg-orange-50 transition-colors"
      >
        Cancel
      </button>
      <button
        onClick={handleLoadOrder}
        className="px-8 py-3 bg-orange-500 text-white font-medium rounded-full hover:bg-orange-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
        disabled={!selectedOrderNo}
      >
        Load
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Hold Orders (${totalOrders})`}
      width="max-w-6xl"
      footer={footer}
      zIndex={1000000}
    >
      <div className="p-6">
        {/* Search Bar */}
        <div className="mb-6 flex items-center border border-gray-200 rounded-lg overflow-hidden">
          <div className="pl-4">
            <FiSearch className="text-gray-400" size={20} />
          </div>
          <input
            type="text"
            placeholder="Search by Order No, Table, Operator, Customer..."
            className="w-full p-3 focus:outline-none text-base"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <button
            onClick={handleSearch}
            className="px-6 py-3 bg-gray-100 border-l border-gray-300 hover:bg-gray-200 transition-colors"
          >
            →
          </button>
        </div>

        {/* Table Headers */}
        <div className="grid grid-cols-6 gap-4 py-3 border-b border-gray-200 font-medium text-gray-600">
          <div>Order No</div>
          <div>Table No</div>
          <div>Operator Name</div>
          <div>Customer</div>
          <div>Amount</div>
          <div>Record Date</div>
        </div>

        {/* Table Content - removed scroll and fixed height */}
        <div className="min-h-[300px]">
          {currentOrders.length > 0 ? (
            <>
              {currentOrders.map((order) => (
                <div
                  key={order.orderNo}
                  className={`grid grid-cols-6 gap-4 py-4 border-b border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors ${selectedOrderNo === order.orderNo ? 'bg-orange-50 border-orange-300' : ''
                    }`}
                  onClick={() => handleOrderSelect(order.orderNo)}
                >
                  <div className="font-medium">{order.orderNo}</div>
                  <div>{order.tableNo || '-'}</div>
                  <div>{order.operatorName}</div>
                  <div>
                    <div>{order.customer}</div>
                    {order.customerId && (
                      <div className="text-xs text-gray-500">ID: {order.customerId}</div>
                    )}
                  </div>
                  <div className="font-semibold">${order.amount.toFixed(2)}</div>
                  <div className="text-sm">{formatDate(order.recordDate)}</div>
                </div>
              ))}
              {/* Add empty rows to maintain consistent height */}
              {Array.from({ length: ordersPerPage - currentOrders.length }).map((_, index) => (
                <div
                  key={`empty-${index}`}
                  className="grid grid-cols-6 gap-4 py-4 border-b border-gray-200"
                  style={{ minHeight: '64px' }}
                >
                  {/* Empty row for consistent spacing */}
                </div>
              ))}
            </>
          ) : (
            <div className="py-8 text-center text-gray-500">
              {searchTerm ? 'No orders found matching your search' : 'No held orders available'}
            </div>
          )}
        </div>

        {/* Order Details Preview */}
        {selectedOrderNo && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">Order Items:</h4>
            <div className="text-sm space-y-1">
              {heldOrders
                .find(order => order.orderNo === selectedOrderNo)
                ?.items.map((item, index) => (
                  <div key={`${item.id}-${index}`} className="flex justify-between">
                    <span>{item.name} x{item.quantity}</span>
                    <span>${(item.price * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Pagination */}
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {filteredOrders.length > 0 ? startIndex + 1 : 0}-{Math.min(endIndex, filteredOrders.length)} of {filteredOrders.length} orders
          </div>
          <div className="flex items-center">
            <button
              onClick={handlePrevPage}
              className="px-4 py-2 border border-gray-300 rounded-l-lg text-gray-600 hover:bg-gray-100 flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white"
              disabled={currentPage === 1}
            >
              <FiArrowLeft className="mr-1" /> Previous
            </button>
            <div className="px-4 py-2 border-t border-b border-gray-300 bg-white">
              {currentPage} of {totalPages}
            </div>
            <button
              onClick={handleNextPage}
              className="px-4 py-2 border border-gray-300 rounded-r-lg text-gray-600 hover:bg-gray-100 flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white"
              disabled={currentPage >= totalPages}
            >
              Next <FiArrowRight className="ml-1" />
            </button>
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default HoldOrdersModal;