import React, { useEffect, useRef, useState } from "react";
import CustomModal from "../../CustomModal";
import { FiSearch, <PERSON>P<PERSON>ter, FiMail } from "react-icons/fi";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format } from "date-fns";
import { useGetAggregatedOrdersQuery } from "../../../store/api/pos/customer";
import { useGetAggregatedOrdersByDateQuery } from "../../../store/api/pos/customer";

interface InventorySalesReportProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ProductItem {
  name: string;
  price: number;
  productId: string;
  qty: number;
  remainingqty: number;
  unitPrice: number;
}

const InventorySalesReport: React.FC<InventorySalesReportProps> = ({
  isOpen,
  onClose,
}) => {
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ]);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredProducts, setFilteredProducts] = useState<ProductItem[]>([]);
  const recordsPerPage = 5;
  const [reportType, setReportType] = useState<string>("Monthly");
  const [isDateFilterActive, setIsDateFilterActive] = useState(false);

  const userId = localStorage.getItem("userId") || "";

  // Use the query with options to ensure it refetches when reportType changes
  const { data, isLoading, error, isFetching } = useGetAggregatedOrdersQuery({
    userId,
    reporttype: reportType
  }, {
    // Force refetch when reportType changes
    refetchOnMountOrArgChange: true,
    // Skip this query when date filter is active
    skip: isDateFilterActive
  });

  // Only call the date-based query when both dates are selected
  const shouldCallDateQuery = isDateFilterActive && startDate && endDate;

  const {
    data: datedata,
    isLoading: secondLoading,
    error: error2
  } = useGetAggregatedOrdersByDateQuery({
    userId,
    startDate: startDate?.toISOString() || "",
    endDate: endDate?.toISOString() || "",
  }, {
    // Skip this query if date range is not selected
    skip: !shouldCallDateQuery
  });

  // Determine which data to use based on active filter
  const activeData = isDateFilterActive ? datedata : data;

  // Combine loading states
  const isLoadingData = isLoading || isFetching || (shouldCallDateQuery && secondLoading);

  // Filter products based on search term and update when data or search changes
  useEffect(() => {
    if (activeData?.combinedProducts) {
      console.log("Data received:", activeData);
      if (searchTerm.trim() === "") {
        setFilteredProducts(activeData.combinedProducts);
      } else {
        const filtered = activeData.combinedProducts.filter(product =>
          product.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setFilteredProducts(filtered);
      }
      // Reset to first page when data changes
      setCurrentPage(1);
    } else {
      // Clear filtered products if no data
      setFilteredProducts([]);
    }
  }, [activeData, searchTerm]);

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      const newStartDate = selection.startDate || null;
      const newEndDate = selection.endDate || null;
      setDateRange([newStartDate, newEndDate]);

      // Activate date filter if both dates are selected
      if (newStartDate && newEndDate) {
        setIsDateFilterActive(true);
      }
    }
  };

  // Clear date filter
  const clearDateFilter = () => {
    setDateRange([null, null]);
    setIsDateFilterActive(false);
    setCurrentPage(1);
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "Select Date Range";
    return `${format(startDate, "MMM dd, yyyy ")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };

  // Pagination calculations
  const totalPages = Math.ceil((filteredProducts?.length || 0) / recordsPerPage);
  const indexOfLastRecord = currentPage * recordsPerPage;
  const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;
  const currentProducts = filteredProducts?.slice(indexOfFirstRecord, indexOfLastRecord) || [];

  const handlePrint = () => {
    window.print();
  };

  const handleSend = () => {
    // Implement email sending functionality
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={goToPreviousPage}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage >= totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={goToNextPage}
          disabled={currentPage >= totalPages || filteredProducts.length === 0}
        >
          Next →
        </button>
      </div>
      <div className="flex gap-4 font-bold">
        <button
          onClick={handleSend}
          className="px-14 py-2 bg-orange text-white rounded-full cursor-pointer transition-colors flex items-center gap-2"
        >
          Send <FiMail size={20} />
        </button>
        <button
          onClick={handlePrint}
          className="px-14 py-2 bg-black text-white rounded-full cursor-pointer transition-colors flex items-center gap-2"
        >
          Print <FiPrinter size={20} />
        </button>
        <button
          onClick={onClose}
          className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
        >
          Cancel
        </button>
      </div>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Inventory Sales Report"
      width="max-w-5xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search and Filters */}
        <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
          <div className="relative flex-1 mr-8">
            <div className="flex items-center">
              <FiSearch className="text-gray-400 mr-2" size={20} />
              <input
                type="text"
                placeholder="Search Inventory Item"
                className="w-full py-2 focus:outline-none text-base"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
          </div>
          <div className="relative mx-7 flex items-center">
            <button
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
              className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md cursor-pointer"
            >
              {formatDateRange()}
            </button>
            {isDateFilterActive && (
              <button
                onClick={clearDateFilter}
                className="ml-2 text-orange hover:text-orange-700 text-sm"
              >
                Clear
              </button>
            )}
            {isCalendarOpen && (
              <div
                ref={calendarRef}
                className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
              >
                <DateRangePicker
                  ranges={[
                    {
                      startDate: startDate || new Date(),
                      endDate: endDate || new Date(),
                      key: "selection",
                    },
                  ]}
                  onChange={handleDateRangeChange}
                  direction="horizontal"
                  moveRangeOnFirstSelection={false}
                />
              </div>
            )}
          </div>
          <select
            className="border border-[#E4E4E4] rounded-full p-2"
            value={reportType}
            onChange={(e) => {
              const newReportType = e.target.value;
              console.log("Changing report type to:", newReportType);
              // Update report type which will trigger a new API call
              setReportType(newReportType);
              // Reset to first page when report type changes
              setCurrentPage(1);
              // Clear filtered products while loading new data
              setFilteredProducts([]);
              // Disable date filter when changing report type
              if (isDateFilterActive) {
                clearDateFilter();
              }
            }}
            disabled={isDateFilterActive}
          >
            <option value="Monthly">Monthly</option>
            <option value="Weekly">Weekly</option>
            <option value="Daily">Daily</option>
          </select>
        </div>

        {/* Active filters display */}
        {(isDateFilterActive || reportType !== "Monthly") && (
          <div className="mb-4 flex flex-wrap gap-2">
            <span className="text-sm text-gray-500">Active filters:</span>
            {isDateFilterActive && startDate && endDate && (
              <span className="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">
                Date: {format(startDate, "dd/MM/yyyy")} - {format(endDate, "dd/MM/yyyy")}
              </span>
            )}
            {!isDateFilterActive && (
              <span className="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">
                Report Type: {reportType}
              </span>
            )}
          </div>
        )}

        {/* Table */}
        <div className="overflow-x-auto">
          {isLoadingData ? (
            <div className="flex justify-center py-8">
              <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
              <div className="ml-3 text-orange-500">
                Loading {isDateFilterActive ? "custom date range" : reportType} report data...
              </div>
            </div>
          ) : (error || error2) ? (
            <div className="text-center py-8 text-red-500">
              Error loading data. Please try again.
            </div>
          ) : (
            <>
              <table className="w-full">
                <thead>
                  <tr className="text-left text-[#9C9C9C] text-xs font-extralight border-b border-[#E4E4E4]">
                    <th className="pb-3">Product Name</th>
                    <th className="pb-3">Ordered Quantity</th>
                    <th className="pb-3">Single Price</th>
                    <th className="pb-3">Total Price</th>
                  </tr>
                </thead>
                <tbody>
                  {currentProducts.length > 0 ? (
                    currentProducts.map((product, index) => (
                      <tr key={index} className="border-b text-sm border-[#E4E4E4]">
                        <td className="py-4">{product.name}</td>
                        <td className="py-4">{product.qty}</td>
                        <td className="py-4">${product.unitPrice.toFixed(2)}</td>
                        <td className="py-4">${product.price.toFixed(2)}</td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={4} className="py-4 text-center text-gray-500">
                        {searchTerm ? `No products found matching "${searchTerm}"` : "No products available"}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>

              {/* Pagination info */}
              {filteredProducts.length > 0 && (
                <div className="mt-4 text-sm text-gray-500 text-right">
                  Showing {indexOfFirstRecord + 1}-{Math.min(indexOfLastRecord, filteredProducts.length)} of {filteredProducts.length} records
                </div>
              )}
            </>
          )}
        </div>

        {/* Total */}
        <div className="flex justify-end mt-4 mb-2 border border-[#E4E4E4] p-2">
          <div className="text-right">
            <span className="text-black text-lg">Total: </span>
            <span className="text-orange font-bold text-xl">
              ${activeData?.totalPrice ? activeData.totalPrice.toFixed(2) : "0.00"}
            </span>
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default InventorySalesReport;