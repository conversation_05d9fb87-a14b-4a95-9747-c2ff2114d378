import React, { useState } from 'react';
import CustomModal from '../../CustomModal';
import { Printer } from 'lucide-react';
import { useGetXReportQuery } from '../../../store/api/pos/orderapi';

interface ZReportSearchResultsProps {
    isOpen: boolean;
    onClose: () => void;
    searchParams: {
        employee: string;
        employeeId: string | null;
        timeRange: string;
        dateRange: { startDate: string; endDate: string } | null;
        timeSelection: { startTime: string; endTime: string } | null;
        showOnlyTotals: boolean;
        showProducts: boolean;
        paymentMethods: Array<{
            id: string;
            label: string;
            isActive: boolean;
            isDefault: boolean;
        }>;
        isCustomDateRange?: boolean;
    };
}

const ZReportSearchResults: React.FC<ZReportSearchResultsProps> = ({
    isOpen,
    onClose,
    searchParams,
}) => {
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 5; // Show only 5 items per page
    const userId = localStorage.getItem("userId") || "";

    // Convert timeRange to reportType
    const getReportType = (): 'Daily' | 'Weekly' | 'Monthly' | 'date' => {
        // If custom date range is selected, use 'date' as reportType
        if (searchParams.isCustomDateRange) {
            return 'date';
        }

        // Otherwise, determine reportType based on timeRange
        switch (searchParams.timeRange) {
            case 'Today':
            case 'Yesterday':
                return 'Daily';
            case 'This Week':
                return 'Weekly';
            case 'This Month':
                return 'Monthly';
            default:
                return 'Daily';
        }
    };

    // Prepare API parameters
    const apiParams = {
        userId,
        employeeId: searchParams.employeeId || undefined,
        reportType: getReportType(),
        paymentIds: searchParams.paymentMethods.map(method => method.id),
        status: 'payment' as const,
        startDate: searchParams.dateRange?.startDate,
        endDate: searchParams.dateRange?.endDate
    };

    // Fetch data from API
    const { data: reportData, isLoading, error } = useGetXReportQuery(apiParams);

    // Process orders data to display individual orders
    const orderItems = React.useMemo(() => {
        if (!reportData || !reportData.orders || !Array.isArray(reportData.orders)) {
            return [];
        }

        // Map each order to the display format
        return reportData.orders.map(order => ({
            paymentName: order.paymentType || 'Unknown',
            count: 1, // Each row represents a single order
            total: order.totalPrice || 0
        }));
    }, [reportData]);

    // Get paginated items
    const paginatedItems = React.useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return orderItems.slice(startIndex, endIndex);
    }, [orderItems, currentPage, itemsPerPage]);

    // Calculate total pages
    const totalPages = React.useMemo(() => {
        return Math.ceil(orderItems.length / itemsPerPage);
    }, [orderItems, itemsPerPage]);

    // We don't need the paginatedOrders anymore since we're only showing the payment summary

    // Calculate totals from the orders data
    const totalSalesWithoutTax = React.useMemo(() => {
        if (!reportData?.summary) return 0;
        return reportData.summary.totalRevenue || 0;
    }, [reportData]);

    const totalTax = React.useMemo(() => {
        if (!reportData?.summary) return 0;
        return reportData.summary.totalTax || 0;
    }, [reportData]);

    const totalSales = React.useMemo(() => {
        if (!reportData?.summary) return 0;
        return reportData.summary.totalRevenue + reportData.summary.totalTax || 0;
    }, [reportData]);

    // Handle pagination
    const handlePrevPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    const handleNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    // Handle print
    const handlePrint = () => {
        window.print();
    };

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={onClose}
            title="Z-Report Sales Summary"
            width="max-w-3xl"
            zIndex={100}
        >
            <div className="p-6">
                {/* Employee Info */}
                <div className="flex items-center mb-6">
                    <div className="text-gray-700 font-medium">Employee:</div>
                    <div className="ml-2 text-orange-500 font-bold text-lg">{searchParams.employee}</div>
                </div>

                {isLoading ? (
                    // Loading state
                    <div className="flex justify-center items-center p-12">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
                        <span className="ml-3 text-gray-600">Loading report data...</span>
                    </div>
                ) : error ? (
                    // Error state
                    <div className="p-6 bg-red-50 border border-red-200 rounded-lg text-red-600 mb-6">
                        <p className="font-medium mb-2">Error loading report data</p>
                        <p>Please try again later or contact support if the problem persists.</p>
                    </div>
                ) : (
                    <>
                        {/* Section Title for Orders */}
                        <h3 className="text-lg font-medium text-gray-800 mb-3">Z-Report Details</h3>

                        {/* Orders Table */}
                        <div className="mb-6 border rounded-lg overflow-hidden">
                            {/* Table Header */}
                            <div className="grid grid-cols-3 bg-gray-50 border-b">
                                <div className="p-4 font-medium text-gray-600">Payment Name</div>
                                <div className="p-4 font-medium text-gray-600 text-center">Count</div>
                                <div className="p-4 font-medium text-gray-600 text-right">Total</div>
                            </div>

                            {/* Table Body */}
                            <div className="divide-y">
                                {orderItems.length === 0 ? (
                                    <div className="p-4 text-center text-gray-500">No payment data available</div>
                                ) : (
                                    paginatedItems.map((item, index) => (
                                        <div key={index} className="grid grid-cols-3 hover:bg-gray-50">
                                            <div className="p-4 text-gray-700">{item.paymentName}</div>
                                            <div className="p-4 text-gray-700 text-center">{item.count}</div>
                                            <div className="p-4 text-gray-700 text-right">$ {item.total}</div>
                                        </div>
                                    ))
                                )}
                            </div>
                        </div>

                        {/* Totals Summary */}
                        <div className="bg-gray-50 p-4 rounded-lg mb-6 flex justify-between items-center">
                            <div className="flex items-center">
                                <span className="text-gray-700 font-medium">Total Sales Without Tax:</span>
                                <span className="ml-2 text-orange-500 font-bold text-xl">${totalSalesWithoutTax.toFixed(1)}</span>
                            </div>
                            <div className="flex items-center">
                                <span className="text-gray-700 font-medium">Total Sales:</span>
                                <span className="ml-2 text-orange-500 font-bold text-xl">${totalTax.toFixed(2)}</span>
                            </div>
                            <div className="flex items-center">
                                <span className="text-gray-700 font-medium">Total:</span>
                                <span className="ml-2 text-orange-500 font-bold text-xl">${totalSales.toFixed(2)}</span>
                            </div>
                        </div>
                    </>
                )}

                {/* Pagination and Actions */}
                <div className="flex justify-between items-center">
                    {/* Pagination */}
                    <div className="flex items-center border border-gray-200 rounded-full overflow-hidden">
                        <button
                            onClick={handlePrevPage}
                            className="px-4 py-2 flex items-center text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                            disabled={currentPage === 1}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                            Previous
                        </button>
                        <div className="px-4 py-2 border-x border-gray-200 bg-white">
                            {currentPage}
                        </div>
                        <button
                            onClick={handleNextPage}
                            className="px-4 py-2 flex items-center text-gray-500 hover:bg-gray-50"
                            disabled={currentPage >= totalPages}
                        >
                            Next
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-4">
                        <button
                            onClick={handlePrint}
                            className="px-6 py-2.5 bg-black text-white rounded-full flex items-center"
                            disabled={isLoading || !!error}
                        >
                            <Printer className="w-4 h-4 mr-2" />
                            Print
                        </button>
                        <button
                            onClick={onClose}
                            className="px-6 py-2.5 border border-orange-500 text-orange-500 rounded-full"
                        >
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </CustomModal>
    );
};

export default ZReportSearchResults;
