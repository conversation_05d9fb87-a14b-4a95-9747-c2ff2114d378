import React, { useState } from "react";
import { ChevronLeft, ChevronDown } from "lucide-react";
import { useNavigate } from "react-router-dom";

export default function AddSMSCampaign() {
  const [formData, setFormData] = useState({
    campaignName: "",
    subject: "",
    recipients: "",
    customRecipients: "",
    message: "",
    isPublished: false,
  });

  const [showRecipientDropdown, setShowRecipientDropdown] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleToggleChange = () => {
    setFormData((prev) => ({ ...prev, isPublished: !prev.isPublished }));
  };

  const navigate = useNavigate();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    navigate("/admin/marketing/sms-marketing");
    // Add API call or state management logic here
  };

  const handleCancel = () => {
    console.log("Form cancelled");
    navigate("/admin/marketing/sms-marketing");
    // Add navigation or reset logic here
  };

  return (
    <div className="bg-gray-50 min-h-screen p-4">
      <div className="">
        {/* Header */}
        <div className="flex items-center mb-6 text-gray-800 bg-white rounded-2xl border border-gray-200 p-2">
          <ChevronLeft
            onClick={() => {
              navigate(-1);
            }}
            className="w-5 h-5 mr-2"
          />
          <h1 className="text-xl font-medium">Add SMS Campaign</h1>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Campaign Details Section */}
          <div className="mb-6">
            <div className="bg-orange-50 py-3 px-4 text-gray-800 font-medium rounded-t-lg border-b border-orange-100">
              SMS Campaign Details
            </div>
            <div className="bg-white p-6 rounded-b-lg border border-gray-200 border-t-0 space-y-6">
              {/* Two columns layout */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Campaign Name */}
                <div>
                  <label className="block text-sm mb-2">
                    SMS Campaign Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="campaignName"
                    placeholder="Enter SMS Campaign Name"
                    value={formData.campaignName}
                    onChange={handleInputChange}
                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                    required
                  />
                </div>

                {/* Subject */}
                <div>
                  <label className="block text-sm mb-2">
                    SMS Subject <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="subject"
                    placeholder="Enter SMS Subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                    required
                  />
                </div>
              </div>

              {/* Second row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Recipients Dropdown */}
                <div>
                  <label className="block text-sm mb-2">
                    SMS Recipients <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <button
                      type="button"
                      className="w-full bg-white border border-gray-300 rounded-md p-2 flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-orange-500"
                      onClick={() =>
                        setShowRecipientDropdown(!showRecipientDropdown)
                      }
                    >
                      <span className="text-gray-500">
                        Select SMS Recipients
                      </span>
                      <ChevronDown className="w-4 h-4 text-gray-500" />
                    </button>
                    {showRecipientDropdown && (
                      <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg">
                        <ul className="py-1">
                          <li className="px-3 py-2 hover:bg-gray-100 cursor-pointer">
                            All Customers
                          </li>
                          <li className="px-3 py-2 hover:bg-gray-100 cursor-pointer">
                            New Customers
                          </li>
                          <li className="px-3 py-2 hover:bg-gray-100 cursor-pointer">
                            Regular Customers
                          </li>
                        </ul>
                      </div>
                    )}
                  </div>
                </div>

                {/* Custom Recipients */}
                <div>
                  <label className="block text-sm mb-2">
                    Custom SMS Recipients{" "}
                    <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="customRecipients"
                    placeholder="Enter Custom SMS Recipients"
                    value={formData.customRecipients}
                    onChange={handleInputChange}
                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                    required
                  />
                </div>
              </div>

              {/* Message Composition */}
              <div>
                <label className="block text-sm mb-2">
                  Compose SMS <span className="text-red-500">*</span>
                </label>
                <textarea
                  name="message"
                  placeholder="Compose SMS"
                  value={formData.message}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md p-2 h-32 focus:outline-none focus:ring-2 focus:ring-orange-500"
                  required
                />
              </div>
            </div>
          </div>

          {/* Permissions Section */}
          <div className="mb-6">
            <div className="bg-orange-50 py-3 px-4 text-gray-800 font-medium rounded-t-lg border-b border-orange-100">
              Permissions
            </div>
            <div className="bg-white p-6 rounded-b-lg border border-gray-200 border-t-0">
              <div className="flex items-center">
                <label className="inline-flex relative items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={formData.isPublished}
                    onChange={handleToggleChange}
                  />
                  <div
                    className={`w-11 h-6 rounded-full peer transition-all duration-200 
                    ${formData.isPublished ? "bg-orange-500" : "bg-gray-300"} 
                    after:content-[''] after:absolute after:top-0.5 after:left-0.5 
                    after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all
                    ${formData.isPublished ? "after:translate-x-5" : ""}`}
                  ></div>
                </label>
                <span className="ml-3 text-sm font-medium text-gray-700">
                  Publish
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600"
            >
              Add SMS Campaign
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
