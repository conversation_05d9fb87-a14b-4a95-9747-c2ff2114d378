import { createSlice } from '@reduxjs/toolkit';
// import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import { emailMarketingApi } from '../api/emailMarketingApi';

// Define proper types for email marketing data
export interface EmailMarketingItem {
  _id: string;
  subject: string;
  email: string[] | string;
  message: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface EmailMarketingState {
  emailMarketings: EmailMarketingItem[];
  isLoading: boolean;
  error: string | null;
}

const initialState: EmailMarketingState = {
  emailMarketings: [],
  isLoading: false,
  error: null,
};

const emailMarketingSlice = createSlice({
  name: 'emailMarketings',
  initialState,
  reducers: {
    // Local reducers for manual operations if needed
    setEmailMarketings: (state, action) => {
      state.emailMarketings = action.payload;
    },
    clearEmailMarketings: (state) => {
      state.emailMarketings = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle getEmailMarketing query
      .addMatcher(
        emailMarketingApi.endpoints.getEmailMarketing.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        emailMarketingApi.endpoints.getEmailMarketing.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          state.emailMarketings = action.payload;
        }
      )
      .addMatcher(
        emailMarketingApi.endpoints.getEmailMarketing.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to fetch email campaigns';
        }
      )
      
      // Handle createEmailMarketing mutation
      .addMatcher(
        emailMarketingApi.endpoints.createEmailMarketing.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        emailMarketingApi.endpoints.createEmailMarketing.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          state.emailMarketings.push(action.payload);
        }
      )
      .addMatcher(
        emailMarketingApi.endpoints.createEmailMarketing.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to create email campaign';
        }
      )
      
      // Handle deleteEmailMarketing mutation
      .addMatcher(
        emailMarketingApi.endpoints.deleteEmailMarketing.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        emailMarketingApi.endpoints.deleteEmailMarketing.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          // Filter out the deleted campaign
          const deletedId = action.meta.arg.originalArgs;
          state.emailMarketings = state.emailMarketings.filter(
            (campaign) => campaign._id !== deletedId
          );
        }
      )
      .addMatcher(
        emailMarketingApi.endpoints.deleteEmailMarketing.matchRejected,
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message || 'Failed to delete email campaign';
        }
      );
  },
});

// Export actions
export const { setEmailMarketings, clearEmailMarketings } = emailMarketingSlice.actions;

// Export selectors
export const selectEmailMarketings = (state: RootState) => state.emailMarketings.emailMarketings;
export const selectEmailMarketingsLoading = (state: RootState) => state.emailMarketings.isLoading;
export const selectEmailMarketingsError = (state: RootState) => state.emailMarketings.error;

// Export reducer
export default emailMarketingSlice.reducer;