import React, { useState } from 'react';
import CustomModal from '../CustomModal';

export interface WithModalProps {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
}

export const withModal = <P extends object>(
  WrappedComponent: React.ComponentType<P & WithModalProps>,
  modalTitle: string
) => {
  return (props: P) => {
    const [isOpen, setIsOpen] = useState(false);

    const handleOpen = () => setIsOpen(true);
    const handleClose = () => setIsOpen(false);

    return (
      <CustomModal
        isOpen={isOpen}
        onClose={handleClose}
        title={modalTitle}
      >
        <WrappedComponent
          {...props}
          isOpen={isOpen}
          onOpen={handleOpen}
          onClose={handleClose}
        />
      </CustomModal>
    );
  };
};