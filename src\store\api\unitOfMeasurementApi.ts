import { baseApi } from "./baseApi";

export interface UnitOfMeasurement {
  id: string;
  name: string;
  code: string;
  userId: string;
  active: boolean;
  value: string;
}

export const unitOfMeasurementApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getUnitOfMeasurements: builder.query<UnitOfMeasurement[], string>({
      query: (userId) => `/mu?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          name: item.name,
          code: item.code,
          userId: item.userId,
          active: item.active,
          value: item.value,
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "UnitOfMeasurement" as const, id })),
              { type: "UnitOfMeasurement", id: "LIST" },
            ]
          : [{ type: "UnitOfMeasurement", id: "LIST" }],
    }),

    getUnitOfMeasurement: builder.query<UnitOfMeasurement, string>({
      query: (id) => `/mu/${id}`,
      transformResponse: (response: any) => ({
        id: response._id,
        name: response.name,
        code: response.code,
        userId: response.userId,
        active: response.active,
        value: response.value,
      }),
      providesTags: (_result, _error, id) => [{ type: "UnitOfMeasurement", id }],
    }),

    deleteUnitOfMeasurement: builder.mutation<void, string>({
      query: (id) => ({
        url: `/mu/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            unitOfMeasurementApi.util.invalidateTags([
              { type: "UnitOfMeasurement", id },
              { type: "UnitOfMeasurement", id: "LIST" }
            ])
          );
        } catch (error) {
          console.error("Failed to delete unit of measurement", error);
        }
      },
    }),

    postUnitOfMeasurement: builder.mutation<any, any>({
      query: (data) => {
        console.log("Sending POST request with data:", data);
        return {
          url: "/mu",
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: [{ type: "UnitOfMeasurement", id: "LIST" }],
    }),

    putUnitOfMeasurement: builder.mutation<
      any,
      { id: string; formData: any }
    >({
      query: ({ id, formData }) => ({
        url: `/mu/${id}`,
        method: "PUT",
        body: {
          ...formData,
        },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "UnitOfMeasurement", id },
        { type: "UnitOfMeasurement", id: "LIST" },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetUnitOfMeasurementsQuery,
  useGetUnitOfMeasurementQuery,
  useDeleteUnitOfMeasurementMutation,
  usePostUnitOfMeasurementMutation,
  usePutUnitOfMeasurementMutation,
} = unitOfMeasurementApi;