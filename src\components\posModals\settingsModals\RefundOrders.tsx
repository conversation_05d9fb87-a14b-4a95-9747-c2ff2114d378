import React, { useEffect, useRef, useState } from "react";
import CustomModal from "../../CustomModal";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format } from "date-fns";
import { FiSearch } from "react-icons/fi";
import { IoEye } from "react-icons/io5";

import { useGetOrdersWithRefundDataQuery } from "../../../store/api/pos/orderapi";
import RefundOrderDetailsModal from "./RefundOrderDetailsModal";

interface RefundOrdersProps {
  isOpen: boolean;
  onClose: () => void;
}

const RefundableOrders: React.FC<RefundOrdersProps> = ({ isOpen, onClose }) => {
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ]);
  const userId = localStorage.getItem("userId") || "";
  const { data: orders = [], isLoading, error } = useGetOrdersWithRefundDataQuery(userId);

  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;
  const [currentPage, setCurrentPage] = useState(1);
  const [isOrderDetailsOpen, setIsOrderDetailsOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  // Pagination constants
  const ORDERS_PER_PAGE = 5;

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Reset to first page when search term or date range changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, startDate, endDate]);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      setDateRange([selection.startDate || null, selection.endDate || null]);
    }
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "Select Date Range";
    return `${format(startDate, "MMM dd, yyyy ")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };

  // Get customer name from order data
  const getCustomerName = (order: any) => {
    if (order.customername) return order.customername;
    if (order.customerId?.FirstName && order.customerId?.LastName) {
      return `${order.customerId.FirstName} ${order.customerId.LastName}`;
    }

    return "_";
  };

  // Get operator name from order data
  const getOperatorName = (order: any) => {
    if (order.orderId?.operator) return order.orderId.operator;
    // if (order.userId?.name) return order.userId.name;
    return "-";
  };

  // Get receipt number
  const getReceiptNumber = (order: any) => {
    if (order.OrderNumber) return order.OrderNumber;
    if (order.orderNo) return order.orderNo;
    if (order.recieptId?.recieptNo) return order.recieptId.recieptNo;
    return "-";
  };

  // Get tax amount
  const getTaxAmount = (order: any) => {
    if (order.lineValueTax) return order.lineValueTax;
    if (order.taxValue) return order.taxValue;
    if (order.tax && Array.isArray(order.tax)) {
      return order.tax.reduce((sum: number, t: any) => sum + (t.addtax || 0), 0);
    }
    if (order.recieptId?.tax) return order.recieptId.tax;
    return 0;
  };

  // Get received amount
  const getReceivedAmount = (order: any) => {
    if (order.recieveamount !== undefined) return order.recieveamount;
    if (order.recieptId?.cashTendered) return order.recieptId.cashTendered;
    if (order.grandTotal && order.dueamount !== undefined) {
      return order.grandTotal - order.dueamount;
    }
    return 0;
  };

  // Enhanced filter orders based on search term and date range
  const filteredOrders = orders.filter((order: any) => {
    const searchLower = searchTerm.toLowerCase().trim();

    // Date filtering - only apply if both dates are selected
    if (startDate && endDate) {
      const orderDate = new Date(order.createdAt);
      const start = new Date(startDate);
      const end = new Date(endDate);

      // Set time to start and end of day for proper comparison
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);

      if (orderDate < start || orderDate > end) {
        return false;
      }
    }

    // If no search term, return true (already filtered by date if applicable)
    if (!searchLower) return true;

    // Check customername field
    if (order.customername && order.customername.toLowerCase().includes(searchLower)) {
      return true;
    }

    // Check customerId fields
    if (order.customerId) {
      const firstName = order.customerId.FirstName?.toLowerCase() || "";
      const lastName = order.customerId.LastName?.toLowerCase() || "";
      const fullName = `${firstName} ${lastName}`.trim();

      // Search in first name
      if (firstName.includes(searchLower)) return true;

      // Search in last name
      if (lastName.includes(searchLower)) return true;

      // Search in full name
      if (fullName.includes(searchLower)) return true;
    }

    return false;
  });

  // Calculate pagination values
  const totalPages = Math.ceil(filteredOrders.length / ORDERS_PER_PAGE);
  const startIndex = (currentPage - 1) * ORDERS_PER_PAGE;
  const endIndex = startIndex + ORDERS_PER_PAGE;
  const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

  // Check if we're on first or last page
  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === totalPages || totalPages === 0;

  // Format currency
  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "-";
    return format(new Date(dateString), "yyyy-MM-dd");
  };

  // Handle view order details
  const handleViewOrder = (order: any) => {
    setSelectedOrder(order);
    setIsOrderDetailsOpen(true);
  };

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className={`px-4 py-2 text-sm font-medium cursor-pointer transition-colors ${isFirstPage
            ? "text-gray-400 cursor-not-allowed"
            : "text-[#9C9C9C] hover:text-gray-700"
            }`}
          onClick={() => !isFirstPage && setCurrentPage((prev) => prev - 1)}
          disabled={isFirstPage}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium cursor-pointer transition-colors ${isLastPage
            ? "text-gray-400 cursor-not-allowed"
            : "text-[#9C9C9C] hover:text-gray-700"
            }`}
          onClick={() => !isLastPage && setCurrentPage((prev) => prev + 1)}
          disabled={isLastPage}
        >
          Next →
        </button>
      </div>
      <button
        onClick={onClose}
        className="px-12 py-2 border border-orange text-orange text-xl font-poppins font-semibold rounded-full cursor-pointer transition-colors"
      >
        Cancel
      </button>
    </div>
  );

  if (isLoading) {
    return (
      <CustomModal
        isOpen={isOpen}
        onClose={onClose}
        title="Refund Orders"
        width="max-w-7xl"
        footer={footer}
      >
        <div className="p-6 flex justify-center items-center h-64">
          <div className="text-gray-500">Loading orders...</div>
        </div>
      </CustomModal>
    );
  }

  if (error) {
    return (
      <CustomModal
        isOpen={isOpen}
        onClose={onClose}
        title="Refund Orders"
        width="max-w-7xl"
        footer={footer}
      >
        <div className="p-6 flex justify-center items-center h-64">
          <div className="text-red-500">Error loading orders. Please try again.</div>
        </div>
      </CustomModal>
    );
  }

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Refund Orders"
      width="max-w-7xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search and Date Filter */}
        <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
          <div className="relative flex-1 mr-8">
            <div className="flex items-center">
              <FiSearch className="text-gray-400 mr-2" size={20} />
              <input
                type="text"
                placeholder="Search Customer (First name, Last name, or Full name)"
                className="w-full py-2 focus:outline-none text-base"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
          </div>
          <div className="relative mx-15">
            <button
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
              className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md  cursor-pointer"
            >
              {formatDateRange()}
            </button>
            {isCalendarOpen && (
              <div
                ref={calendarRef}
                className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
              >
                <DateRangePicker
                  ranges={[
                    {
                      startDate: startDate || new Date(),
                      endDate: endDate || new Date(),
                      key: "selection",
                    },
                  ]}
                  onChange={handleDateRangeChange}
                  direction="horizontal"
                  moveRangeOnFirstSelection={false}
                />
              </div>
            )}
          </div>
        </div>

        {/* Orders Table */}
        <div className="mb-6 overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left font-poppins font-extralight text-xs text-[#9C9C9C] border-b-1 border-[#E4E4E4]">
                <th className="px-4 py-2">Receipt Number</th>
                <th className="px-4 py-2">Operator Name</th>
                <th className="px-4 py-2">Customer</th>
                <th className="px-4 py-2">Record Date</th>
                <th className="px-4 py-2">Tax</th>
                <th className="px-4 py-2">Amount</th>
                <th className="px-4 py-2">Received Amount</th>
                <th className="px-4 py-2">Due Amount</th>
                <th className="px-4 py-2">Date</th>
                <th className="px-4 py-2">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y font-poppins divide-gray-100">
              {paginatedOrders.length === 0 ? (
                <tr>
                  <td colSpan={10} className="text-center py-8 text-gray-500">
                    {filteredOrders.length === 0 ? "No orders found" : "No orders on this page"}
                  </td>
                </tr>
              ) : (
                paginatedOrders.map((order: any) => (
                  <tr key={order._id} className="border-b-1 border-[#E4E4E4]">
                    <td className="px-4 py-3">
                      {getReceiptNumber(order)}
                    </td>
                    <td className="px-4 py-3">
                      {getOperatorName(order)}
                    </td>
                    <td className="px-4 py-3">
                      {getCustomerName(order)}
                    </td>
                    <td className="px-4 py-3">
                      {formatDate(order.createdAt)}
                    </td>
                    <td className="px-4 py-3">
                      {formatCurrency(getTaxAmount(order))}
                    </td>
                    <td className="px-4 py-3">
                      {formatCurrency(order.grandTotal || 0)}
                    </td>
                    <td className="px-4 py-3">
                      {formatCurrency(getReceivedAmount(order))}
                    </td>
                    <td className="px-4 py-3">
                      {formatCurrency(order.dueamount || 0)}
                    </td>
                    <td className="px-4 py-3">
                      {formatDate(order.orderId?.orderDate || order.createdAt)}
                    </td>
                    <td className="px-4 py-3">
                      <button
                        onClick={() => handleViewOrder(order)}
                        className="bg-[#FF6B35] text-white px-5 py-1.5 rounded-full text-sm font-medium cursor-pointer transition-colors hover:bg-[#E55A2B] flex items-center gap-1.5"
                      >
                        <IoEye size={16} className="text-white" />
                        View
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination info */}
        {filteredOrders.length > 0 && (
          <div className="text-sm text-gray-600 text-center">
            Showing {startIndex + 1} to {Math.min(endIndex, filteredOrders.length)} of {filteredOrders.length} orders
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {isOrderDetailsOpen && selectedOrder && (
        <RefundOrderDetailsModal
          isOpen={isOrderDetailsOpen}
          onClose={() => setIsOrderDetailsOpen(false)}
          order={selectedOrder}
        />
      )}
    </CustomModal>
  );
};

export default RefundableOrders;