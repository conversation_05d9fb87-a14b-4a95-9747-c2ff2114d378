import { useState } from 'react';
import { Search, Trash2, ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useGetEmailMarketingQuery, useDeleteEmailMarketingMutation } from "../../../store/api/emailMarketingApi";
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Define interfaces for the component
interface EmailMarketingItem {
  _id: string;
  subject: string;
  email: string[] | string;
  message: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export default function EmailMarketing() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [itemToDelete, setItemToDelete] = useState<string | null>(null);
  const itemsPerPage = 5; // Show 5 records per page
  
  const userId: string = localStorage.getItem('userId') || '';
  
  const {
    data: emailMarketing = [] as EmailMarketingItem[],
    isLoading,
    error,
  } = useGetEmailMarketingQuery(userId, {
    skip: !userId,
    refetchOnMountOrArgChange: true,
  });

  // Delete mutation hook
  const [deleteEmailMarketing, { isLoading: isDeleting }] = useDeleteEmailMarketingMutation();

  const navigate = useNavigate();

  // Handle search functionality - only search by email
  const filteredEmails = emailMarketing.filter((item: EmailMarketingItem) => {
    // Check if email is an array, a string, or undefined
    if (Array.isArray(item.email)) {
      return item.email.some((email: string) => 
        email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } else if (typeof item.email === 'string') {
      return item.email.toLowerCase().includes(searchTerm.toLowerCase());
    }
    return false;
  });

  // Calculate pagination
  const totalPages = Math.ceil(filteredEmails.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedEmails = filteredEmails.slice(startIndex, startIndex + itemsPerPage);

  // Handle page navigation
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Generate page numbers array for pagination display
  const getPageNumbers = (): number[] => {
    const pageNumbers: number[] = [];
    const maxPagesToShow = 5; // How many page numbers to show at once
    
    if (totalPages <= maxPagesToShow) {
      // Show all pages if total is less than maxPagesToShow
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Complex logic for showing current page with some neighbors
      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
      let endPage = startPage + maxPagesToShow - 1;
      
      if (endPage > totalPages) {
        endPage = totalPages;
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }
      
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }
    }
    
    return pageNumbers;
  };

  // Handle delete
  const handleDelete = (id: string) => {
    setItemToDelete(id);
    setShowModal(true);
  };

  // Confirm deletion
  const confirmDelete = async () => {
    if (itemToDelete) {
      try {
        await deleteEmailMarketing(itemToDelete).unwrap();
        toast.success('Email campaign deleted successfully!');
      } catch (err) {
        console.error("Failed to delete email marketing campaign:", err);
        toast.error('Failed to delete email campaign!');
      } finally {
        setShowModal(false);
        setItemToDelete(null);
      }
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen p-2 sm:p-3">
      {/* Delete Confirmation Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-50">
          <div className="bg-white p-8 rounded-xl shadow-xl w-full max-w-md text-center relative">
            <div className="flex justify-center mb-4">
              <div className="bg-orange-100 text-orange-500 p-4 rounded-full">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01M12 5c.512 0 1.023.195 1.414.586C13.805 6.977 14 7.488 14 8s-.195 1.023-.586 1.414A1.993 1.993 0 0112 10a1.993 1.993 0 01-1.414-.586A1.993 1.993 0 0110 8c0-.512.195-1.023.586-1.414A1.993 1.993 0 0112 5z"
                  />
                </svg>
              </div>
            </div>
            <h3 className="text-2xl font-semibold text-gray-800">
              Are you sure?
            </h3>
            <p className="mt-2 text-gray-600">
              Do you want to delete this email campaign?
            </p>
            <div className="mt-6 flex justify-center gap-4">
              <button
                onClick={confirmDelete}
                disabled={isDeleting}
                className="bg-orange-600 hover:bg-orange-700 text-white px-5 py-2 rounded-md font-medium"
              >
                {isDeleting ? 'Deleting...' : 'Yes, delete it!'}
              </button>
              <button
                onClick={() => setShowModal(false)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-5 py-2 rounded-md font-medium"
              >
                No, cancel!
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="p-2 flex flex-col sm:flex-row justify-between items-center gap-3 border border-gray-200 rounded-2xl bg-white">
        <h1 className="text-2xl sm:text-3xl p-2 sm:p-3 font-bold">Email Marketing</h1>
        
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <div className="relative w-full sm:w-64">
            <input 
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)} 
              placeholder="Search by Email" 
              className="w-full sm:w-64 pl-3 pr-10 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
            <button className="absolute right-3 top-2.5">
              <Search size={18} />
            </button>
          </div>
          
          <button 
            onClick={() => navigate('/admin/marketing/email-marketing/email-marketing-form')}
            className="bg-orange-500 text-white px-4 py-2 rounded-md flex items-center justify-center gap-2 w-full sm:w-auto"
          >
            Create Email Campaign
            <Plus size={16} />
          </button>
        </div>
      </div>

      {/* Loading and Error States */}
      {isLoading && (
        <div className="flex justify-center items-center p-8 bg-white rounded-2xl mt-3 border border-gray-200">
          <p className="text-gray-500">Loading email campaigns...</p>
        </div>
      )}

      {error && (
        <div className="flex justify-center items-center p-8 bg-white rounded-2xl mt-3 border border-gray-200">
          <p className="text-red-500">Error loading email campaigns. Please try again.</p>
        </div>
      )}

      {/* Table */}
      {!isLoading && !error && filteredEmails.length > 0 && (
        <div className="mt-3 overflow-x-auto rounded-2xl">
          <table className="min-w-full bg-white rounded-2xl shadow-sm">
            <thead className=''>
              <tr className="font-medium bg-orange-50 text-gray-700 ">
                <th className="p-3 pl-4 text-left w-16">No</th>
                <th className="p-3 text-left w-40">Subject</th>
                <th className="p-3 text-left w-64">Email</th>
                <th className="p-3 text-left w-64 pr-12">Message</th>
              </tr>
            </thead>
            <tbody>
              {paginatedEmails.map((item: EmailMarketingItem, index: number) => (
                <tr 
                  key={item._id} 
                  className="border-t border-gray-100 hover:bg-orange-50"
                >
                  <td className="p-3 pl-4">{startIndex + index + 1}</td>
                  <td className="p-3 break-words">{item.subject}</td>
                  <td className="p-3 break-words">
                    {Array.isArray(item.email) 
                      ? item.email.join(', ')
                      : item.email}
                  </td>
                  <td className="p-3 pr-3">
                    <div className="flex justify-between items-center">
                      <span className="break-words pr-2">{item.message}</span>
                      <button 
                        className="text-red-400 shrink-0"
                        onClick={() => handleDelete(item._id)}
                      >
                        <Trash2 size={22} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && !error && filteredEmails.length === 0 && (
        <div className="flex flex-col items-center justify-center p-8 bg-white rounded-2xl mt-3 border border-gray-200">
          <p className="text-gray-500 mb-4">No email campaigns found</p>
          <button 
            onClick={() => navigate('/admin/marketing/email-marketing/email-marketing-form')}
            className="bg-orange-500 text-white px-4 py-2 rounded-md flex items-center gap-2"
          >
            Create Email Campaign
            <Plus size={16} />
          </button>
        </div>
      )}

      {/* Pagination */}
      {!isLoading && !error && filteredEmails.length > 0 && (
        <div className="flex flex-col items-start gap-2 p-2 sm:p-4 mt-2">
          <div className="flex items-center gap-2">
            <button 
              className={`border border-gray-200 rounded-md p-1 ${currentPage === 1 ? 'text-gray-400' : 'hover:bg-gray-100'}`}
              onClick={goToPrevPage}
              disabled={currentPage === 1}
            >
              <ChevronLeft size={18} />
            </button>
            
            {/* Page number buttons */}
            {getPageNumbers().map(pageNum => (
              <button
                key={pageNum}
                onClick={() => goToPage(pageNum)}
                className={`border rounded-md w-8 h-8 flex items-center justify-center 
                  ${currentPage === pageNum ? 'bg-orange-500 text-white' : 'text-gray-600 hover:bg-gray-100'}`}
              >
                {pageNum}
              </button>
            ))}
            
            <button 
              className={`border border-gray-200 rounded-md p-1 ${currentPage === totalPages ? 'text-gray-400' : 'hover:bg-gray-100'}`}
              onClick={goToNextPage}
              disabled={currentPage === totalPages}
            >
              <ChevronRight size={18} />
            </button>
          </div>
          
          {/* Page info */}
          <div className="text-sm text-gray-500">
            Showing {filteredEmails.length > 0 ? startIndex + 1 : 0}-{Math.min(startIndex + itemsPerPage, filteredEmails.length)} of {filteredEmails.length} results
          </div>
        </div>
      )}
    </div>
  );
}