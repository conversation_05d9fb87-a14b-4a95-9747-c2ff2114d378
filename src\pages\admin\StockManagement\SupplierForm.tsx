import React, { useState, useEffect } from 'react';
import { ChevronLeft } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  usePostSupplierMutation,
  usePutSupplierMutation,
  useGetSupplierQuery,
  useGetSuppliersQuery,
} from '../../../store/api/supplierApi';
import Swal from 'sweetalert2';

interface SupplierFormData {
  SupplierID: string;
  SupplierName: string;
  EmailAddress: string;
  ContactNumber: string;
  BussinessAddress: string;
  TaxIdentification: string;
  ProductOffered: string[];
  OtherInformation: string;
  userId: string;
}

export default function SupplierForm() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const userId = localStorage.getItem("userId") || "";

  // RTK Query hooks
  const { data: supplier } = useGetSupplierQuery(id || "", { skip: !isEditMode });
  const { data: _ } = useGetSuppliersQuery(userId);
  const [postSupplier, { isLoading: isCreating }] = usePostSupplierMutation();
  const [putSupplier, { isLoading: isUpdating }] = usePutSupplierMutation();

  // Form state
  const [formData, setFormData] = useState<SupplierFormData>({
    SupplierID: "",
    SupplierName: "",
    EmailAddress: "",
    ContactNumber: "",
    BussinessAddress: "",
    TaxIdentification: "",
    ProductOffered: [],
    OtherInformation: "",
    userId: userId,
  });

  const [currentProduct, setCurrentProduct] = useState('');
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Populate form data if in edit mode
  useEffect(() => {
    if (isEditMode && supplier) {
      setFormData({
        SupplierID: supplier.SupplierID || '',
        SupplierName: supplier.SupplierName || '',
        EmailAddress: supplier.EmailAddress || '',
        ContactNumber: supplier.ContactNumber || '',
        BussinessAddress: supplier.BussinessAddress || '',
        TaxIdentification: supplier.TaxIdentification || '',
        ProductOffered: supplier.ProductOffered ? (typeof supplier.ProductOffered === "string" ? supplier.ProductOffered : "").split(",")
        : [],
        OtherInformation: supplier.OtherInformation || '',
        userId: userId
      });
    }
  }, [isEditMode, supplier, userId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    
    // Clear validation error for this field when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleProductKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && currentProduct.trim()) {
      e.preventDefault();
      setFormData({
        ...formData,
        ProductOffered: [...formData.ProductOffered, currentProduct.trim()]
      });
      setCurrentProduct('');
      
      // Clear validation error for products when user adds one
      if (validationErrors.ProductOffered) {
        setValidationErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.ProductOffered;
          return newErrors;
        });
      }
    }
  };

  const handleRemoveProduct = (index: number) => {
    const updatedProducts = [...formData.ProductOffered];
    updatedProducts.splice(index, 1);
    setFormData({
      ...formData,
      ProductOffered: updatedProducts
    });
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (!formData.SupplierName.trim()) {
      errors.SupplierName = 'Supplier name is required';
    }
    
    if (!formData.EmailAddress.trim()) {
      errors.EmailAddress = 'Email address is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.EmailAddress)) {
      errors.EmailAddress = 'Please enter a valid email address';
    }
    
    if (!formData.ContactNumber.trim()) {
      errors.ContactNumber = 'Contact number is required';
    }
    
    if (!formData.BussinessAddress.trim()) {
      errors.BussinessAddress = 'Business address is required';
    }
    
    if (!formData.TaxIdentification.trim()) {
      errors.TaxIdentification = 'Tax identification is required';
    }
    
    if (formData.ProductOffered.length === 0 && !currentProduct.trim()) {
      errors.ProductOffered = 'At least one product must be offered';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setValidationErrors({});

    if (!validateForm()) {
      await Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        html: `
          <div class="text-center">
            Please fix the following issues:
            <ul class="list-disc pl-5 mt-2 text-left">
              ${Object.values(validationErrors).map(error => `<li>${error}</li>`).join('')}
            </ul>
          </div>
        `,
        confirmButtonText: 'OK',
        confirmButtonColor: '#F97316',
      });
      return;
    }

    try {
      const submissionData = {
        ...formData,
        ProductOffered: formData.ProductOffered.join(","),
      };

      console.log("Submitting form data:", submissionData);

      if (isEditMode && id) {
        // Update existing supplier
        await putSupplier({
          id,
          formData: {
            ...submissionData,
            _id: id,
          }
        }).unwrap();
        
        await Swal.fire({
          title: 'Success!',
          text: 'Supplier has been updated successfully',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#F97316',
        });
      } else {
        // Create new supplier
        await postSupplier(submissionData).unwrap();
        
        await Swal.fire({
          title: 'Success!',
          text: 'Supplier has been created successfully',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#F97316',
        });
      }
      navigate('/admin/stock-management/supplier');
    } catch (error) {
      console.error(isEditMode ? 'Error updating supplier:' : 'Error creating supplier:', error);
      
      await Swal.fire({
        title: 'Error!',
        text: `Failed to ${isEditMode ? 'update' : 'create'} supplier. Please try again.`,
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#F97316',
      });
    }
  };

  const handleCancel = () => {
    navigate('/admin/stock-management/supplier');
  };

  const isLoading = isCreating || isUpdating;

  return (
    <div className="p-3 bg-gray-50">
      {/* Header with back button */}
      <div className="bg-white p-2 flex items-center border border-gray-200 rounded-2xl mb-4">
        <button 
          onClick={handleCancel}
          className="mr-2">
          <ChevronLeft size={20} />
        </button>
        <h1 className="text-lg font-semibold">{isEditMode ? 'Edit Supplier' : 'Add Supplier'}</h1>
      </div>

      {/* Form container */}
      <div className="bg-white border border-gray-200 rounded-xl mb-3">
        <div className="bg-orange-50 rounded-t-xl p-3 mb-4">
          <h2 className="text-sm font-medium">Supplier Details</h2>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 p-4">
            {/* Supplier ID - Only show in edit mode */}
            {isEditMode && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Supplier ID</label>
                <input
                  type="text"
                  name="SupplierID"
                  value={formData.SupplierID}
                  className="w-full p-2 border border-gray-300 rounded"
                  disabled
                />
              </div>
            )}

            {/* Supplier Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Supplier Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="SupplierName"
                value={formData.SupplierName}
                onChange={handleInputChange}
                placeholder="Enter Supplier Name"
                className={`w-full p-2 border rounded ${
                  validationErrors.SupplierName ? 'border-red-500' : 'border-gray-300'
                }`}
                required
              />
              {validationErrors.SupplierName && (
                <p className="text-red-500 text-xs mt-1">{validationErrors.SupplierName}</p>
              )}
            </div>

            {/* Email Address */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email Address <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                name="EmailAddress"
                value={formData.EmailAddress}
                onChange={handleInputChange}
                placeholder="Enter Email Address"
                className={`w-full p-2 border rounded ${
                  validationErrors.EmailAddress ? 'border-red-500' : 'border-gray-300'
                }`}
                required
              />
              {validationErrors.EmailAddress && (
                <p className="text-red-500 text-xs mt-1">{validationErrors.EmailAddress}</p>
              )}
            </div>

            {/* Contact Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Number <span className="text-red-500">*</span>
              </label>
              <input
                type="tel"
                name="ContactNumber"
                value={formData.ContactNumber}
                onChange={handleInputChange}
                placeholder="Enter Contact Number"
                className={`w-full p-2 border rounded ${
                  validationErrors.ContactNumber ? 'border-red-500' : 'border-gray-300'
                }`}
                required
              />
              {validationErrors.ContactNumber && (
                <p className="text-red-500 text-xs mt-1">{validationErrors.ContactNumber}</p>
              )}
            </div>

            {/* Business Address */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Business Address <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="BussinessAddress"
                value={formData.BussinessAddress}
                onChange={handleInputChange}
                placeholder="Enter Business Address"
                className={`w-full p-2 border rounded ${
                  validationErrors.BussinessAddress ? 'border-red-500' : 'border-gray-300'
                }`}
                required
              />
              {validationErrors.BussinessAddress && (
                <p className="text-red-500 text-xs mt-1">{validationErrors.BussinessAddress}</p>
              )}
            </div>

            {/* Tax Identification */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tax Identification <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="TaxIdentification"
                value={formData.TaxIdentification}
                onChange={handleInputChange}
                placeholder="Enter Tax Identification Number"
                className={`w-full p-2 border rounded ${
                  validationErrors.TaxIdentification ? 'border-red-500' : 'border-gray-300'
                }`}
                required
              />
              {validationErrors.TaxIdentification && (
                <p className="text-red-500 text-xs mt-1">{validationErrors.TaxIdentification}</p>
              )}
            </div>

            {/* Other Information */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Other Information (Optional)
              </label>
              <input
                type="text"
                name="OtherInformation"
                value={formData.OtherInformation}
                onChange={handleInputChange}
                placeholder="Enter Other Information"
                className="w-full p-2 border border-gray-300 rounded"
              />
            </div>

            {/* Products Offered */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Products Offered <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={currentProduct}
                onChange={(e) => setCurrentProduct(e.target.value)}
                onKeyDown={handleProductKeyDown}
                placeholder="Enter Offered Products Name and press Enter for (Multiple)"
                className={`w-full p-2 border rounded ${
                  validationErrors.ProductOffered ? 'border-red-500' : 'border-gray-300'
                }`}
                required={formData.ProductOffered.length === 0}
              />
              {validationErrors.ProductOffered && (
                <p className="text-red-500 text-xs mt-1">{validationErrors.ProductOffered}</p>
              )}
              {formData.ProductOffered.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-1">
                  {formData.ProductOffered.map((product, index) => (
                    <span 
                      key={index} 
                      className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center"
                    >
                      {product}
                      <button 
                        type="button"
                        onClick={() => handleRemoveProduct(index)}
                        className="ml-1 text-blue-800 hover:text-blue-900"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4 mt-6 p-4">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-200 rounded-md text-sm"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className={`px-6 py-2 rounded-md text-white text-sm ${
                isLoading ? 'bg-orange-300 cursor-not-allowed' : 'bg-orange-500 hover:bg-orange-600'
              }`}
            >
              {isLoading
                ? isEditMode ? 'Updating...' : 'Adding...'
                : isEditMode ? 'Update Supplier' : 'Add Supplier'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}