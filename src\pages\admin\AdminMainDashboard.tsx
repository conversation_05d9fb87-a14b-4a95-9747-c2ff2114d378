import { useState, useMemo } from "react";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

import { TrendingUp, ArrowRight, Calendar } from "lucide-react";
import { usePosOrderItemsQuery } from "../../store/api/orderItemApi";
import icon from "../../assets/Home/icon.png";
import { 
  useGetCustomersQuery, 
} from "../../store/api/customerApi"; 

type ExpiringItem = {
  name: string;
  remaining: number;
  percentage: number;
};

type MetricCardProps = {
  title: string;
  value: string | number;
  change: string;
  icon: React.ReactNode;
};

type Order = {
  createdAt: string;
  grandTotal: number;
};

type Customer = {
  FirstName: string;
  LastName: string;
  CustomerLoyalty?: {
    ActivateCard?: boolean;
    creditLimits?: number;
  };
};

const expiringItems: ExpiringItem[] = [
  { name: "<PERSON><PERSON><PERSON>", remaining: 30, percentage: 70 },
  { name: "<PERSON><PERSON><PERSON><PERSON>", remaining: 10, percentage: 50 },
  { name: "Tiramisu", remaining: 15, percentage: 30 },
  { name: "<PERSON><PERSON>uil<PERSON>", remaining: 7, percentage: 20 },
  { name: "Boeuf Bourguignon", remaining: 15, percentage: 35 },
];

const formatDate = (date: Date): string => {
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
};

// Helper to get the date range for the last 7 days including today
const getLastSevenDaysRange = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const sixDaysAgo = new Date(today);
  sixDaysAgo.setDate(today.getDate() - 6);
  
  return { today, sixDaysAgo };
};

export default function Dashboard() {
  const { today, sixDaysAgo } = getLastSevenDaysRange();
  
  const [salesDateRange, setSalesDateRange] = useState({
    startDate: today,
    endDate: today,
  });

  const { data: customersData } = useGetCustomersQuery();
  const activeGiftCardCount = (customersData as Customer[] | undefined)?.filter(
    customer => customer.CustomerLoyalty?.ActivateCard
  ).length || 0;

  // Process data to get top 4 customers by credit limit
  const topCustomers = (customersData as Customer[] | undefined)
    ?.filter(customer => customer.CustomerLoyalty?.creditLimits) 
    ?.sort((a, b) => (b.CustomerLoyalty?.creditLimits || 0) - (a.CustomerLoyalty?.creditLimits || 0)) 
    ?.slice(0, 4) 
    ?.map(customer => ({
      name: `${customer.FirstName} ${customer.LastName}`,
      creditLimit: customer.CustomerLoyalty?.creditLimits || 0
    })) || []; 
    
  const userId = localStorage.getItem("userId") || "";
  const { data: AllOrders } = usePosOrderItemsQuery(userId);
  const recordCount = AllOrders?.length || 0;

  const [customersDateRange, setCustomersDateRange] = useState({
    startDate: new Date(),
    endDate: new Date(),
  });

  const [showSalesCalendar, setShowSalesCalendar] = useState(false);
  const [showCustomersCalendar, setShowCustomersCalendar] = useState(false);

  const handleSalesStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newStartDate = new Date(e.target.value);
    setSalesDateRange({
      ...salesDateRange,
      startDate: newStartDate,
      // If end date is before new start date, update end date
      endDate: newStartDate > salesDateRange.endDate ? newStartDate : salesDateRange.endDate
    });
  };

  const handleSalesEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEndDate = new Date(e.target.value);
    setSalesDateRange({
      ...salesDateRange,
      endDate: newEndDate
    });
  };

  const handleCustomersStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newStartDate = new Date(e.target.value);
    setCustomersDateRange({
      ...customersDateRange,
      startDate: newStartDate,
      // If end date is before new start date, update end date
      endDate: newStartDate > customersDateRange.endDate ? newStartDate : customersDateRange.endDate
    });
  };

  const handleCustomersEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEndDate = new Date(e.target.value);
    setCustomersDateRange({
      ...customersDateRange,
      endDate: newEndDate
    });
  };

  // Calculate today's sales
  const todaySales = useMemo(() => {
    if (!AllOrders) return 0;
    
    const today = new Date().toISOString().split('T')[0];
    return (AllOrders as Order[])
      .filter((order) => order.createdAt.split('T')[0] === today)
      .reduce((sum: number, order) => sum + order.grandTotal, 0);
  }, [AllOrders]);

  // Calculate selected range sales
  const selectedRangeSales = useMemo(() => {
    if (!AllOrders || !salesDateRange.startDate || !salesDateRange.endDate) return 0;
    
    // If start and end dates are the same and equal to today, use todaySales
    const startIsToday = new Date(salesDateRange.startDate).toISOString().split('T')[0] === new Date().toISOString().split('T')[0];
    const endIsToday = new Date(salesDateRange.endDate).toISOString().split('T')[0] === new Date().toISOString().split('T')[0];
    
    if (startIsToday && endIsToday) {
      return todaySales;
    }
    
    const startDate = new Date(salesDateRange.startDate);
    startDate.setHours(0, 0, 0, 0);
    
    const endDate = new Date(salesDateRange.endDate);
    endDate.setHours(23, 59, 59, 999); // End of day
    
    return (AllOrders as Order[])
      .filter((order) => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= startDate && orderDate <= endDate;
      })
      .reduce((sum: number, order) => sum + order.grandTotal, 0);
  }, [AllOrders, salesDateRange, todaySales]);

  // Calculate weekly sales
  const weeklySales = useMemo(() => {
    if (!AllOrders) return 0;
  
    const { today, sixDaysAgo } = getLastSevenDaysRange();
  
    return (AllOrders as Order[])
      .filter((order) => {
        const orderDate = new Date(order.createdAt);
        orderDate.setHours(0, 0, 0, 0); // Normalize for comparison
        return orderDate >= sixDaysAgo && orderDate <= today;
      })
      .reduce((sum: number, order) => sum + order.grandTotal, 0);
  }, [AllOrders]);
  
  // Generate weekly sales data for the chart
  const dynamicSalesData = useMemo(() => {
    if (!AllOrders) return [];
  
    // Generate labels for today and the previous 6 days
    const today = new Date();
    today.setHours(0, 0, 0, 0);
  
    const days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' }); // e.g., "Mon"
      return { date, dayName, value: 0 };
    }).reverse(); // Reverse to show days in chronological order
  
    (AllOrders as Order[]).forEach((order) => {
      const orderDate = new Date(order.createdAt);
      orderDate.setHours(0, 0, 0, 0);
  
      days.forEach(day => {
        if (orderDate.getTime() === day.date.getTime()) {
          day.value += order.grandTotal;
        }
      });
    });
  
    // Return only dayName and value
    return days.map(({ dayName, value }) => ({ day: dayName, value }));
  }, [AllOrders]);
  
  // Format date for display in the input field
  const formatDateForInput = (date: Date) => {
    if (!date) return "";
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, "0");
    const day = String(d.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };
  
  // Format date range for display
  const selectedDateRangeFormatted = useMemo(() => {
    if (!salesDateRange.startDate || !salesDateRange.endDate) {
      return "Select date range";
    }
    
    const startDate = salesDateRange.startDate;
    const endDate = salesDateRange.endDate;
    
    // Check if the selection is today
    const isToday = new Date(startDate).toISOString().split('T')[0] === new Date().toISOString().split('T')[0] && 
                   new Date(endDate).toISOString().split('T')[0] === new Date().toISOString().split('T')[0];
    
    if (isToday) {
      return "Today";
    }
    
    if (startDate.getTime() === endDate.getTime()) {
      // If same day, just display one date
      return new Date(startDate).toLocaleDateString("en-US", { 
        weekday: "long",
        month: "long",
        day: "numeric"
      });
    } 
    
    // Format for date range
    return `${new Date(startDate).toLocaleDateString("en-US", { 
      weekday: "short",
      month: "short",
      day: "numeric"
    })} - ${new Date(endDate).toLocaleDateString("en-US", { 
      weekday: "short",
      month: "short",
      day: "numeric"
    })}`;
  }, [salesDateRange]);

  // Handle applying the date range
  const applyDateRange = () => {
    setShowSalesCalendar(false);
  };

  // Handle canceling the date selection and reset to today
  const cancelDateSelection = () => {
    setSalesDateRange({
      startDate: today,
      endDate: today
    });
    setShowSalesCalendar(false);
  };

  return (
    <div className="p-6 bg-gray-50">
      <h1 className="text-2xl border border-gray-200 p-3 font-bold rounded-2xl bg-white mb-6">
        Dashboard
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3">
        <MetricCard
          title="Total Orders"
          value={recordCount}
          change="+11.01%"
          icon={<TrendingUp size={16} />}
        />
        <MetricCard
          title="Order Returned"
          value="0"
          change="+18.01%"
          icon={<TrendingUp size={16} />}
        />
        <MetricCard
          title="Employees Salaries"
          value="18K"
          change="+33.01%"
          icon={<TrendingUp size={16} />}
        />
        <MetricCard
          title="Loyal Customers"
          value={activeGiftCardCount}
          change="+29.01%"
          icon={<TrendingUp size={16} />}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-2">
        <div className="bg-white shadow border border-gray-200 rounded-2xl">
          <div className="flex md:flex-row flex-col justify-between items-center mb-4 p-3 rounded-t-2xl border-b border-b-gray-200 bg-orange-50">
            <h2 className="text-lg font-semibold">Sales Performance</h2>
            <div className="relative">
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  placeholder="Start date — End date"
                  className="border border-gray-200 rounded px-3 py-2 text-sm w-48"
                  value={selectedDateRangeFormatted}
                  readOnly
                  onClick={() => setShowSalesCalendar(!showSalesCalendar)}
                />
                <button
                  className="p-2 bg-gray-100 rounded"
                  onClick={() => setShowSalesCalendar(!showSalesCalendar)}
                >
                  <Calendar size={16} />
                </button>
              </div>

              {showSalesCalendar && (
                <div className="absolute z-10 mt-1 right-0 bg-white shadow-lg rounded-lg border border-gray-200">
                  <div className="p-4">
                    <h3 className="text-lg font-medium mb-2">Date Range</h3>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700">Start Date</label>
                      <input 
                        type="date" 
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                        value={formatDateForInput(salesDateRange.startDate)}
                        onChange={handleSalesStartDateChange}
                        min={formatDateForInput(sixDaysAgo)}
                        max={formatDateForInput(today)}
                      />
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700">End Date</label>
                      <input 
                        type="date" 
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                        value={formatDateForInput(salesDateRange.endDate)}
                        onChange={handleSalesEndDateChange}
                        min={formatDateForInput(salesDateRange.startDate)}
                        max={formatDateForInput(today)}
                      />
                    </div>
                    <div className="flex space-x-2">
                      <button
                        className="w-full bg-blue-500 text-white px-4 py-2 rounded"
                        onClick={applyDateRange}
                      >
                        Apply
                      </button>
                      <button
                        className="w-full border border-gray-300 px-4 py-2 rounded"
                        onClick={cancelDateSelection}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 p-4 mb-4">
            <div className="flex md:flex-row flex-col justify-between border border-gray-200 rounded p-3">
              <div>
                <div className="text-sm text-gray-600">
  {salesDateRange.startDate.toDateString() === new Date().toDateString() && 
   salesDateRange.endDate.toDateString() === new Date().toDateString() 
   ? "Today's Sales:" 
   : "Selected Range Sales:"}
</div>
                <div className="text-sm text-gray-600">
                  {selectedDateRangeFormatted}
                </div>
              </div>
              <div className="text-xl font-bold">${selectedRangeSales.toFixed(2)}</div>
            </div>

            <div className="flex md:flex-row flex-col justify-between border border-gray-200 rounded p-3">
              <div>
                <div className="text-sm text-gray-600">Sales this Week:</div>
                <div className="text-sm text-gray-600">
                  {new Date().toLocaleDateString("en-US", { month: "long" })}
                </div>
              </div>
              <div className="text-xl font-bold">${weeklySales.toFixed(2)}</div>
            </div>
          </div>

          <div className="h-80 px-4 pb-4">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                 data={dynamicSalesData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <defs>
                  <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#FCA5A5" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#FCA5A5" stopOpacity={0.1} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f5f5f5" />
                <XAxis 
                  dataKey="day" 
                  axisLine={false} 
                  tickLine={false} 
                  tick={{ fill: '#666', fontSize: 12 }}
                />
                <YAxis 
                  axisLine={false} 
                  tickLine={false}
                  domain={[0, 4800]}
                  ticks={[0, 300, 600, 900, 1200, 1500, 1800, 2100, 2400, 2700, 3000, 3300, 3600, 3900, 4200, 4500, 4800]}
                  tickFormatter={(value) => `$${value}`}
                  tick={{ fill: '#666', fontSize: 12 }}
                />
                <Tooltip 
                  formatter={(value: number) => [`$${value.toFixed(2)}`, "Sales"]}
                  labelFormatter={(label) => `Day: ${label}`}
                  contentStyle={{ backgroundColor: 'white', borderRadius: '4px', boxShadow: '0 2px 5px rgba(0,0,0,0.1)' }}
                />
                <Area 
                  type="monotone" 
                  dataKey="value" 
                  stroke="#FCA5A5" 
                  strokeWidth={2}
                  fillOpacity={1} 
                  fill="url(#colorValue)" 
                  activeDot={{ r: 6, stroke: "#FCA5A5", strokeWidth: 2, fill: "white" }}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="flex flex-col">
          <div className="bg-white rounded-2xl shadow mb-6">
            <div className="flex justify-between md:flex-row flex-col items-center mb-4 p-3 rounded-t-2xl border-b border-b-gray-200 bg-orange-50">
              <h2 className="text-lg font-semibold">
                Spend by Loyal Customers
              </h2>
              <div className="relative">
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    placeholder="Start date — End date"
                    className="border border-gray-200 rounded px-3 py-2 text-sm w-48"
                    value={`${formatDate(customersDateRange.startDate)} — ${formatDate(customersDateRange.endDate)}`}
                    readOnly
                    onClick={() => setShowCustomersCalendar(!showCustomersCalendar)}
                  />
                  <button
                    className="p-2 bg-gray-100 rounded"
                    onClick={() => setShowCustomersCalendar(!showCustomersCalendar)}
                  >
                    <Calendar size={16} />
                  </button>
                </div>

                {showCustomersCalendar && (
                  <div className="absolute z-10 mt-1 right-0 bg-white shadow-lg rounded-lg border border-gray-200">
                    <div className="p-4">
                      <h3 className="text-lg font-medium mb-2">Date Range</h3>
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700">Start Date</label>
                        <input 
                          type="date" 
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                          value={formatDateForInput(customersDateRange.startDate)}
                          onChange={handleCustomersStartDateChange}
                        />
                      </div>
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700">End Date</label>
                        <input 
                          type="date" 
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                          value={formatDateForInput(customersDateRange.endDate)}
                          onChange={handleCustomersEndDateChange}
                          min={formatDateForInput(customersDateRange.startDate)}
                        />
                      </div>
                      <div className="flex space-x-2">
                        <button
                          className="w-full bg-blue-500 text-white px-4 py-2 rounded"
                          onClick={() => setShowCustomersCalendar(false)}
                        >
                          Apply
                        </button>
                        <button
                          className="w-full border border-gray-300 px-4 py-2 rounded"
                          onClick={() => {
                            setCustomersDateRange({
                              startDate: new Date(),
                              endDate: new Date()
                            });
                            setShowCustomersCalendar(false);
                          }}
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 p-4">
              {topCustomers.map((customer, index) => (
                <div key={index} className="border border-gray-200 rounded p-3">
                  <div className="text-sm text-gray-600 mb-2">
                    {customer.name}
                  </div>
                  <div className="text-lg font-bold text-end">
                    ${customer.creditLimit.toFixed(2)}
                  </div>
                </div>
              ))}
              {/* Add empty placeholders if we have fewer than 4 customers */}
              {topCustomers.length < 4 && Array(4 - topCustomers.length).fill(0).map((_, index) => (
                <div key={`empty-${index}`} className="border border-gray-200 rounded p-3">
                  <div className="text-sm text-gray-600 mb-2">
                    —
                  </div>
                  <div className="text-lg font-bold text-end">
                    $0.00
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow">
            <div className="flex justify-between items-center mb-4 p-3 rounded-t-2xl border-b border-b-gray-200 bg-orange-50">
              <h2 className="text-lg font-semibold">Items Expiring Soon</h2>
              <button className="text-blue-500 text-sm flex items-center">
                View <ArrowRight size={16} className="ml-1" />
              </button>
            </div>

            <div className="space-y-3 p-3">
              {expiringItems.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="w-24 text-sm">{item.name}</div>
                  <div className="flex-1 mx-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${getProgressColor(
                          item.percentage
                        )}`}
                        style={{ width: `${item.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 w-28 text-right">
                    {item.remaining} Quantities Left
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function MetricCard({ title, value, change }: MetricCardProps) {
  const isPositive = change.startsWith("+");

  return (
    <div className="bg-white rounded-lg shadow p-3">
      <div className="flex justify-between mb-2">
        <div className="flex items-center text-gray-600">
          <img src={icon} alt="icon" className="h-6 w-6" />
          <span className="ml-2 text-lg font-semibold">{title}</span>
        </div>
        <button>
          <ArrowRight
            size={20}
            style={{ transform: "rotate(310deg)", color: "gray" }}
          />
        </button>
      </div>
      <div className="flex justify-between">
        <div className="text-4xl font-bold mb-1">{value}</div>
        <div className="flex flex-col items-end justify-end text-xs">
          <span className={`${isPositive ? "text-green-500" : "text-red-500"}`}>
            {change}
          </span>
          <span className="text-gray-500 ml-1">from last month</span>
        </div>
      </div>
    </div>
  );
}

function getProgressColor(percentage: number): string {
  if (percentage > 60) return "bg-green-500";
  if (percentage > 40) return "bg-yellow-500";
  return "bg-red-500";
}