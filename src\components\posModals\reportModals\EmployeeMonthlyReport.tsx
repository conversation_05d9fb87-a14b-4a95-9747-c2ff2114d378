import React, { useEffect, useRef, useState } from "react";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format, parse, differenceInSeconds } from "date-fns";
import {
    FiSearch,
    FiEye,
    FiPrinter,
    FiChevronDown,
    FiChevronUp,
    FiChevronRight,
    FiChevronLeft,
} from "react-icons/fi";
// import { useGetEmployeeTimeQuery } from "../../../store/api/pos/customer";
import CustomModal from "../../CustomModal";
import { Tooltip } from "react-tooltip";
import PaySlip from "./PaySlip";
import DeductionDetailsModal from "./DeductionDetailsModal";
import { useGetEmployeesQuery, type EmployeeRecord } from "../../../store/api/pos/customer";

interface EmployeeMonthlyReportProps {
    isOpen: boolean;
    onClose: () => void;
    reportType?: string;
    filteredData?: EmployeeRecord[];
}

const EmployeeMonthlyReport: React.FC<EmployeeMonthlyReportProps> = ({
    isOpen,
    onClose,
    reportType: initialReportType = "Monthly",
    filteredData,
}) => {
    const userId = localStorage.getItem("userId") || "";
    const { data, isLoading, error } = useGetEmployeesQuery(userId);

    const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
        new Date(),
        new Date(),
    ]);
    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const [records, setRecords] = useState<EmployeeRecord[]>([]);
    const calendarRef = useRef<HTMLDivElement>(null);
    const [startDate, endDate] = dateRange;
    const [currentPage, setCurrentPage] = useState(1);
    const [reportType, setReportType] = useState<string>(initialReportType);
    const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
    const [isPaySlipOpen, setIsPaySlipOpen] = useState(false);
    const [isDeductionModalOpen, setIsDeductionModalOpen] = useState(false);
    const [selectedEmployee, setSelectedEmployee] = useState<EmployeeRecord | null>(
        null
    );
    const recordsPerPage = 5;

    useEffect(() => {
        if (filteredData && filteredData.length > 0) {
            setRecords(filteredData);
        } else if (data) {
            const transformedData = data.map((employee: any) => {
                const hours = Math.floor(employee.totalHours || 0);
                const minutes = Math.floor(((employee.totalHours % 1) * 60) % 60) || 0;
                const seconds = Math.floor((((employee.totalHours % 1) * 60) % 1) * 60) || 0;

                const startTime = parse(
                    employee.employeeStartTime,
                    "HH:mm",
                    new Date()
                );
                const endTime = parse(employee.employeeEndTime, "HH:mm", new Date());
                let diff = differenceInSeconds(endTime, startTime);
                const workHours = Math.floor(diff / 3600);
                diff %= 3600;
                const workMinutes = Math.floor(diff / 60);
                const workSeconds = diff % 60;
                const totalWork = `${workHours} hr, ${workMinutes} min, ${workSeconds} sec`;

                const shiftTotalAmount =
                    (employee.hourlyRate || 0) * (employee.totalHours || 0);
                const subtotal = parseFloat(shiftTotalAmount.toFixed(2));

                const totalDeductions = employee.deductions?.reduce(
                    (sum: number, deduction: any) =>
                        sum + (subtotal * deduction.amount) / 100,
                    0
                );

                const monthNames = [
                    "January",
                    "February",
                    "March",
                    "April",
                    "May",
                    "June",
                    "July",
                    "August",
                    "September",
                    "October",
                    "November",
                    "December",
                ];
                const date = new Date(employee.startDate);
                const month = monthNames[date.getMonth()] + " " + date.getFullYear();

                return {
                    ...employee,
                    empName: `${employee.firstName} ${employee.lastName}`,
                    shiftHours: `${hours} hr, ${minutes} mm, ${seconds}ss`,
                    shiftTotalAmount: parseFloat(shiftTotalAmount.toFixed(2)),
                    month: month,
                    startHourlyRate: employee.hourlyRate,
                    total: parseFloat((shiftTotalAmount - totalDeductions).toFixed(2)),
                    overtimeHours: "0 hr, 0 mm, 0ss",
                    overtimeHourlyRate: employee.overTimeRate || 0,
                    overtimeTotalAmount: 0,
                    totalWork: totalWork,
                    subtotal: subtotal,
                    deductionAmount: totalDeductions,
                };
            });

            setRecords(transformedData);
        } else {
            setRecords([
                {
                    _id: "6789fd328b72dad9d983017f",
                    userId: "65d6e2acf4cb2c368afded71",
                    employeeId: "223424",
                    firstName: "Jon",
                    lastName: "Doe",
                    empName: "Jon Doe",
                    address: "1600 Fake Street",
                    email: "<EMAIL>",
                    phoneNo: "6019521325",
                    employeeType: "cashier",
                    role: "employee",
                    hourlyRate: 7,
                    overTimeRate: 2,
                    employeeStartTime: "09:00",
                    employeeEndTime: "17:00",
                    startDate: "2025-01-17T06:48:18.807Z",
                    totalHours: 8,
                    deductions: [
                        { amount: 5, name: "Fined", _id: "680b0d0be8f0dd08453c9d05" },
                        { amount: 1.5, name: "Tax", _id: "680b0d0be8f0dd08453c9d06" },
                    ],
                    allowancesHistory: [],
                    bonusesHistory: [],
                    createdAt: "2025-01-17T06:48:18.813Z",
                    updatedAt: "2025-04-25T04:18:19.932Z",
                    __v: 0,
                    shiftHours: "8 hr, 0 mm, 0ss",
                    shiftTotalAmount: 56.0,
                    month: "January 2025",
                    startHourlyRate: 7,
                    total: 52.36,
                    overtimeHours: "0 hr, 0 mm, 0ss",
                    overtimeHourlyRate: 2,
                    overtimeTotalAmount: 0,
                    totalWork: "8 hr, 0 mm, 0ss",
                    subtotal: 56.0,
                    deductionAmount: 3.64,
                },
            ]);
        }
    }, [data, filteredData]);

    const filteredRecords = records.filter((record) =>
        record.empName.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);

    const getCurrentRecords = () => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        return filteredRecords.slice(startIndex, endIndex);
    };

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                calendarRef.current &&
                !calendarRef.current.contains(event.target as Node)
            ) {
                setIsCalendarOpen(false);
            }
        };
        if (isCalendarOpen) {
            document.addEventListener("mousedown", handleClickOutside);
            return () =>
                document.removeEventListener("mousedown", handleClickOutside);
        }
    }, [isCalendarOpen]);

    const handleDateRangeChange = (ranges: RangeKeyDict) => {
        const selection = ranges.selection;
        if (selection) {
            setDateRange([selection.startDate || null, selection.endDate || null]);
        }
    };

    const formatDateRange = () => {
        if (!startDate || !endDate) return "Select Date Range";
        return `${format(startDate, "MMM dd, yyyy")} → ${format(endDate, "MMM dd, yyyy")}`;
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };

    const handleSearch = () => {
        console.log("Searching for:", searchTerm);
    };

    useEffect(() => {
        if (initialReportType) {
            setReportType(initialReportType);
        }
    }, [initialReportType]);

    const handleNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const handlePrevPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    const toggleRowExpand = (recordId: string) => {
        setExpandedRows((prevExpandedRows) => {
            const newExpandedRows = new Set(prevExpandedRows);
            if (newExpandedRows.has(recordId)) {
                newExpandedRows.delete(recordId);
            } else {
                newExpandedRows.add(recordId);
            }
            return newExpandedRows;
        });
    };

    const handlePrint = () => {
        window.print();
    };

    const handleView = (record: EmployeeRecord) => {
        setSelectedEmployee(record);
        setIsPaySlipOpen(true);
    };

    if (!isOpen) return null;

    return (
        <>
            {isPaySlipOpen && selectedEmployee && (
                <PaySlip
                    isOpen={isPaySlipOpen}
                    onClose={() => setIsPaySlipOpen(false)}
                    employeeData={selectedEmployee}
                />
            )}
            {isDeductionModalOpen && selectedEmployee && (
                <DeductionDetailsModal
                    isOpen={isDeductionModalOpen}
                    onClose={() => setIsDeductionModalOpen(false)}
                    employeeData={selectedEmployee}
                />
            )}

            <CustomModal
                isOpen={isOpen}
                onClose={onClose}
                title={`Employees ${reportType} Report`}
                width="max-w-6xl"
                zIndex={1000}
                footer={
                    <div className="flex justify-between items-center px-6 py-4 border-t border-gray-200">
                        <div className="flex items-center gap-2 border border-gray-300 rounded-full overflow-hidden shadow-sm">
                            <button
                                className={`px-4 py-2 text-sm font-medium ${currentPage === 1
                                    ? "text-gray-400 cursor-not-allowed"
                                    : "text-gray-500 hover:text-gray-700 hover:bg-gray-50 cursor-pointer"
                                    }`}
                                onClick={handlePrevPage}
                                disabled={currentPage === 1}
                                type="button"
                            >
                                <span className="flex items-center">
                                    <FiChevronLeft className="mr-1" /> Previous
                                </span>
                            </button>
                            <span className="px-4 py-2 text-sm font-medium text-black border-x border-gray-300 bg-gray-50">
                                {currentPage}
                            </span>
                            <button
                                className={`px-4 py-2 text-sm font-medium ${currentPage >= totalPages
                                    ? "text-gray-400 cursor-not-allowed"
                                    : "text-gray-500 hover:text-gray-700 hover:bg-gray-50 cursor-pointer"
                                    }`}
                                onClick={handleNextPage}
                                disabled={currentPage >= totalPages}
                                type="button"
                            >
                                <span className="flex items-center">
                                    Next <FiChevronRight className="ml-1" />
                                </span>
                            </button>
                        </div>
                        <div className="flex gap-4">
                            <button
                                onClick={handlePrint}
                                className="px-8 py-2 bg-black text-white rounded-full flex items-center gap-2 hover:bg-gray-800 transition-colors shadow-sm"
                                type="button"
                            >
                                Print <FiPrinter size={18} />
                            </button>
                            <button
                                onClick={onClose}
                                className="px-12 py-2 border border-orange-500 text-orange-500 font-medium rounded-full hover:bg-orange-50 transition-colors shadow-sm"
                                type="button"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                }
            >
                <div className="p-6">
                    <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                        <div className="flex items-center w-1/3 relative">
                            <FiSearch className="text-gray-400 absolute left-3" size={20} />
                            <input
                                type="text"
                                placeholder="Search Employee"
                                className="w-full pl-10 py-2 pr-4 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-orange-200 shadow-sm transition-shadow"
                                value={searchTerm}
                                onChange={handleSearchChange}
                                onKeyDown={(e) => {
                                    if (e.key === "Enter") {
                                        handleSearch();
                                    }
                                }}
                            />
                        </div>
                        <div className="flex items-center gap-4">
                            <div className="relative">
                                <button
                                    onClick={() => setIsCalendarOpen(!isCalendarOpen)}
                                    className="border border-gray-200 rounded-full py-2 px-4 flex items-center gap-2 hover:bg-gray-50 shadow-sm transition-all hover:shadow"
                                    type="button"
                                >
                                    {formatDateRange()}
                                </button>
                                {isCalendarOpen && (
                                    <div
                                        ref={calendarRef}
                                        className="absolute right-0 top-12 z-50 bg-white shadow-xl rounded-lg border border-gray-200"
                                    >
                                        <DateRangePicker
                                            ranges={[
                                                {
                                                    startDate: startDate || new Date(),
                                                    endDate: endDate || new Date(),
                                                    key: "selection",
                                                },
                                            ]}
                                            onChange={handleDateRangeChange}
                                            direction="horizontal"
                                            moveRangeOnFirstSelection={false}
                                        />
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="rounded-t-lg bg-gray-50 shadow-sm">
                        <div className="grid grid-cols-7 px-6 py-3 text-sm font-medium text-gray-500">
                            <div>Employee Name</div>
                            <div>Month</div>
                            <div>Shift Hours</div>
                            <div>Start Hourly Rate</div>
                            <div>Shift Total Amount</div>
                            <div>Total</div>
                            <div>Deductions</div>
                        </div>
                    </div>

                    <div className="bg-white rounded-b-lg shadow-sm">
                        {isLoading ? (
                            <div className="flex justify-center py-8">
                                <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
                            </div>
                        ) : error ? (
                            <div className="text-center py-4 text-red-500">
                                Error loading employee data
                            </div>
                        ) : filteredRecords.length === 0 ? (
                            <div className="text-center py-8 text-gray-500">No records found</div>
                        ) : (
                            getCurrentRecords().map((record, index) => (
                                <div
                                    key={record._id}
                                    className={`border-b ${index === getCurrentRecords().length - 1
                                        ? ""
                                        : "border-gray-100"
                                        } hover:bg-gray-50 transition-colors`}
                                >
                                    <div className="grid grid-cols-7 px-6 py-4 text-sm items-center">
                                        <div className="text-gray-800 font-medium">
                                            {record.empName}
                                        </div>
                                        <div className="text-gray-800">{record.month}</div>
                                        <div className="text-gray-800">{record.shiftHours}</div>
                                        <div className="text-gray-800">
                                            ${record.startHourlyRate?.toFixed(2)}
                                        </div>
                                        <div className="text-gray-800">
                                            ${record.shiftTotalAmount?.toFixed(2)}
                                        </div>
                                        <div className="text-gray-800 font-medium">
                                            ${record.total?.toFixed(2)}
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <div className="flex items-center gap-3">
                                                <button
                                                    onClick={() => {
                                                        setSelectedEmployee(record);
                                                        setIsDeductionModalOpen(true);
                                                    }}
                                                    className="text-blue-500 hover:text-blue-600 transition-colors flex items-center mr-7 ml-2 gap-1"
                                                >
                                                    View
                                                </button>
                                                <button
                                                    onClick={() => handleView(record)}
                                                    className="text-blue-500 hover:text-blue-600 transition-colors flex items-center gap-1"
                                                >
                                                    <FiEye size={18} />
                                                </button>
                                                <button
                                                    id={`print-btn-${record._id}`}
                                                    onClick={handlePrint}
                                                    className="text-gray-500 hover:text-gray-700 transition-colors"
                                                    data-tooltip-id="action-tooltip"
                                                    data-tooltip-content="Print"
                                                >
                                                    <FiPrinter size={18} />
                                                </button>
                                                <button
                                                    onClick={() => toggleRowExpand(record._id)}
                                                    className="text-gray-500 hover:text-gray-700 transition-colors ml-1 p-1 hover:bg-gray-100 rounded-full"
                                                    data-tooltip-id="action-tooltip"
                                                    data-tooltip-content={expandedRows.has(record._id)
                                                        ? "Collapse"
                                                        : "Expand"}
                                                >
                                                    {expandedRows.has(record._id) ? (
                                                        <FiChevronUp size={20} />
                                                    ) : (
                                                        <FiChevronDown size={20} />
                                                    )}
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    {expandedRows.has(record._id) && (
                                        <div className="bg-gray-50 px-6 py-4 border-b border-gray-100 rounded-lg mx-4 mb-4 shadow-inner">
                                            <div className="grid grid-cols-2 gap-8">
                                                <div>
                                                    <div className="mb-4">
                                                        <div className="text-gray-500 text-sm mb-1 font-medium">
                                                            Overtimes Hours
                                                        </div>
                                                        <div className="text-gray-800 bg-white p-2 rounded-md shadow-sm">
                                                            {record.overtimeHours}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div className="text-gray-500 text-sm mb-1 font-medium">
                                                            Subtotal
                                                        </div>
                                                        <div className="text-gray-800 bg-white p-2 rounded-md shadow-sm">
                                                            ${record.subtotal?.toFixed(2)}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div className="mb-4">
                                                        <div className="text-gray-500 text-sm mb-1 font-medium">
                                                            Overtimes Hourly Rate
                                                        </div>
                                                        <div className="text-gray-800 bg-white p-2 rounded-md shadow-sm">
                                                            ${record.overtimeHourlyRate?.toFixed(2)}
                                                        </div>
                                                    </div>
                                                    <div className="mb-4">
                                                        <div className="text-gray-500 text-sm mb-1 font-medium">
                                                            Overtimes Total Amount
                                                        </div>
                                                        <div className="text-gray-800 bg-white p-2 rounded-md shadow-sm">
                                                            ${record.overtimeTotalAmount?.toFixed(2)}
                                                        </div>
                                                    </div>
                                                    <div className="mb-4">
                                                        <div className="text-gray-500 text-sm mb-1 font-medium">
                                                            Total Work
                                                        </div>
                                                        <div className="text-gray-800 bg-white p-2 rounded-md shadow-sm">
                                                            {record.totalWork}
                                                        </div>
                                                    </div>
                                                    <div className="mb-4">
                                                        <div className="text-gray-500 text-sm mb-1 font-medium">
                                                            Deductions
                                                        </div>
                                                        <div className="text-gray-800 bg-white p-2 rounded-md shadow-sm">
                                                            {record.deductions?.map((deduction, idx) => (
                                                                <div key={idx}>
                                                                    {deduction.name}: $
                                                                    {(record.subtotal! * deduction.amount / 100).toFixed(2)}
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ))
                        )}
                    </div>

                    {!isLoading && !error && filteredRecords.length > 0 && (
                        <div className="text-right text-sm text-gray-500 mt-3 px-6">
                            Showing{" "}
                            {Math.min(
                                (currentPage - 1) * recordsPerPage + 1,
                                filteredRecords.length
                            )}{" "}
                            to{" "}
                            {Math.min(
                                currentPage * recordsPerPage,
                                filteredRecords.length
                            )}{" "}
                            of {filteredRecords.length} records
                        </div>
                    )}
                    <Tooltip id="action-tooltip" className="z-50" />
                </div>
            </CustomModal>
        </>
    );
};

export default EmployeeMonthlyReport;