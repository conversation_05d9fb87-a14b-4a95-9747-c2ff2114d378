import React, { useState, useEffect } from 'react';
import Navbar from '../../components/admin/AdminNavbar';
import Sidebar from '../../components/admin/AdminSidebar';
import { Outlet } from 'react-router-dom';

const AdminLayout: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isLargeScreen, setIsLargeScreen] = useState(window.innerWidth >= 1024); 

  const toggleSidebar = () => {
    setIsSidebarOpen(prev => !prev);
  };

  useEffect(() => {
    const handleResize = () => {
      const isLg = window.innerWidth >= 1024;
      setIsLargeScreen(isLg);
      setIsSidebarOpen(isLg); 
    };

    handleResize(); 
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      <Navbar toggleSidebar={toggleSidebar} />
      <div className="flex flex-1 overflow-hidden">
       
        <div
          className={`
            ${isSidebarOpen ? 'w-[290px]' : 'w-0'}
            ${isSidebarOpen && !isLargeScreen ? 'absolute lg:top-12 top-20 border-t border-t-gray-200 left-0 z-50 bg-white shadow-lg' : 'lg:static'}
            transition-all duration-300 ease-in-out
            custom-scrollbar overflow-y-auto
            h-full
          `}
        >
          <Sidebar />
        </div>
        {isSidebarOpen && !isLargeScreen && (
          <div
            className="fixed inset-0 bg-black opacity-50 z-40"
            onClick={toggleSidebar}
          ></div>
        )}

        {/* Main content */}
        <main className="flex-1 overflow-y-auto scrollbar-hide bg-gray-50">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
