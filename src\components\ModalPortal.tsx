import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface ModalPortalProps {
  children: React.ReactNode;
}

const ModalPortal: React.FC<ModalPortalProps> = ({ children }) => {
  const [portalRoot, setPortalRoot] = useState<HTMLElement | null>(null);

  useEffect(() => {
    // Check if portal root exists, create it if not
    let root = document.getElementById('modal-portal-root');
    if (!root) {
      root = document.createElement('div');
      root.id = 'modal-portal-root';
      root.style.position = 'fixed';
      root.style.top = '0';
      root.style.left = '0';
      root.style.width = '100%';
      root.style.height = '100%';
      root.style.zIndex = '99999'; // Extremely high z-index to ensure it's always on top
      root.style.pointerEvents = 'auto'; // Allow clicks on the portal content
      document.body.appendChild(root);
    }

    setPortalRoot(root);

    // Cleanup
    return () => {
      if (root && root.childNodes.length === 0) {
        document.body.removeChild(root);
      }
    };
  }, []);

  if (!portalRoot) return null;

  return createPortal(children, portalRoot);
};

export default ModalPortal;
