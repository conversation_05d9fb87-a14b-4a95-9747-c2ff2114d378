import React from 'react';
import { Search, Calendar, CheckCircle, Eye } from 'lucide-react';

interface Employee {
  id: string;
  name: string;
  type: string;
  email: string;
  contact: string;
  salary: string;
  arrears: string;
  status: 'Dues Clear' | 'Dues Pending';
}

interface DeductionItem {
  title: string;
  amount: string;
  percentage: string;
}

const AutomatedPayroll: React.FC = () => {
  // Removed useState for employees since we're not changing them in this component
  const employees: Employee[] = [
    { id: '01', name: '<PERSON>', type: 'Waiter', email: '<EMAIL>', contact: '555-2222-2344', salary: '$2,470.00', arrears: '', status: 'Dues Clear' },
    { id: '02', name: '<PERSON>', type: 'Manager', email: '<EMAIL>', contact: '555-2222-2344', salary: '$5,000.00', arrears: '$150.00', status: 'Dues Pending' },
    { id: '03', name: '<PERSON>', type: 'Cashier', email: '<EMAIL>', contact: '555-2222-2344', salary: '$2,899.00', arrears: '', status: 'Dues Clear' },
    { id: '01', name: 'Virat Kohli', type: 'Security', email: '<EMAIL>', contact: '555-2222-2344', salary: '$8,000.00', arrears: '$1,000.00', status: 'Dues Pending' }
  ];

  // Removed useState for deductions since we're not changing them in this component
  const deductions: DeductionItem[] = [
    { title: 'GST', amount: '$150.00', percentage: '10%' },
    { title: 'VAT', amount: '$800.00', percentage: '15%' },
    { title: 'Sales Tax', amount: '$550.00', percentage: '10%' },
    { title: 'Social Security', amount: '$300.00', percentage: '10%' }
  ];

  return (
    <div className="bg-gray-50 p-6 font-sans">
      <div className="flex justify-between items-center md:flex-row flex-col mb-6 bg-white p-3 border border-gray-200 rounded-2xl">
        <h1 className="text-3xl p-3 font-bold md:mb-0 mb-3">Automated Payroll</h1>
        <div className="flex md:flex-row flex-col gap-2">
          <button className="flex items-center gap-1 px-4 py-2 border border-orange-500 rounded-lg text-orange-500">
            <Calendar className="w-4 h-4" />
            <span>Edit Schedule of Payment</span>
          </button>
          <button className="flex items-center gap-1 px-4 py-2 bg-orange-500 text-white rounded-lg">
            <CheckCircle className="w-4 h-4" />
            <span>Approve Manually</span>
          </button>
        </div>
      </div>

      {/* Account Info */}
      <div className="flex justify-between items-center md:flex-row flex-col md:space-y-0 space-y-3 bg-white p-4 rounded-lg shadow-sm mb-6">
        <div className="flex items-center gap-3">
          <div className="bg-blue-600 text-white w-10 h-10 rounded-lg flex items-center justify-center text-xl font-bold">
            S
          </div>
          <div>
            <p className="text-sm text-gray-500">Stripe Account</p>
            <p className="font-medium"><EMAIL></p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-sm">Last Month Salary "April 2024" is Cleared</span>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid lg:grid-cols-5 md:grid-cols-3 grid-col-1 mb-6 border border-gray-200 rounded-2xl">
        <div className="bg-white p-4 rounded-2xl">
          <p className="text-sm text-gray-500 mb-1">Account Balance</p>
          <h2 className="text-xl font-bold">$2.80M</h2>
          <div className="flex items-center mt-2 text-sm">
            <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
            <span>This payment is scheduled for 1st May, 2024 Automatically</span>
          </div>
        </div>
        <div className="bg-white p-4">
          <p className="text-sm text-gray-500 mb-1">Employee's Net Pay</p>
          <h2 className="text-xl font-bold">$53422.30</h2>
        </div>
        <div className="bg-white p-4">
          <p className="text-sm text-gray-500 mb-1">Total Amount</p>
          <h2 className="text-xl font-bold">$750.53</h2>
        </div>

        <div className='bg-white p-4'>
              <p className="text-sm text-gray-500 mb-1">Next Payment Due</p>
              <h2 className="text-lg font-medium">1st May 2024</h2>
            </div>
            <div className='bg-white p-4'>
              <p className="text-sm text-gray-500 mb-1">No. of Employees</p>
              <h2 className="text-lg font-medium">31</h2>
            </div>
      
      </div>

      {/* Deduction Summary */}
      <div className="border border-gray-200 mb-3">
        <div className="flex justify-between items-center bg-orange-50 p-2 rounded-t-2xl">
          <h2 className="font-medium">Deduction Summary</h2>
          <button className="flex items-center gap-1 px-4 py-2 border border-orange-500 rounded-lg hover:bg-orange-600 text-orange-500 hover:text-white">
            <Eye className="w-4 h-4" />
            <span>View Tax Report</span>
          </button>
        </div>
        <div className="grid md:grid-cols-4 grid-cols-2">
          {deductions.map((deduction, index) => (
            <div key={index} className="bg-white p-4  rounded-b-2xl">
              <p className="text-sm text-gray-500 mb-1">{deduction.title}</p>
              <h3 className="text-xl font-bold">{deduction.amount}</h3>
              <p className="text-sm text-green-500 mt-2">{deduction.percentage}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Employees Salary History */}
      <div>
        <div className="flex justify-between md:flex-row flex-col items-center mb-4">
          <h2 className="font-medium text-xl md:mb-0 mb-3">Employees Salary History</h2>
          <div className="flex md:flex-row flex-col gap-2">
            <div className="relative">
              <input 
                type="text" 
                className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg"
                placeholder="Start date → End date"
              />
              <Calendar className="w-4 h-4 absolute right-3 top-3 text-gray-400" />
            </div>
            <div className="relative">
              <input 
                type="text" 
                className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg"
                placeholder="Search Employee"
              />
              <Search className="w-4 h-4 absolute right-3 top-3 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="bg-white rounded-2xl border border-gray-200 overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-orange-50">
                <th className="py-3 px-4 text-left font-medium text-gray-600">No</th>
                <th className="py-3 px-4 text-left font-medium text-gray-600">User Name</th>
                <th className="py-3 px-4 text-left font-medium text-gray-600">Employee Type</th>
                <th className="py-3 px-4 text-left font-medium text-gray-600">Email</th>
                <th className="py-3 px-4 text-left font-medium text-gray-600">Contact</th>
                <th className="py-3 px-4 text-left font-medium text-gray-600">Salary Amount</th>
                <th className="py-3 px-4 text-left font-medium text-gray-600">Arrears</th>
                <th className="py-3 px-4 text-left font-medium text-gray-600">Status</th>
                <th className="py-3 px-4 text-center font-medium text-gray-600">Action</th>
              </tr>
            </thead>
            <tbody>
              {employees.map((employee, index) => (
                <tr key={index} className="border-t border-gray-100 hover:bg-orange-50">
                  <td className="py-3 px-4">{employee.id}</td>
                  <td className="py-3 px-4">{employee.name}</td>
                  <td className="py-3 px-4">{employee.type}</td>
                  <td className="py-3 px-4">{employee.email}</td>
                  <td className="py-3 px-4">{employee.contact}</td>
                  <td className="py-3 px-4">{employee.salary}</td>
                  <td className="py-3 px-4 text-red-500">{employee.arrears}</td>
                  <td className="py-3 px-4">
                    <span 
                      className={`px-3 py-1 rounded-full text-xs ${
                        employee.status === 'Dues Clear' 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-orange-100 text-orange-700'
                      }`}
                    >
                      {employee.status}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <button className="text-orange-500">
                      <Eye className="w-5 h-5" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex justify-start mt-6">
          <button className="mx-1 p-2 rounded-md border border-gray-300">
            <span className="sr-only">Previous page</span>
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
          <button className="mx-1 px-3 py-2 rounded-md bg-orange-500 text-white">1</button>
          <button className="mx-1 p-2 rounded-md border border-gray-300">
            <span className="sr-only">Next page</span>
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AutomatedPayroll;