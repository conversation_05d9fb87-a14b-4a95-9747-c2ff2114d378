import { useState, useEffect } from 'react';
import {
  usePostTaxMutation,
  usePutTaxMutation,
  useGetTaxQuery,
} from '../../../store/api/taxApi';
import { toast } from 'react-toastify';
import { useNavigate, useParams } from 'react-router-dom';

export default function AddTaxForm() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  
  const userId = localStorage.getItem('userId') || '';

  const [postTax, { isLoading: isCreating }] = usePostTaxMutation();
  const [putTax, { isLoading: isUpdating }] = usePutTaxMutation();
  
  const { data: taxData, isLoading: isLoadingTax } = useGetTaxQuery(id || '', {
    skip: !isEditMode,
  });

  const [formData, setFormData] = useState({
    taxName: '',
    taxValue: '',
    isActive: false,
    isDefault: false
  });

  // Load existing data when in edit mode
  useEffect(() => {
    if (isEditMode && taxData) {
      setFormData({
        taxName: taxData.name || '',
        taxValue: taxData.taxValue || '',
        isActive: taxData.active === 'true' || false,
        isDefault: taxData.byDefault === 'true' || false
      });
    }
  }, [taxData, isEditMode]);

  const handleChange = (e: { target: { name: any; value: any; type: any; checked: any; }; }) => {
    const { name, value, type, checked } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async () => {
    try {
      // Validate form data
      if (!formData.taxName.trim()) {
        toast.error('Please enter tax name');
        return;
      }
      
      if (!formData.taxValue.trim()) {
        toast.error('Please enter tax value percentage');
        return;
      }
      
      // Check if tax value is a valid number
      const taxValueNum = parseFloat(formData.taxValue);
      if (isNaN(taxValueNum) || taxValueNum < 0) {
        toast.error('Please enter a valid tax value percentage');
        return;
      }

      // Prepare data for API
      const submitData = {
        name: formData.taxName,
        taxValue: formData.taxValue,
        active: formData.isActive.toString(),
        byDefault: formData.isDefault.toString(),
        userId: userId
      };

      console.log(`${isEditMode ? 'Updating' : 'Creating'} tax...`, submitData);

      if (isEditMode && id) {
        await putTax({ id, formData: submitData }).unwrap();
        toast.success('Tax updated successfully!');
      } else {
        await postTax(submitData).unwrap();
        toast.success('Tax added successfully!');
      }

      // Redirect to tax list
      navigate('/admin/administration/tax');
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error(`Failed to ${isEditMode ? 'update' : 'add'} tax. Please try again.`);
    }
  };

  const handleCancel = () => {
    navigate('/admin/administration/tax');
  };

  if (isEditMode && isLoadingTax) {
    return (
      <div className="w-full p-6 flex justify-center items-center">
        <p className="text-gray-600">Loading tax data...</p>
      </div>
    );
  }

  return (
    <div className="w-full p-2">
      <div>
        {/* Tax Type Details Section */}
        <div className="mb-6">
          <div className="bg-red-50 p-4 rounded-md mb-4">
            <h2 className="text-lg font-medium text-gray-800">Tax Type Details</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Tax Name Field */}
            <div>
              <label className="block text-gray-700 mb-2" htmlFor="taxName">
                Tax Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="taxName"
                name="taxName"
                value={formData.taxName}
                onChange={handleChange}
                placeholder="Enter Tax Name"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-orange-500"
              />
            </div>
            
            {/* Tax Value Percentage Field */}
            <div>
              <label className="block text-gray-700 mb-2" htmlFor="taxValue">
                Tax Value Percentage <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="taxValue"
                name="taxValue"
                value={formData.taxValue}
                onChange={handleChange}
                placeholder="Enter Tax Value %"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:border-orange-500"
              />
            </div>
          </div>
        </div>
        
        {/* Permissions Section */}
        <div className="mb-6">
          <div className="bg-red-50 p-4 rounded-md mb-4">
            <h2 className="text-lg font-medium text-gray-800">Permissions</h2>
          </div>
          
          <div className="space-y-4">
            {/* Activate Toggle */}
            <div className="flex items-center">
              <div className="relative inline-block w-12 mr-2 align-middle">
                <input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleChange}
                  className="opacity-0 w-0 h-0 absolute"
                />
                <label
                  htmlFor="isActive"
                  className={`block overflow-hidden h-6 rounded-full cursor-pointer ${
                    formData.isActive ? 'bg-orange-500' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`block h-6 w-6 rounded-full bg-white transform transition-transform duration-200 ease-in ${
                      formData.isActive ? 'translate-x-6' : 'translate-x-0'
                    }`}
                  />
                </label>
              </div>
              <label htmlFor="isActive" className="text-gray-700">Activate</label>
            </div>
            
            {/* Make Default Toggle */}
            <div className="flex items-center">
              <div className="relative inline-block w-12 mr-2 align-middle">
                <input
                  type="checkbox"
                  id="isDefault"
                  name="isDefault"
                  checked={formData.isDefault}
                  onChange={handleChange}
                  className="opacity-0 w-0 h-0 absolute"
                />
                <label
                  htmlFor="isDefault"
                  className={`block overflow-hidden h-6 rounded-full cursor-pointer ${
                    formData.isDefault ? 'bg-orange-500' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`block h-6 w-6 rounded-full bg-white transform transition-transform duration-200 ease-in ${
                      formData.isDefault ? 'translate-x-6' : 'translate-x-0'
                    }`}
                  />
                </label>
              </div>
              <label htmlFor="isDefault" className="text-gray-700">Make Default</label>
            </div>
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 border border-orange-500 text-orange-500 rounded-md hover:bg-orange-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            disabled={isCreating || isUpdating}
            className={`px-6 py-2 ${
              (isCreating || isUpdating) ? 'bg-orange-300' : 'bg-orange-500 hover:bg-orange-600'
            } text-white rounded-md transition-colors`}
          >
            {isEditMode ? 
              (isUpdating ? 'Updating...' : 'Update Tax') : 
              (isCreating ? 'Adding...' : 'Add Tax')
            }
          </button>
        </div>
      </div>
    </div>
  );
}