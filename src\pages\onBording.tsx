import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import onBording from "../assets/OnBording/onbording.png";

interface SlideData {
  title: string;
  subtitle: string;
  description: string;
  hasBackButton: boolean;
}

const OnBordingPage: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState<number>(0);
  const navigate = useNavigate();
  
  const slides: SlideData[] = [
    {
      title: "What sets us apart!",
      subtitle: "Cloud Based Accessibility",
      description: "Manage your store or restaurant anywhere, anytime with our cloud based POS system. All you need is internet access, and you're ready to handle business on the go. This flexibility ensures that you stay in control if your operations, whether you're on site or miles away.",
      hasBackButton: false
    },
    {
      title: "What sets us apart!",
      subtitle: "Cloud Based Accessibility",
      description: "Manage your store or restaurant anywhere, anytime with our cloud based POS system. All you need is internet access, and you're ready to handle business on the go. This flexibility ensures that you stay in control if your operations, whether you're on site or miles away.",
      hasBackButton: true
    }
    // Add more slides as needed
  ];
  
  const goToNextSlide = () => {
    if (currentSlide < slides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    } else if (currentSlide === slides.length - 1) {
      navigate('/role-type');
    }
  };
  
  const goToPreviousSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    }
  };
  
  const renderDots = () => {
    return (
      <div className="flex justify-center mt-4 space-x-2">
        {slides.map((_, index) => (
          <div 
            key={index}
            className={`h-2 rounded-full cursor-pointer transition-all duration-300 ${
              index === currentSlide 
                ? "w-8 bg-orange-500" 
                : "w-2 bg-orange-200"
            }`}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    );
  };
  
  const currentSlideData = slides[currentSlide];
  
  return (
    <div className="w-full min-h-screen bg-gray-50 px-4 sm:px-6 py-9">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center">
          <h1 className="text-2xl font-bold">
            Patron<span className="text-orange-500">Works</span>
          </h1>
          <div className="ml-auto text-sm font-medium">
            {currentSlideData.title}
          </div>
        </div>
        
        <div className="mt-12 flex flex-col-reverse md:flex-row gap-6 bg-white p-4 sm:p-6 rounded-lg">
          {/* Left Content */}
          <div className="w-full md:w-1/2 space-y-4">
            <h2 className="text-sm text-gray-600">
              {currentSlideData.title}
            </h2>
            <h3 className="text-2xl font-bold">
              {currentSlideData.subtitle}
            </h3>
            <p className="text-gray-600 leading-relaxed">
              {currentSlideData.description}
            </p>  

            <div className="flex flex-col sm:flex-row sm:space-x-4 space-y-4 sm:space-y-0 mt-8 justify-center items-center w-full">
  {currentSlideData.hasBackButton ? (
    <>
      <button 
        onClick={goToPreviousSlide}
        className="w-full sm:w-auto sm:px-20 py-2 border border-orange-500 rounded font-medium text-orange-500 hover:bg-gray-50"
      >
        Back
      </button>
      <button 
        onClick={goToNextSlide}
        className="w-full sm:w-auto sm:px-20 py-2 bg-orange-500 text-white rounded font-medium hover:bg-orange-600"
      >
        Next
      </button>
    </>
  ) : (
    <button 
      onClick={goToNextSlide}
      className="w-full sm:w-auto sm:px-40 py-2 bg-orange-500 text-white rounded font-medium hover:bg-orange-600"
    >
      Next
    </button>
  )}
</div>

          </div>
          
          {/* Right Image */}
          <div className="w-full md:w-1/2">
            <div className="rounded-lg overflow-hidden">
              <img 
                src={onBording} 
                alt="Person using POS system" 
                className="w-full h-full object-cover max-h-[300px] sm:max-h-none"
              />
            </div>
          </div>
        </div>

        <div className="pt-4">
          {renderDots()}
          
          <div className="mt-4 text-sm text-center" onClick={() => navigate('/auth')}>
            Already have an account? <span className="text-orange-500 font-medium cursor-pointer">Sign in</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnBordingPage;
