import React, { useState, useEffect } from 'react';
import { ChevronLeft } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  usePostCustomerMutation,
  usePutCustomerMutation, 
  useGetCustomersQuery 
} from '../../../store/api/customerApi';
import Swal from 'sweetalert2';

interface CustomerFormData {
  customerId: string;
  firstName: string;
  lastName: string;
  email: string;
  contactNumber: string;
  address: string;
  city: string;
  state: string;
  isActive: boolean;
}

const AddCustomerForm: React.FC = () => {
  const { id } = useParams();
  console.log("id:", id);
  const isEditMode = !!id;
  const navigate = useNavigate();
  
  // API hooks
  const [postCustomer, { isLoading: isPosting }] = usePostCustomerMutation();
  const [putCustomer, { isLoading: isUpdating }] = usePutCustomerMutation();
  const { data: customers, refetch } = useGetCustomersQuery();
  
  const [formData, setFormData] = useState<CustomerFormData>({
    customerId: '',
    firstName: '',
    lastName: '',
    email: '',
    contactNumber: '',
    address: '',
    city: '',
    state: '',
    isActive: false
  });
  
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Load customer data in edit mode
  useEffect(() => {
    if (isEditMode && customers) {
      const customerToEdit = customers.find(c => c._id === id);
      console.log("customerToEdit:", customerToEdit);
      if (customerToEdit) {
        setFormData({
          customerId: customerToEdit.CustomerId || '',
          firstName: customerToEdit.FirstName || '',
          lastName: customerToEdit.LastName || '',
          email: customerToEdit.Email || '',
          contactNumber: customerToEdit.Phone || '',
          address: customerToEdit.Address || '',
          city: customerToEdit.City || '',
          state: customerToEdit.State || '',
          isActive: customerToEdit.isActive || false
        });
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Customer not found',
          confirmButtonColor: '#f97316'
        });
        navigate(-1);
      }
    }
  }, [id, isEditMode, customers, navigate]);

  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!formData.firstName.trim()) errors.firstName = "First name is required";
    if (!formData.lastName.trim()) errors.lastName = "Last name is required";
    
    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)) {
      errors.email = "Invalid email address";
    }
    
    if (!formData.contactNumber.trim()) {
      errors.contactNumber = "Contact number is required";
    }
    
    if (!formData.address.trim()) errors.address = "Address is required";
    if (!formData.city.trim()) errors.city = "City is required";
    if (!formData.state.trim()) errors.state = "State is required";
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when field is being edited
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    const userId = localStorage.getItem('userId');
    if (!userId) {
      Swal.fire({
        icon: 'error',
        title: 'Authentication Error',
        text: 'User ID not found. Please login again.',
        confirmButtonColor: '#f97316'
      });
      return;
    }
    
    const customerData = {
      userId,
      FirstName: formData.firstName,
      LastName: formData.lastName,
      Email: formData.email,
      Phone: formData.contactNumber,
      Address: formData.address,
      City: formData.city,
      State: formData.state,
      isActive: formData.isActive,
    };

    try {
      if (isEditMode && id) {
        await putCustomer({
          _id: id,
          ...customerData
        }).unwrap();
        
        Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'Customer updated successfully!',
          
        });
        refetch();
  
      } else {
        await postCustomer(customerData).unwrap();
        
        Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'Customer created successfully!',
      
        });
      }
      navigate(-1);
    } catch (err) {
      console.error('Failed to save customer:', err);
      
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: `Failed to ${isEditMode ? 'update' : 'create'} customer`,
        confirmButtonColor: '#f97316'
      });
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  const isLoading = isPosting || isUpdating;

  return (
    <div className="bg-gray-50 p-4 rounded-lg shadow">
      <div className="p-4 border border-gray-200 bg-white rounded-2xl flex items-center">
        <button onClick={handleCancel} className="mr-2 p-1 cursor-pointer text-gray-700">
          <ChevronLeft size={20} />
        </button>
        <h1 className="text-xl font-bold text-gray-800">
          {isEditMode ? 'Edit Customer' : 'Add Customer'}
        </h1>
      </div>

      <form onSubmit={handleSubmit} className='mt-3 border border-gray-200 rounded-2xl bg-white'>
        {/* Personal Details Section */}
        <div className="p-3 bg-orange-50 border-b border-b-gray-200 rounded-t-2xl">
          <h2 className="text-lg font-medium text-gray-800">Personal Details</h2>
        </div>

        <div className="p-6 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="customerId" className="block text-sm font-medium text-gray-700 mb-1">
              Customer ID
            </label>
            <input
              type="text"
              id="customerId"
              name="customerId"
              value={formData.customerId}
              placeholder="Customer ID"
              className="w-full px-4 py-2 border border-gray-200 rounded-md bg-gray-100 text-gray-500"
              readOnly
            />
          </div>

          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
              First Name<span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              placeholder="Enter First Name"
              value={formData.firstName}
              onChange={handleInputChange}
              className={`w-full px-4 py-2 border ${formErrors.firstName ? 'border-red-500' : 'border-gray-200'} rounded-md focus:ring-orange-500 focus:border-orange-500`}
            />
            {formErrors.firstName && (
              <p className="text-xs text-red-500 mt-1">{formErrors.firstName}</p>
            )}
          </div>

          <div>
            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
              Last Name<span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              placeholder="Enter Last Name"
              value={formData.lastName}
              onChange={handleInputChange}
              className={`w-full px-4 py-2 border ${formErrors.lastName ? 'border-red-500' : 'border-gray-200'} rounded-md focus:ring-orange-500 focus:border-orange-500`}
            />
            {formErrors.lastName && (
              <p className="text-xs text-red-500 mt-1">{formErrors.lastName}</p>
            )}
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address<span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              id="email"
              name="email"
              placeholder="Enter Email Address"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full px-4 py-2 border ${formErrors.email ? 'border-red-500' : 'border-gray-200'} rounded-md focus:ring-orange-500 focus:border-orange-500`}
            />
            {formErrors.email && (
              <p className="text-xs text-red-500 mt-1">{formErrors.email}</p>
            )}
          </div>

          <div>
            <label htmlFor="contactNumber" className="block text-sm font-medium text-gray-700 mb-1">
              Contact Number<span className="text-red-500">*</span>
            </label>
            <input
              type="tel"
              id="contactNumber"
              name="contactNumber"
              placeholder="Enter Contact Number"
              value={formData.contactNumber}
              onChange={handleInputChange}
              className={`w-full px-4 py-2 border ${formErrors.contactNumber ? 'border-red-500' : 'border-gray-200'} rounded-md focus:ring-orange-500 focus:border-orange-500`}
            />
            {formErrors.contactNumber && (
              <p className="text-xs text-red-500 mt-1">{formErrors.contactNumber}</p>
            )}
          </div>

          <div>
            <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
              Address<span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="address"
              name="address"
              placeholder="Enter Address"
              value={formData.address}
              onChange={handleInputChange}
              className={`w-full px-4 py-2 border ${formErrors.address ? 'border-red-500' : 'border-gray-200'} rounded-md focus:ring-orange-500 focus:border-orange-500`}
            />
            {formErrors.address && (
              <p className="text-xs text-red-500 mt-1">{formErrors.address}</p>
            )}
          </div>

          <div>
            <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
              City<span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="city"
              name="city"
              placeholder="Enter City"
              value={formData.city}
              onChange={handleInputChange}
              className={`w-full px-4 py-2 border ${formErrors.city ? 'border-red-500' : 'border-gray-200'} rounded-md focus:ring-orange-500 focus:border-orange-500`}
            />
            {formErrors.city && (
              <p className="text-xs text-red-500 mt-1">{formErrors.city}</p>
            )}
          </div>

          <div>
            <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-1">
              State<span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="state"
              name="state"
              placeholder="Enter State"
              value={formData.state}
              onChange={handleInputChange}
              className={`w-full px-4 py-2 border ${formErrors.state ? 'border-red-500' : 'border-gray-200'} rounded-md focus:ring-orange-500 focus:border-orange-500`}
            />
            {formErrors.state && (
              <p className="text-xs text-red-500 mt-1">{formErrors.state}</p>
            )}
          </div>
        </div>

        {/* Permissions Section */}
        <div className="p-3 bg-orange-50 border-b border-gray-200 rounded-t-2xl">
          <h2 className="text-lg font-medium text-gray-800">Permissions</h2>
        </div>

        <div className="p-6">
          <div className="flex items-center">
            <label htmlFor="isActive" className="flex items-center cursor-pointer">
              <div className={`w-12 h-6 flex items-center rounded-full p-1 duration-300 ease-in-out ${formData.isActive ? 'bg-orange-500' : 'bg-gray-300'}`}>
                <div className={`bg-white w-4 h-4 rounded-full shadow-md transform duration-300 ease-in-out ${formData.isActive ? 'translate-x-6' : 'translate-x-0'}`}></div>
              </div>
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                className="hidden"
                checked={formData.isActive}
                onChange={handleInputChange}
              />
              <span className="ml-2 text-sm font-medium text-gray-900">Active</span>
            </label>
          </div>
        </div>

        {/* Form Actions */}
        <div className="p-6 flex justify-end space-x-4">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 border cursor-pointer border-gray-300 rounded-md text-orange-500 hover:bg-orange-50 transition-colors"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-6 py-2 bg-orange-500 cursor-pointer text-white rounded-md hover:bg-orange-600 transition-colors"
            disabled={isLoading}
          >
            {isLoading 
              ? isEditMode 
                ? 'Updating...' 
                : 'Adding...'
              : isEditMode 
                ? 'Update Customer' 
                : 'Add Customer'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddCustomerForm;