import React, { useEffect, useRef, useState } from "react";
import CustomModal from "../../CustomModal";
import { FiSearch } from "react-icons/fi";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format } from "date-fns";
import { useGetBillDenominationQuery } from "../../../store/api/pos/customer";
import CashierBillDemonstrationModal from "./CashierBillDemonstrationModal";

interface CloseOutReportsProps {
  isOpen: boolean;
  onClose: () => void;
}

interface CloseOutRecord {
  date: string;
  totalCloseCash: number;
  enteredBy: string;
}

interface BillDenomination {
  _id: string;
  EnteredBy: string;
  createdAt: string;
  denominations: Array<{
    value: number;
    quantity: number;
    _id: string;
  }>;
  totalCashOnhand: number;
  updatedAt: string;
  userId: string;
  __v: number;
}

const CloseOutReports: React.FC<CloseOutReportsProps> = ({
  isOpen,
  onClose,
}) => {
  const userId = localStorage.getItem("userId") || ""
  const { data, isLoading, error } = useGetBillDenominationQuery(userId)

  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ]);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;
  const [currentPage, setCurrentPage] = useState(1);
  const recordsPerPage = 10;

  // State for Cashier Bill Demonstration Modal
  const [isBillModalOpen, setIsBillModalOpen] = useState(false);
  const [selectedBillData, setSelectedBillData] = useState<BillDenomination | null>(null);

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      setDateRange([selection.startDate || null, selection.endDate || null]);
    }
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "Select Date Range";
    return `${format(startDate, "MMM dd, yyyy ")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };
  // Transform API data to CloseOutRecord format
  const closeOutRecords: CloseOutRecord[] = React.useMemo(() => {
    if (!data || isLoading) return [];

    return data.map((item: BillDenomination) => ({
      date: new Date(item.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }),
      totalCloseCash: item.totalCashOnhand,
      enteredBy: item.EnteredBy,
    }));
  }, [data, isLoading]);

  // Filter records based on search term and date range
  const filteredRecords = React.useMemo(() => {
    let filtered = closeOutRecords;

    // Filter by search term (EnteredBy only)
    if (searchTerm && searchTerm.trim() !== '') {
      const searchLower = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(record =>
        record.enteredBy && record.enteredBy.toLowerCase().includes(searchLower)
      );
    }

    // Filter by date range
    if (startDate && endDate) {
      filtered = filtered.filter(record => {
        const recordDate = new Date(record.date);
        return recordDate >= startDate && recordDate <= endDate;
      });
    }

    return filtered;
  }, [closeOutRecords, searchTerm, startDate, endDate]);

  // Pagination logic
  const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentRecords = filteredRecords.slice(startIndex, endIndex);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, startDate, endDate]);

  // Handle View button click
  const handleViewClick = (record: CloseOutRecord) => {
    // Find the original data from the API response
    const originalData = data?.find(item =>
      item.EnteredBy === record.enteredBy &&
      new Date(item.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }) === record.date &&
      item.totalCashOnhand === record.totalCloseCash
    );

    if (originalData) {
      setSelectedBillData(originalData);
      setIsBillModalOpen(true);
    }
  };

  // Handle closing the bill modal
  const handleCloseBillModal = () => {
    setIsBillModalOpen(false);
    setSelectedBillData(null);
  };
  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className={`px-4 py-2 text-sm font-medium cursor-pointer ${currentPage === 1
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-[#9C9C9C] hover:text-orange'
            }`}
          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium cursor-pointer ${currentPage >= totalPages
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-[#9C9C9C] hover:text-orange'
            }`}
          onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
          disabled={currentPage >= totalPages}
        >
          Next →
        </button>
      </div>
      <div className="flex gap-4 font-bold">
        <button
          onClick={onClose}
          className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
        >
          Cancel
        </button>
      </div>
    </div>
  );

  return (
    <>
      <CustomModal
        isOpen={isOpen}
        onClose={onClose}
        title="Close Out Reports"
        width="max-w-6xl"
        footer={footer}
      >
        <div className="p-6">
          {/* Search and Date Filter */}
          <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
            <div className="flex-1 mr-8">
              <div className="flex items-center border-b border-gray-200 pb-2">
                <FiSearch className="text-gray-400 mr-2" size={20} />
                <input
                  type="text"
                  placeholder="Search Entered By"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1 text-base bg-transparent border-none outline-none focus:outline-none"
                  autoComplete="off"
                  spellCheck="false"
                />
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm("")}
                    className="ml-2 text-gray-400 hover:text-gray-600 bg-transparent border-none cursor-pointer"
                    type="button"
                  >
                    ✕
                  </button>
                )}
              </div>
              <div className="mt-1 text-xs text-gray-500">
                {searchTerm ? (
                  <span>{filteredRecords.length} result{filteredRecords.length !== 1 ? 's' : ''} found for "{searchTerm}"</span>
                ) : (
                  <span>Type to search by "Entered By" field</span>
                )}
              </div>
            </div>
            <div className="relative mx-15">
              <button
                onClick={() => setIsCalendarOpen(!isCalendarOpen)}
                className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md  cursor-pointer"
              >
                {formatDateRange()}
              </button>
              {isCalendarOpen && (
                <div
                  ref={calendarRef}
                  className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
                >
                  <DateRangePicker
                    ranges={[
                      {
                        startDate: startDate || new Date(),
                        endDate: endDate || new Date(),
                        key: "selection",
                      },
                    ]}
                    onChange={handleDateRangeChange}
                    direction="horizontal"
                    // showSelectionPreview={true}
                    moveRangeOnFirstSelection={false}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-[#9C9C9C] text-xs font-extralight border-b border-[#E4E4E4]">
                  <th className="pb-3">Date</th>
                  <th className="pb-3">Total Close Cash</th>
                  <th className="pb-3">Entered By</th>
                  <th className="pb-3">Actions</th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan={4} className="py-8 text-center text-gray-500">
                      Loading...
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan={4} className="py-8 text-center text-red-500">
                      Error loading data
                    </td>
                  </tr>
                ) : currentRecords.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="py-8 text-center text-gray-500">
                      No records found
                    </td>
                  </tr>
                ) : (
                  currentRecords.map((record, index) => (
                    <tr key={index} className="border-b text-sm border-[#E4E4E4]">
                      <td className="py-4">{record.date}</td>
                      <td className="py-4">${record.totalCloseCash.toFixed(2)}</td>
                      <td className="py-4">{record.enteredBy}</td>
                      <td className="py-4">
                        <button
                          onClick={() => handleViewClick(record)}
                          className="bg-orange text-white px-14 py-2 rounded-full text-sm font-semibold cursor-pointer hover:bg-orange-600 transition-colors"
                        >
                          View
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </CustomModal>

      {/* Cashier Bill Demonstration Modal */}
      <CashierBillDemonstrationModal
        isOpen={isBillModalOpen}
        onClose={handleCloseBillModal}
        data={selectedBillData}
      />
    </>
  );
};

export default CloseOutReports;
