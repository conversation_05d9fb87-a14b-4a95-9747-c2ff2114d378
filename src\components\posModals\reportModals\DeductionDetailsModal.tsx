import React from 'react';
import CustomModal from '../../CustomModal';
import { Info, ChevronLeft, ChevronRight } from 'lucide-react';

interface Deduction {
    amount: number; // This is percentage, e.g., 5% = 5
    name: string;
    _id: string;
}

interface EmployeeData {
    empName: string;
    deductions?: Deduction[];
    subtotal?: number; // Subtotal passed from parent
}

interface DeductionDetailsModalProps {
    isOpen: boolean;
    onClose: () => void;
    employeeData: EmployeeData;
}

const DeductionDetailsModal: React.FC<DeductionDetailsModalProps> = ({
    isOpen,
    onClose,
    employeeData,
}) => {
    const { empName, deductions, subtotal = 0 } = employeeData;

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={onClose}
            title={`${empName} Deduction Amount Details`}
            width="max-w-md"
            zIndex={1001}
            footer={null}
        >
            <div>
                {deductions && deductions.length > 0 ? (
                    <>
                        <div className="p-4">
                            <div className="grid grid-cols-3 gap-4 text-gray-500 mb-2 px-2">
                                <div>Deduction Name</div>
                                <div>Percentage</div>
                                <div className="text-right">Deduction Amount</div>
                            </div>

                            <div className="space-y-4">
                                {deductions.map((deduction) => {
                                    const deductionAmount = (subtotal * deduction.amount) / 100;

                                    return (
                                        <div key={deduction._id} className="grid grid-cols-3 gap-4 py-4 border-t border-gray-200 px-2">
                                            <div className="text-gray-800">{deduction.name}</div>
                                            <div className="text-gray-800">{deduction.amount}%</div>
                                            <div className="text-gray-800 text-right">${deductionAmount.toFixed(2)}</div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Total deduction */}
                        <div className="bg-gray-50 p-4 mt-2 rounded-lg mx-4 mb-4">
                            <div className="flex justify-between items-center">
                                <p className="font-medium text-gray-800">Total Deduction:</p>
                                <p className="text-xl font-bold text-orange-500">
                                    $
                                    {deductions.reduce(
                                        (sum, deduction) => sum + (subtotal * deduction.amount) / 100,
                                        0
                                    ).toFixed(2)}
                                </p>
                            </div>
                        </div>

                        {/* Footer Navigation */}
                        <div className="flex justify-between items-center p-4 border-t border-gray-200">
                            <div className="flex space-x-2">
                                <button className="flex items-center text-gray-400 px-3 py-2 border border-gray-200 rounded-md">
                                    <ChevronLeft size={16} />
                                    <span className="ml-1">Previous</span>
                                </button>

                                <div className="flex items-center justify-center w-10 h-10 border border-gray-200 rounded-md font-medium">
                                    1
                                </div>

                                <button className="flex items-center text-gray-400 px-3 py-2 border border-gray-200 rounded-md">
                                    <span className="mr-1">Next</span>
                                    <ChevronRight size={16} />
                                </button>
                            </div>

                            <button
                                onClick={onClose}
                                className="px-12 py-2 border border-orange-500 text-orange-500 font-medium rounded-full hover:bg-orange-50 transition-colors"
                                type="button"
                            >
                                Cancel
                            </button>
                        </div>
                    </>
                ) : (
                    <>
                        <div className="flex flex-col items-center py-12">
                            <div className="mb-2">
                                <Info size={24} className="text-gray-500" />
                            </div>
                            <p className="text-gray-700">No deduction found.</p>
                        </div>

                        <div className="bg-gray-50 p-4 mx-4 mb-4 rounded-lg">
                            <div className="flex justify-between items-center">
                                <p className="font-medium text-gray-800">Total Deduction:</p>
                                <p className="text-xl font-bold text-orange-500">$0.00</p>
                            </div>
                        </div>

                        <div className="flex justify-between items-center p-4 border-t border-gray-200">
                            <div className="flex space-x-2">
                                <button className="flex items-center text-gray-400 px-3 py-2 border border-gray-200 rounded-md">
                                    <ChevronLeft size={16} />
                                    <span className="ml-1">Previous</span>
                                </button>

                                <div className="flex items-center justify-center w-10 h-10 border border-gray-200 rounded-md font-medium">
                                    1
                                </div>

                                <button className="flex items-center text-gray-400 px-3 py-2 border border-gray-200 rounded-md">
                                    <span className="mr-1">Next</span>
                                    <ChevronRight size={16} />
                                </button>
                            </div>

                            <button
                                onClick={onClose}
                                className="px-12 py-2 border border-orange-500 text-orange-500 font-medium rounded-full hover:bg-orange-50 transition-colors"
                                type="button"
                            >
                                Cancel
                            </button>
                        </div>
                    </>
                )}
            </div>
        </CustomModal>
    );
};

export default DeductionDetailsModal;