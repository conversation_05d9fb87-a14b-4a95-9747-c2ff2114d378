import React, { useState, useEffect } from 'react';
import { ChevronLeft } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  useCreateDeductionMutation,
  useGetDeductionByIdQuery,
  useUpdateDeductionMutation,
  useGetDeductionsQuery,
} from '../../../store/api/EmployeeListApi';
import Swal from 'sweetalert2'; // Import SweetAlert2

interface DeductionFormData {
  name: string;
}

export default function DeductionForm() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);

  // RTK Query hooks
  const { data: _ } = useGetDeductionsQuery();
  const { data: deductionData, isLoading: isLoadingDeduction } = useGetDeductionByIdQuery(id ?? '', {
    skip: !isEditMode || !id,
  });
  const [createDeduction, { isLoading: isCreating }] = useCreateDeductionMutation();
  const [updateDeduction, { isLoading: isUpdating }] = useUpdateDeductionMutation();

  const isLoading = isCreating || isUpdating || isLoadingDeduction;

  // Form state
  const [formData, setFormData] = useState<DeductionFormData>({
    name: ''
  });

  // Validation errors
  const [errors, setErrors] = useState({
    name: ''
  });

  // Populate form with existing data when in edit mode
  useEffect(() => {
    if (isEditMode && deductionData) {
      setFormData({
        name: deductionData.name || '',
      });
    }
  }, [isEditMode, deductionData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Clear validation error when user types
    if (errors[name as keyof typeof errors]) {
      setErrors({
        ...errors,
        [name]: '',
      });
    }
  };

  const validateForm = () => {
    const newErrors = {
      name: ''
    };
    let isValid = true;

    if (!formData.name.trim()) {
      newErrors.name = 'Deduction name is required';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (isEditMode && id) {
        // Update existing deduction
        await updateDeduction({ id, data: formData }).unwrap();
        // SweetAlert2 success notification
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Deduction updated successfully!',
          confirmButtonColor: '#f97316', // Orange-500 color
        });
      } else {
        // Create new deduction
        await createDeduction(formData).unwrap();
        // SweetAlert2 success notification
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Deduction added successfully!',
          confirmButtonColor: '#f97316', // Orange-500 color
        });
      }
      navigate(-1);
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} deduction:`, error);
      // SweetAlert2 error notification
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: `Unable to ${isEditMode ? 'update' : 'create'} deduction. Please try again.`,
        confirmButtonColor: '#f97316', // Orange-500 color
      });
    }
  };

  const handleCancel = () => {
    // Show confirmation dialog when canceling
    Swal.fire({
      title: 'Are you sure?',
      text: "You will lose any unsaved changes!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#f97316', // Orange-500 color
      cancelButtonColor: '#6b7280', // Gray-500 color
      confirmButtonText: 'Yes, discard changes'
    }).then((result) => {
      if (result.isConfirmed) {
        navigate(-1);
      }
    });
  };

  return (
    <div className="bg-gray-50 p-4">
      <div className="flex items-center border border-gray-200 bg-white p-4 rounded-2xl mb-6">
        <button
          onClick={handleCancel}
          className="flex items-center cursor-pointer text-gray-700 mr-2"
        >
          <ChevronLeft size={20} />
        </button>
        <h1 className="text-xl font-bold">{isEditMode ? 'Edit' : 'Add'} Deduction</h1>
      </div>

      {isEditMode && isLoadingDeduction ? (
           <div className="flex justify-center items-start bg-white h-screen pt-[30vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading deduction data...</p>
        </div>
      </div>
      ) : (
        <div className="bg-white mb-6 rounded-2xl border border-gray-50">
          <h2 className="text-lg font-medium bg-orange-50 p-3 text-gray-800">Deduction Details</h2>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-3">
              <div>
                <label className="block mb-2 text-sm font-medium text-gray-900">
                  Deduction Name<span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  placeholder="Enter Deduction Name"
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
                {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
              </div>
            </div>

            <div className="flex justify-end gap-4 p-4 mt-4">
              <button
                type="button"
                onClick={handleCancel}
                className="px-6 py-3 border border-gray-200 cursor-pointer text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className={`px-6 py-3 rounded-md text-white cursor-pointer ${
                  isLoading ? 'bg-orange-300 cursor-not-allowed' : 'bg-orange-500 hover:bg-orange-600'
                }`}
              >
                {isLoading ? (isEditMode ? 'Updating...' : 'Adding...') : (isEditMode ? 'Update Deduction' : 'Add Deduction')}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}