import React from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import MainLayout from "../pages/admin/MainLayout";
import Dashboard from "../pages/admin/AdminMainDashboard";
import MainOrdersLayout from "../pages/admin/AdminMainOrderLayout";

import AllOrdersLayout from "../components/admin/AllOrdersLayout";
import CompletedOrdersLayout from "../components/admin/CompletedOrderLayout";
import AllPosOrders from "../components/admin/Allorder/PosOrder";
import CompletedPosOrders from "../components/admin/completedorder/PosOrder";

import KitchenDisplayLayout from "../pages/admin/AdminKichtenLayout";
import AllOrder from "../components/admin/kitchenDispaly/allOrder";
import DriveThruOrders from "../components/admin/kitchenDispaly/driveThruOrders";
import KioskOrders from "../components/admin/kitchenDispaly/kioskOrders";
import OnlineOrders from "../components/admin/kitchenDispaly/onlineOrders";
import PosOrder from "../components/admin/kitchenDispaly/posOrder";
import AdminParentCategories from "../pages/admin/Catalog/AdminParentCategories";
import CreateParentCategory from "../pages/admin/Catalog/AdminParentCategoryForm";
import Categories from "../pages/admin/Catalog/AdminCategories";
import CreateCategoryForm from "../pages/admin/Catalog/AdminCategoriesFrom";
import Modifiers from "../pages/admin/Catalog/AdminModifires";
import AdminTables from "../pages/admin/TableManagement/AdminTables";
import AdminTableForm from "../pages/admin/TableManagement/AdminTableForm";
import AdminSiteManagement from "../pages/admin/TableManagement/AdminSiteManagement";
import AdminSiteForm from "../pages/admin/TableManagement/AdminSiteMangementForm";
import TablesCustomization from "../pages/admin/TableManagement/AdminTableCustomization";
import MenuItems from "../pages/admin/Catalog/AdminMenuItems";
import CreateMenuItem from "../pages/admin/Catalog/AdminMenuItemsForm";
import IngredientsCategory from "../pages/admin/StockManagement/IngredientsCategory";
import Ingredients from "../pages/admin/StockManagement/Ingredients";
import Wastages from "../pages/admin/StockManagement/Wastages";
import Supplier from "../pages/admin/StockManagement/Supplier";
import UnitOfMeasurement from "../pages/admin/StockManagement/UnitOfMeasurement";
import IngredientCategoryForm from "../pages/admin/StockManagement/ingredienstCatagoryFom";
import IngredientNewStock from "../pages/admin/StockManagement/IngredientNewStock";
import CreateNewIngredient from "../pages/admin/StockManagement/IngredientsForm";
import SupplierForm from "../pages/admin/StockManagement/SupplierForm";
import UnitOfMeasurementForm from "../pages/admin/StockManagement/UnitOfMeasurementForm";
import AddWastage from "../pages/admin/StockManagement/WastagesFom";
import Coupons from "../pages/admin/Marketing/Coupons";
import EmailMarketing from "../pages/admin/Marketing/EmailMarketing";
import SmsMarketing from "../pages/admin/Marketing/SmsMarketing";
import LoyaltyOffers from "../pages/admin/Marketing/LoyaltyOffers";
import CouponForm from "../pages/admin/Marketing/CouponsForm";
import AddEmailCampaign from "../pages/admin/Marketing/MarketingForm";
import AddSMSCampaign from "../pages/admin/Marketing/SmsMarketingForm";
import AddLoyaltyOffer from "../pages/admin/Marketing/LoyaltyOfferForm";
import AutomatedPayroll from "../pages/admin/AutomatedPayroll/AutomatedPayroll";
import EmployeeList from "../pages/admin/Employees/EmployeeList";
import AddDeductionForm from "../pages/admin/Employees/EmployeeDeductionForm";
import EmployeeDeductions from "../pages/admin/Employees/EmpolyeeDeductions";
import AddEmployeeForm from "../pages/admin/Employees/EmployeeListForm";
import SalaryStructure from "../pages/admin/Employees/SalaryStructure";
import SalaryStructureForm from "../pages/admin/Employees/SalaryStructureForm";
import CustomerManagement from "../pages/admin/Customer/CustomerList";
import AddCustomerForm from "../pages/admin/Customer/CustomerListForm";
import GiftCard from "../pages/admin/Customer/GiftCard";
import AddGiftCardForm from "../pages/admin/Customer/AddGiftForm";
import StripeConnection from "../pages/admin/Stripe/StripeConnection";
import CreateModifierForm from "../pages/admin/Catalog/AdminModifierForm";
import ProtectedRoute from "../components/ProtectedRoutes";
import AdministrationLayout from "../pages/admin/AdminAdministrationLayout";
import BusinessName from "../components/admin/AdministrationComponent/BusinessName";
import PaymentTypes from "../components/admin/AdministrationComponent/PaymentTypes";
import AddBusinessForm from "../components/admin/AdministrationComponent/AddBusinessForm";
import PaymentTypeForm from "../components/admin/AdministrationComponent/PaymentTypeForm";
import TaxPage from "../components/admin/AdministrationComponent/TaxPage";
import AddTaxForm from "../components/admin/AdministrationComponent/AddTaxForm";
import TaxCollected from "../components/admin/AdministrationComponent/TaxCollected";
import TaxPaid from "../components/admin/AdministrationComponent/TaxPaid";
import CustomizationPage from "../components/admin/AdministrationComponent/CustomizationPage";
import AddCustomizationForm from "../components/admin/AdministrationComponent/AddCustomizationForm";
import SubscriptionPlanPage from "../components/admin/AdministrationComponent/SubscriptionPlan";
import UpgradeSubscriptionPlan from "../components/admin/AdministrationComponent/UpgradeSubscriptionPlan";
import AllOnlineOrderItem from "../components/admin/Allorder/onlineOrder";
import CompletedOnlineOrderItem from "../components/admin/completedorder/onlineOrder";

const AdminRoutes: React.FC = () => {
  return (
    <Routes>
      <Route
        path="/admin"
        element={
          <ProtectedRoute>
            <MainLayout />
          </ProtectedRoute>
        }
      >
        <Route path="dashboard" element={<Dashboard />} />
        {/* // order */}
        <Route path="orders" element={<MainOrdersLayout />}>
          <Route index element={<Navigate to="all/pos-order" replace />} />
        <Route path="all" element={<AllOrdersLayout />}>
         <Route index element={<Navigate to="pos-order" replace />} />
          <Route path="pos-order" element={<AllPosOrders />} />
          <Route path="online-order" element={<AllOnlineOrderItem />} />
          {/* <Route path="drive-thru" element={<AllDriveThruOrders />} />
          <Route path="kiosk" element={<AllKioskOrders />} /> */}
        </Route>
        <Route path="completed" element={<CompletedOrdersLayout />}>
           <Route index element={<Navigate to="pos-order" replace />} />
  <Route path="pos-order" element={<CompletedPosOrders />} />
          <Route path="online-order" element={<CompletedOnlineOrderItem />} />
          {/* <Route path="drive-thru" element={<CompletedDriveThruOrders />} />
          <Route path="kiosk" element={<CompletedKioskOrders />} /> */}
        </Route>
      </Route>
      
        {/* //kichten */}
        <Route path="kitchen-display" element={<KitchenDisplayLayout />}>
          <Route index element={<Navigate to="all-orders" replace />} />
          <Route path="all-orders" element={<AllOrder />} />
          <Route path="pos-orders" element={<PosOrder />} />
          <Route path="online-orders" element={<OnlineOrders />} />
          <Route path="drive-thru-orders" element={<DriveThruOrders />} />
          <Route path="kiosk-orders" element={<KioskOrders />} />
        </Route>

        {/* // catalog */}
        <Route
          path="catalog/parent-categories"
          element={<AdminParentCategories />}
        />
        <Route
          path="catalog/parent-categories/parent-categories-form/:id?"
          element={<CreateParentCategory />}
        />
        <Route path="catalog/categories" element={<Categories />} />
        <Route
          path="catalog/categories/categories-form/:id?"
          element={<CreateCategoryForm />}
        />
        <Route path="catalog/menu-items" element={<MenuItems />} />
        <Route
          path="catalog/menu-items/menu-items-form/:id?"
          element={<CreateMenuItem />}
        />
        <Route path="catalog/modifiers" element={<Modifiers />} />
        <Route
          path="catalog/modifiers/modifier-form/:id?"
          element={<CreateModifierForm />}
        />
        {/* // tableManagement */}
        <Route path="tables-management/tables" element={<AdminTables />} />
        <Route
          path="tables-management/tables/table-form/:id?"
          element={<AdminTableForm />}
        />
        <Route
          path="tables-management/site-management"
          element={<AdminSiteManagement />}
        />
        <Route
          path="tables-management/site-management/site-form/:id?"
          element={<AdminSiteForm />}
        />
        <Route
          path="tables-management/tables-customization"
          element={<TablesCustomization />}
        />
        {/* // stockManagement */}
        <Route
          path="stock-management/ingredients-category"
          element={<IngredientsCategory />}
        />
        <Route
          path="stock-management/ingredients-category/ingredients-category-form/:id?"
          element={<IngredientCategoryForm />}
        />
        <Route path="stock-management/supplier" element={<Supplier />} />
        <Route
          path="stock-management/supplier/supplier-form/:id?"
          element={<SupplierForm />}
        />
        <Route path="stock-management/ingredients" element={<Ingredients />} />
        <Route
          path="stock-management/ingredients/stock-form"
          element={<IngredientNewStock />}
        />
        <Route
          path="stock-management/ingredients/ingredients-form/:id?"
          element={<CreateNewIngredient />}
        />
        <Route
          path="stock-management/unit-of-measurement"
          element={<UnitOfMeasurement />}
        />
        <Route
          path="stock-management/unit-of-measurement/unit-of-measurement-form/:id?"
          element={<UnitOfMeasurementForm />}
        />
        <Route path="stock-management/wastages" element={<Wastages />} />
        <Route
          path="stock-management/wastages/wastages-form"
          element={<AddWastage />}
        />
        {/* // marketing */}
        <Route path="marketing/coupons" element={<Coupons />} />
        <Route
          path="marketing/coupons/coupons-form/:id?"
          element={<CouponForm />}
        />
        <Route path="marketing/email-marketing" element={<EmailMarketing />} />
        <Route
          path="marketing/email-marketing/email-marketing-form"
          element={<AddEmailCampaign />}
        />
        <Route path="marketing/sms-marketing" element={<SmsMarketing />} />
        <Route
          path="marketing/sms-marketing/sms-marketing-form"
          element={<AddSMSCampaign />}
        />
        <Route path="marketing/loyalty-offers" element={<LoyaltyOffers />} />
        <Route
          path="marketing/loyalty-offers/loyalty-offer-form/:offerId?"
          element={<AddLoyaltyOffer />}
        />

        {/* <Route path="administration" element={<Administration />} /> */}
        <Route
          path="marketing/loyalty-offers/loyalty-offer-form"
          element={<AddLoyaltyOffer />}
        />

        <Route path="administration" element={<AdministrationLayout />}>
          <Route index element={<Navigate to="business-name" replace />} />
          <Route path="business-name" element={<BusinessName />} />
          <Route path="add-business-form/:id?" element={<AddBusinessForm />} />
          <Route path="payment-types" element={<PaymentTypes />} />
          <Route path="payment-types-form/:id?" element={<PaymentTypeForm />} />
          <Route path="tax" element={<TaxPage />} />
          <Route path="add-tax-form/:id?" element={<AddTaxForm />} />
          <Route path="tax-collected" element={<TaxCollected />} />
          <Route path="tax-paid" element={<TaxPaid />} />
          <Route path="customization" element={<CustomizationPage />} />
          <Route
            path="add-customization-form/:id?"
            element={<AddCustomizationForm />}
          />
          <Route path="subscription-plan" element={<SubscriptionPlanPage />} />
          <Route
            path="upgrade-subscription-plan"
            element={<UpgradeSubscriptionPlan />}
          />
        </Route>

        <Route path="automated-payroll" element={<AutomatedPayroll />} />

        <Route path="employees/employee-list" element={<EmployeeList />} />
        <Route
          path="employees/employee-list/employee-list-form/:id?"
          element={<AddEmployeeForm />}
        />
        <Route
          path="employees/salary-structure"
          element={<SalaryStructure />}
        />
        <Route
          path="employees/salary-structure/salary-structure-form/:id?"
          element={<SalaryStructureForm />}
        />
        <Route
          path="employees/employee-deductions"
          element={<EmployeeDeductions />}
        />
        <Route
          path="employees/employee-deductions/employee-deduction-form/:id?"
          element={<AddDeductionForm />}
        />

        <Route path="customer/customer-list" element={<CustomerManagement />} />
        <Route
          path="customer/customer-list/customer-form/:id?"
          element={<AddCustomerForm />}
        />
        <Route path="customer/gift-card" element={<GiftCard />} />
        <Route
          path="customer/gift-card/add-gift-form/:id?"
          element={<AddGiftCardForm />}
        />

        <Route path="connect-stripe" element={<StripeConnection />} />
      </Route>
    </Routes>
  );
};

export default AdminRoutes;
