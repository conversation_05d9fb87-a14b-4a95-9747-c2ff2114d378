import React from 'react';

interface Floor {
  id: string;
  name: string;
}

interface FloorSelectorProps {
  floors: Floor[];
  selectedFloor: string;
  onFloorSelect: (floorId: string) => void;
}

const FloorSelector: React.FC<FloorSelectorProps> = ({ floors, selectedFloor, onFloorSelect }) => {
  return (
    <div className="flex flex-col space-y-2 p-4">
      {floors.map((floor) => (
        <button
          key={floor.id}
          className={`py-4 px-8 rounded-4xl text-center hover:bg-[#FF5C00]  hover:text-white cursor-pointer transition-colors ${selectedFloor === floor.id
            ? 'bg-orange text-white'
            : 'bg-white text-gray-700 hover:bg-orange'
            }`}
          onClick={() => onFloorSelect(floor.id)}
        >
          {floor.name}
        </button>
      ))}
    </div>
  );
};

export default FloorSelector;