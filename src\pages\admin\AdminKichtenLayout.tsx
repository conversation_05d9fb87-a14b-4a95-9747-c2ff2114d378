import { useState } from 'react';
import { Outlet,useNavigate } from 'react-router-dom';

type OrderType = 'All' | 'POS' | 'Online' | 'Drive Thru' | 'Kiosk';
const disabledTabs: OrderType[] = ['Kiosk', 'Drive Thru'];

export default function KitchenDisplayLayout() {
  const [activeTab, setActiveTab] = useState<OrderType>('All');
   const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();
  

  const tabs: { type: OrderType; path: string }[] = [
  { type: 'All', path: '/admin/kitchen-display/all-orders' },
  { type: 'POS', path: '/admin/kitchen-display/pos-orders' },
  { type: 'Online', path: '/admin/kitchen-display/online-orders' },
  { type: 'Drive Thru', path: '/admin/kitchen-display/drive-thru-orders' },
  { type: 'Kiosk', path: '/admin/kitchen-display/kiosk-orders' }
];

  const handleTabChange = (tab: OrderType, path: string) => {
    setActiveTab(tab);
    navigate(path);
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-center p-4 bg-white m-2 rounded-3xl border border-gray-200 shadow">
        <h1 className="text-3xl font-bold mb-2  sm:mb-0">Kitchen Display</h1>
       <div className="relative w-full sm:w-64 bg-gray-100 rounded-md">
          <input 
            type="text"
            placeholder="Search Order"
            className="w-full border border-gray-200 rounded-lg py-2 px-4 pr-10 focus:outline-none focus:ring-2 focus:ring-orange-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
      </div>
     
      
      <div className="pt-6 pl-6 overflow-x-auto">
        <div className="flex whitespace-nowrap pb-2">
        {tabs.map((tab) => {
  const isDisabled = disabledTabs.includes(tab.type);
  return (
   <button
  key={tab.type}
  className={`px-4 py-1 mr-2 font-medium rounded-3xl transition duration-200
    ${isDisabled
      ? 'text-gray-700 cursor-not-allowed'
      : activeTab === tab.type
      ? 'bg-orange-50 border border-orange-500 text-orange-500 cursor-pointer'
      : 'text-gray-600 hover:text-orange-500 cursor-pointer'
    }`}
  onClick={() => !isDisabled && handleTabChange(tab.type, tab.path)}
  disabled={isDisabled}
>
  {tab.type === 'All' ? 'All Orders' : `${tab.type} Orders`}
</button>

  );
})}

        </div>
      </div>
     <div className="flex-1 overflow-hidden">
      <Outlet context={{ searchQuery }} />
      </div>
    </div>
  );
}