import { useState, useEffect } from 'react';
import { Search, Plus, Edit, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  useGetEmployeesListQuery,
  useDeleteEmployeeListMutation,
} from "../../../store/api/EmployeeListApi";
import Swal from 'sweetalert2';
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";


export default function EmployeeList() {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(6);
  
  const navigate = useNavigate();
  const userId = localStorage.getItem('userId') || '';

  // Fetch employees data using the API
  const { data: employees = [], isLoading, error, refetch } = useGetEmployeesListQuery(userId);
  const [deleteEmployee, { isLoading: isDeleting }] = useDeleteEmployeeListMutation();
  
  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleDeleteEmployee = (id: any) => {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you want to delete this employee?',
      icon: 'warning',
      iconColor: '#f97316', // orange-500
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
      background: '#ffffff',
     
    }).then((result) => {
      if (result.isConfirmed) {
        performDeleteEmployee(id);
      }
    });
  };

  const performDeleteEmployee = async (id: any) => {
    try {
      await deleteEmployee(id).unwrap();
      Swal.fire({
        title: 'Deleted!',
        text: 'Employee deleted successfully',
        icon: 'success',
       
        timer: 2000
      });
      refetch();
    } catch (error) {
      console.error('Failed to delete employee:', error);
      Swal.fire({
        title: 'Error!',
        text: 'Failed to delete employee',
        icon: 'error',
        
      });
    }
  };

  const handleAddEmployee = () => {
    navigate('/admin/employees/employee-list/employee-list-form');
  };

  const handleEdit = (id: any) => {
    navigate(`/admin/employees/employee-list/employee-list-form/${id}`);
  };

  // Filter employees based on search query
  const filteredEmployees = employees.filter((employee) =>
    employee.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    employee.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    employee.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    employee.phoneNo.includes(searchQuery) ||
    employee.employeeType.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentEmployees = filteredEmployees.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredEmployees.length / itemsPerPage);

  useEffect(() => {
    if (error) {
      Swal.fire({
        title: 'Error!',
        text: 'Failed to fetch employees',
        icon: 'error',
        confirmButtonColor: '#ea580c', // orange-600
      });
      console.error('Error fetching employees:', error);
    }
  }, [error]);

  // Format name by combining first and last name
  const formatName = (firstName: string, lastName: string): string => {
    return `${firstName} ${lastName}`;
  };
  return (
    <div className="p-4 bg-gray-50 rounded-lg">
      <div className="flex justify-between items-center md:flex-row flex-col mb-6 bg-white rounded-2xl border border-gray-200 p-2">
        <div className="flex items-center">
          <h1 className="text-xl font-bold text-gray-800 md:mb-0 mb-2">Employee List</h1>
        </div>
        <div className="flex md:flex-row flex-col gap-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Employee"
              className="pl-3 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchQuery}
              onChange={handleSearchChange}
            />
            <Search className="absolute right-3 top-2.5 text-gray-400" size={18} />
          </div>
          <button
            onClick={handleAddEmployee}
            className="flex items-center gap-1 cursor-pointer bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Add Employee
            <Plus size={18} />
          </button>
        </div>
      </div>

      {isLoading ? (
         <div className="flex justify-center items-start bg-white h-screen pt-[35vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading Employee list...</p>
        </div>
      </div>
      ) : error ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-red-500">Failed to load employees. Please try again later.</p>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto border border-gray-200 bg-white rounded-2xl">
            <table className="w-full">
              <thead>
                <tr className="bg-orange-50 border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold  text-gray-700">ID</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Name</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Email</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Address</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Contact</th>
                  <th className="text-left py-3 px-4 font-semibold w-[220px] text-gray-700">Employee Type</th>
                  <th className="text-right py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentEmployees.length > 0 ? (
                  currentEmployees.map((employee) => (
                    <tr key={employee.id} className="border-b border-gray-200 hover:bg-orange-50">
                      <td className="py-4 px-4 text-gray-800">{employee.employeeId}</td>
                      <td className="py-4 px-4 text-gray-800">{formatName(employee.firstName, employee.lastName)}</td>
                      <td className="py-4 px-4 text-gray-800">{employee.email}</td>
                      <td className="py-4 px-4 text-gray-800">{employee.address}</td>
                      <td className="py-4 px-4 text-gray-800">{employee.phoneNo}</td>
                      <td className="py-4 px-4 text-gray-800">{employee.employeeType}</td>
                      <td className="py-4 px-4 flex justify-end gap-2">
                        <button
                          onClick={() => handleEdit(employee.id)}
                          className="text-blue-500 hover:text-blue-700"
                          disabled={isDeleting}
                        >
                        <Edit
                          id="edit-icon"
                          data-tooltip-id="edit-tooltip"
                          data-tooltip-content="Edit"
                          size={20}
                          className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                        />
                        <Tooltip
                          id="edit-tooltip"
                          place="bottom"
                          className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                        />

                        </button>
                        <button 
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleDeleteEmployee(employee.id)}
                          disabled={isDeleting}
                        >
                                                  <Trash2
                          id="delete-icon"
                          data-tooltip-id="delete-tooltip"
                          data-tooltip-content="Delete"
                          size={20}
                          className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                        />
                        <Tooltip
                          id="delete-tooltip"
                          place="bottom"
                          className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                        />

                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="py-4 px-4 text-center text-gray-500">
                      No employees found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {filteredEmployees.length > 0 && (
            <div className="flex justify-start items-center gap-2 mt-6">
              <button 
                className={`p-2 rounded-md border border-gray-200 ${
                  currentPage === 1 ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "hover:bg-gray-100"
                }`}
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft size={18} />
              </button>
              
              {Array.from({ length: Math.min(totalPages, 4) }, (_, i) => {
                // Show pagination numbers based on current page position
                let pageNum = i + 1;
                if (totalPages > 4 && currentPage > 2) {
                  pageNum = Math.min(currentPage - 1 + i, totalPages);
                  if (i === 0 && currentPage > 2) pageNum = 1;
                  if (i === 3 && pageNum < totalPages) pageNum = totalPages;
                }
                
                return (
                  <button 
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={`w-8 h-8 flex items-center justify-center rounded-md ${
                      currentPage === pageNum 
                        ? 'bg-orange-500 text-white' 
                        : 'border border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
              
              <button 
                className={`p-2 rounded-md border border-gray-200 ${
                  currentPage === totalPages ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "hover:bg-gray-100"
                }`}
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                <ChevronRight size={18} />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}