import { useState, useEffect } from 'react';
import { Edit, Trash2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
    useGetAdministrationsQuery,
    useDeleteAdministrationMutation,
} from "../../../store/api/administrationApi";
import Swal from 'sweetalert2';

const BusinessName = () => {
    const [businessToDelete, setBusinessToDelete] = useState(null);

    const navigate = useNavigate();
    const userId = localStorage.getItem('userId') || '';

    // Fetch business data using the API
    const { data: businesses = [], isLoading, error, refetch } = useGetAdministrationsQuery(userId);
    const [deleteAdministration, { isLoading: isDeleting }] = useDeleteAdministrationMutation();

    const handleDeleteBusiness = (id: any) => {
        setBusinessToDelete(id);
        
        Swal.fire({
            title: 'Are you sure?',
            text: 'Do you want to delete this business?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#6b7280',
     
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
         
            customClass: {
                confirmButton: 'bg-orange-600 hover:bg-orange-700',
                cancelButton: 'bg-gray-200 hover:bg-gray-300 text-gray-800'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                confirmDelete();
            }
        });
    };

    const confirmDelete = async () => {
        if (businessToDelete) {
            try {
                await deleteAdministration(businessToDelete).unwrap();
                
                Swal.fire({
                    title: 'Deleted!',
                    text: 'Business deleted successfully',
                    icon: 'success',
                    confirmButtonColor: '#ea580c'
                });
                
                refetch();
            } catch (error) {
                console.error('Failed to delete business:', error);
                
                Swal.fire({
                    title: 'Error!',
                    text: 'Failed to delete business',
                    icon: 'error',
                    confirmButtonColor: '#ea580c'
                });
            }
        }
    };

    const handleEdit = (id: any) => {
        navigate(`/admin/administration/add-business-form/${id}`);
    };

    useEffect(() => {
        if (error) {
            Swal.fire({
                title: 'Error!',
                text: 'Failed to fetch business information',
                icon: 'error',
                confirmButtonColor: '#ea580c'
            });
            console.error('Error fetching business information:', error);
        }
    }, [error]);

    // Helper function to determine status display
    const getStatusDisplay = (active: any) => {
        const isActive = active === true || active === 'true';

        return isActive ? (
            <span className="inline-flex rounded-full px-3 py-1 text-sm bg-green-100 text-green-800">
                Active
            </span>
        ) : (
            <span className="inline-flex rounded-full px-3 py-1 text-sm bg-red-100 text-red-800">
                Inactive
            </span>
        );
    };

    // Format address from components
    const formatAddress = (business: any) => {
        const addressParts = [
            business.Line1,
            business.Line2,
            business.City,
            business.State,
            business.PostalCode,
            business.Country
        ].filter(Boolean);

        return addressParts.join(', ');
    };

    return (
        <div className="rounded-lg bg-white shadow-sm">
            <div className="flex justify-between items-center p-4">
                <h2 className="text-xl font-semibold">Business Info</h2>
                {businesses.length === 0 && (
                    <button
                        onClick={() => navigate('/admin/administration/add-business-form')}
                        className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                        Add Business
                    </button>
                )}
            </div>

            {isLoading ? (
                <div className="bg-gray-50 p-4 shadow min-h-screen flex justify-center items-center">
                    <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                        <span>Loading business information...</span>
                    </div>
                </div>
            ) : error ? (
                <div className="flex justify-center items-center h-40">
                    <p className="text-red-500">Failed to load business information. Please try again later.</p>
                </div>
            ) : (
                <div className="overflow-x-auto">
                    <table className="w-full table-auto">
                        <thead className="bg-orange-50 text-left">
                            <tr>
                                <th className="p-4 font-medium text-gray-700">Business Name</th>
                                <th className="p-4 font-medium text-gray-700">Phone No</th>
                                <th className="p-4 font-medium text-gray-700">Address</th>
                                <th className="p-4 font-medium text-gray-700">Status</th>
                                <th className="p-4 font-medium text-gray-700">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {businesses.length > 0 ? (
                                businesses.map((business) => (
                                    <tr key={business.id} className="border-t border-gray-200 hover:bg-orange-50">
                                        <td className="p-4">{business.name || 'N/A'}</td>
                                        <td className="p-4">{business.Phoneno || 'N/A'}</td>
                                        <td className="p-4">{formatAddress(business) || 'N/A'}</td>
                                        <td className="p-4">
                                            {getStatusDisplay(business.active)}
                                        </td>
                                        <td className="p-4">
                                            <div className="flex space-x-2">
                                                <button
                                                    className="text-blue-500 hover:text-blue-700"
                                                    onClick={() => handleEdit(business.id)}
                                                    disabled={isDeleting}
                                                >
                                                    <Edit size={18} />
                                                </button>
                                                <button
                                                    className="text-red-500 hover:text-red-700"
                                                    onClick={() => handleDeleteBusiness(business.id)}
                                                    disabled={isDeleting}
                                                >
                                                    <Trash2 size={18} />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))
                            ) : (
                                <tr className="border-t border-gray-200">
                                    <td colSpan={5} className="p-4 text-center text-gray-500">
                                        No business information found.
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
};

export default BusinessName;