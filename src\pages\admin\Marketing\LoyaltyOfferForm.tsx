import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronDown } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import { useGetProductsQuery } from '../../../store/api/menuitemApi';
import { useGetLoyaltyOffersQuery, useCreateLoyaltyOfferMutation, useUpdateLoyaltyOfferMutation } from '../../../store/api/loyaltyOfferAPi';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

interface Product {
  id: string;
  name: string;
}

interface FormData {
  productName: string;
  productNam: string;
  description: string;
  offerQty: number | null;
  isActive: boolean;
}

interface LoyaltyOffer {
  _id: string;
  productName: {
    _id: string;
    name: string;
  };
  productNam: string;
  description: string;
  offerQty: number;
  isActive: boolean;
  active: string; // "true" or "false" string from API
}

export default function AddLoyaltyOffer() {
  const { offerId } = useParams<{ offerId: string }>(); 

  const userId = localStorage.getItem('userId') || '';
  const navigate = useNavigate();

  // Fetch products and loyalty offers
  const { data: products = [], isLoading: isLoadingProducts } = useGetProductsQuery(userId);
  const { data: loyaltyOffers = [] } = useGetLoyaltyOffersQuery(userId);
  const [createLoyaltyOffer, { isLoading: isSubmitting }] = useCreateLoyaltyOfferMutation();
  const [updateLoyaltyOffer, { isLoading: isUpdating }] = useUpdateLoyaltyOfferMutation();

  const [formData, setFormData] = useState<FormData>({
    productName: '',
    productNam: '',
    description: '',
    offerQty: null,
    isActive: false,
  });

  const [errors, setErrors] = useState<{ productId?: string; quantity?: string }>({});
  const [showProductDropdown, setShowProductDropdown] = useState(false);

  useEffect(() => {
    if (offerId && loyaltyOffers.length > 0) {
      const selectedOffer = loyaltyOffers.find((offer: LoyaltyOffer) => offer._id === offerId);
      if (selectedOffer) {
        console.log("selectedOffer: ", selectedOffer);
        setFormData({
          productName: selectedOffer.productName._id, 
          productNam: selectedOffer.productName.name, 
          description: selectedOffer.description,
          offerQty: selectedOffer.offerQty,
          isActive: selectedOffer.active === "true", // Convert string to boolean
        });
      }
    }
  }, [offerId, loyaltyOffers]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleProductSelect = (product: Product) => {
    setFormData(prev => ({
      ...prev,
      productName: product.id,
      productNam: product.name,
    }));
    setShowProductDropdown(false);
    setErrors(prev => ({ ...prev, productId: undefined }));
  };

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    // If input is empty, set to null
    if (inputValue === '') {
      setFormData(prev => ({ ...prev, offerQty: null }));
    } else {
      const value = parseInt(inputValue);
      if (!isNaN(value)) {
        setFormData(prev => ({ ...prev, offerQty: value }));
        if (value > 0) setErrors(prev => ({ ...prev, quantity: undefined }));
      }
    }
  };

  const handleToggleChange = () => {
    setFormData(prev => ({
      ...prev,
      isActive: !prev.isActive, // Toggle the active state
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const newErrors: { productId?: string; quantity?: string } = {};
    if (!formData.productName) newErrors.productId = 'Product is required';
    if (!formData.offerQty || formData.offerQty <= 0) newErrors.quantity = 'Quantity is required';

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors({});

    try {
      const loyaltyOfferData = {
        productName: formData.productName,
        description: formData.description,
        offerQty: formData.offerQty || 0, // Ensure we send at least 0
        active: formData.isActive.toString(), // Convert boolean to string for API
      };

      if (offerId) {
        console.log("offerId: hwdbex", offerId); 
        console.log("Updated data:", loyaltyOfferData);
      
        await updateLoyaltyOffer({ id: offerId, ...loyaltyOfferData }).unwrap();
        toast.success('Loyalty offer updated successfully!');
      } else {
        await createLoyaltyOffer(loyaltyOfferData).unwrap();
        toast.success('Loyalty offer created successfully!');
      }
      
      navigate("/admin/marketing/loyalty-offers");
    } catch (error) {
      console.error('Failed to create or update loyalty offer:', error);
      toast.error('Failed to create or update loyalty offer. Please try again.');
    }
  };

  const handleCancel = () => {
    navigate("/admin/marketing/loyalty-offers");
  };

  return (
    <div className="bg-gray-50 min-h-screen p-4">
      <div>
        <div
          onClick={handleCancel}
          className="flex items-center mb-6 text-gray-800 border border-gray-200 bg-white rounded-2xl p-2 cursor-pointer"
        >
          <ChevronLeft className="w-5 h-5 mr-2" />
          <h1 className="text-xl font-medium">{offerId ? 'Edit Loyalty Offer' : 'Add Loyalty Offer'}</h1>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Loyalty Offer Details Section */}
          <div className="mb-6">
            <div className="bg-orange-50 py-3 px-4 text-gray-800 font-medium rounded-t-lg border-b border-orange-100">
              Loyalty Offer Details
            </div>
            <div className="bg-white p-6 rounded-b-lg border border-gray-200 border-t-0 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Product Dropdown */}
                <div>
                  <label className="text-sm mb-2 block">Select Product <span className="text-red-500">*</span></label>
                  <div className="relative mt-1">
                    <button
                      type="button"
                      className="w-full bg-white border border-gray-300 rounded-md p-2 flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-orange-500"
                      onClick={() => setShowProductDropdown(!showProductDropdown)}
                      disabled={isLoadingProducts}
                    >
                      <span className={`text-gray-500 ${formData.productNam ? 'text-black' : ''}`}>
                        {isLoadingProducts
                          ? 'Loading products...'
                          : formData.productNam || 'Select Product'}
                      </span>
                      <ChevronDown className="w-4 h-4 text-gray-500" />
                    </button>
                    {showProductDropdown && !isLoadingProducts && (
                      <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        <ul className="py-1">
                          {products.length > 0 ? (
                            products.map((product: Product) => (
                              <li
                                key={product.id}
                                className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                                onClick={() => handleProductSelect(product)}
                              >
                                {product.name}
                              </li>
                            ))
                          ) : (
                            <li className="px-3 py-2 text-gray-500">No products available</li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                  {errors.productId && (
                    <p className="text-red-500 text-sm mt-1">{errors.productId}</p>
                  )}
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm mb-2">Description</label>
                  <input
                    type="text"
                    name="description"
                    placeholder="Enter Offer Description"
                    value={formData.description}
                    onChange={handleInputChange}
                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>

                {/* Quantity */}
                <div>
                  <label className="block text-sm mb-2">
                    Quantity <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    name="offerQty"
                    placeholder="0"
                    value={formData.offerQty === null ? '' : formData.offerQty}
                    onChange={handleQuantityChange}
                    min="0"
                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                  {errors.quantity && (
                    <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Permissions Section */}
          <div className="mb-6">
            <div className="bg-orange-50 py-3 px-4 text-gray-800 font-medium rounded-t-lg border-b border-orange-100">
              Permissions
            </div>
            <div className="bg-white p-6 rounded-b-lg border border-gray-200 border-t-0">
              <div className="flex items-center">
                <label className="inline-flex relative items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only"
                    checked={formData.isActive}
                    onChange={handleToggleChange}
                  />
                  <div className={`w-12 h-6 rounded-full transition-colors ${formData.isActive ? 'bg-orange-500' : 'bg-gray-300'}`}>
                    <div className={`w-4 h-4 bg-white rounded-full shadow transform transition-transform ${
                      formData.isActive ? 'translate-x-7' : 'translate-x-1'
                    } mt-1`}></div>
                  </div>
                </label>
                <p className="ml-3 text-sm">Active</p>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <button
              type="button"
              className="px-6 py-2 bg-gray-300 text-black rounded-md hover:bg-gray-400"
              onClick={handleCancel}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600"
              disabled={isSubmitting || isUpdating}
            >
              {isSubmitting || isUpdating ? 
                (offerId ? 'Updating...' : 'Creating...') : 
                (offerId ? 'Update Offer' : 'Create Offer')
              }
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}