
import React, { useState, useMemo } from 'react';
import CustomModal from '../../CustomModal';
import { useGetCouponsQuery } from '../../../store/api/pos/orderapi';

interface DiscountOption {
  id: string;
  description: string;
  discount: number;
  discountType: string;
  series: string;
}

interface DiscountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (discountAmount: string, selectedDiscount: DiscountOption | null, loyaltyData?: { amount: string, percentage?: number, type: string }) => void;
}

const DiscountModal: React.FC<DiscountModalProps> = ({ isOpen, onClose, onApply }) => {
  const [inputValue, setInputValue] = useState('');
  const [selectedOfferType, setSelectedOfferType] = useState('');
  const [selectedDiscount, setSelectedDiscount] = useState<DiscountOption | null>(null);
  const userId = localStorage.getItem('userId') || ""
  const { data: couponsData, isLoading, error } = useGetCouponsQuery(userId)

  // Filter and transform coupons based on end date and published status
  const discountOptions: DiscountOption[] = useMemo(() => {
    if (!couponsData || !Array.isArray(couponsData)) {
      return [];
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day for accurate comparison

    return couponsData
      .filter((coupon: any) => {
        // Filter by end date (greater than today) and published status
        const endDate = new Date(coupon.endDate);
        endDate.setHours(0, 0, 0, 0); // Set to start of day for accurate comparison
        return endDate > today && coupon.publish === true;
      })
      .map((coupon: any) => ({
        id: coupon._id,
        description: coupon.description,
        discount: coupon.discount,
        discountType: coupon.discountType,
        series: coupon.series
      }));

  }, [couponsData]);

  const handleDiscountSelect = (discount: DiscountOption) => {
    // Single selection - if same discount is clicked, deselect it
    if (selectedDiscount?.id === discount.id) {
      setSelectedDiscount(null);
      setInputValue('');
    } else {
      setSelectedDiscount(discount);
      setInputValue(discount.discount.toString());
    }
  };

  const handleNumberClick = (num: string) => {
    if (num === 'C') {
      setInputValue('');
      setSelectedDiscount(null); // Also clear selected discount when clearing
    } else if (num === '.') {
      if (!inputValue.includes('.')) {
        setInputValue(prev => prev + '.');
      }
    } else if (num === 'Add') {
      handleApply();
    } else {
      setInputValue(prev => prev + num);
    }
  };

  const handleBackspace = () => {
    setInputValue(prev => prev.slice(0, -1));
  };

  const handleApply = () => {
    let loyaltyData: { amount: string, percentage?: number, type: string } | undefined;

    // Calculate loyalty based on discount type - separate from discount
    if (selectedDiscount) {
      if (selectedDiscount.discountType === '%') {
        // If percentage, store the percentage for dynamic calculation
        loyaltyData = {
          amount: '0', // Will be calculated dynamically
          percentage: selectedDiscount.discount,
          type: 'percentage'
        };
      } else {
        // If not percentage (fixed amount), store as fixed amount
        loyaltyData = {
          amount: selectedDiscount.discount.toString(),
          type: 'fixed'
        };
      }
    } else if (inputValue && inputValue.trim() !== '' && Number(inputValue) > 0) {
      // If manual input with valid value, treat as fixed amount for loyalty
      loyaltyData = {
        amount: inputValue,
        type: 'fixed'
      };
    }
    // If no selectedDiscount and no valid inputValue, loyaltyData remains undefined
    // This will trigger the reset logic in InvoicePanel

    // Don't pass discount amount for loyalty - pass '0' for discount
    onApply('0', selectedDiscount, loyaltyData);
    onClose();
  };

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Select Discount"
      width="max-w-5xl"
      zIndex={1000000}
      footer={null}
    >
      <div className="p-6">
        {/* Input Fields */}
        <div className="flex gap-4 mb-6">
          {/* Left Column - Enter or scan offer */}
          <div className="flex-1">
            <label className="block text-gray-600 mb-2 text-sm">Enter or scan offer</label>
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Scan or type offer"
              className="w-full p-3 border border-gray-200 text-gray-400 rounded-lg focus:outline-none focus:border-orange-300"
            />
          </div>

          {/* Right Column - Select Offer Type */}
          <div className="flex-1">
            <label className="block text-gray-600 mb-2 text-sm">Select Offer Type</label>
            <input
              type="text"
              value={selectedOfferType}
              onChange={(e) => setSelectedOfferType(e.target.value)}
              placeholder="(or) Select Offer Type"
              className="w-full p-3 border border-gray-200 text-gray-400 rounded-lg focus:outline-none focus:border-orange-300"
            />
          </div>
        </div>

        {/* Main Content - Discount Cards and Number Pad Side by Side */}
        <div className="flex gap-8 mb-6">
          {/* Left Side - Discount Cards in responsive grid */}
          <div className="flex-1">
            {isLoading ? (
              <div className="flex items-center justify-center h-40">
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                  <p className="text-gray-500 mt-2">Loading coupons...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-40">
                <div className="text-center">
                  <div className="text-red-500 mb-2">
                    <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <p className="text-red-600 font-medium">Failed to load coupons</p>
                  <p className="text-gray-500 text-sm mt-1">Please try again later</p>
                </div>
              </div>
            ) : discountOptions.length === 0 ? (
              <div className="flex items-center justify-center h-40">
                <div className="text-center">
                  <div className="text-gray-400 mb-2">
                    <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                    </svg>
                  </div>
                  <p className="text-gray-600 font-medium">No active coupons available</p>
                  <p className="text-gray-500 text-sm mt-1">All coupons may have expired or are not published</p>
                </div>
              </div>
            ) : (
              <div className="flex flex-wrap gap-3">
                {discountOptions.map((discount) => {
                  const isSelected = selectedDiscount?.id === discount.id;

                  return (
                    <button
                      key={discount.id}
                      onClick={() => handleDiscountSelect(discount)}
                      className={`flex-1 min-w-[280px] border rounded-lg p-3 transition-all duration-200 text-left ${isSelected
                        ? 'border-orange-300 bg-orange-50 shadow-sm'
                        : 'border-gray-200 bg-white hover:border-orange-300 hover:shadow-sm'
                        }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center flex-1">
                          {/* Checkbox */}
                          <div className={`w-4 h-4 border-2 mr-3 rounded-full flex items-center justify-center ${isSelected
                            ? 'bg-orange-500 border-orange-500'
                            : 'border-gray-300'
                            }`}>
                            {isSelected && (
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            )}
                          </div>

                          {/* Description */}
                          <div className="flex-1 pr-2">
                            <div className="text-gray-700 text-sm font-medium">
                              {discount.description}
                            </div>
                          </div>
                        </div>

                        {/* Discount Badge with image */}
                        <div className="flex items-center">
                          {/* Badge with discount text overlay */}
                          <div className="relative flex-shrink-0">
                            <img
                              src="/Badge.png"
                              alt="Discount badge"
                              className="w-16 h-full object-contain"
                            />
                            <span className="absolute inset-0 flex items-center justify-center text-white text-sm font-bold">
                              {discount.discountType === '%' ? `${discount.discount}%` : `$${discount.discount}`}
                            </span>
                          </div>
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>
            )}
          </div>

          {/* Right Side - Number Pad */}
          <div className="flex-shrink-0">
            <div className="max-w-xs">
              {/* Row 1 */}
              <div className="grid grid-cols-4 gap-3 mb-3">
                <button
                  onClick={() => handleNumberClick('1')}
                  className="bg-gray-100 hover:bg-gray-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-gray-700 transition-colors"
                >
                  1
                </button>
                <button
                  onClick={() => handleNumberClick('2')}
                  className="bg-gray-100 hover:bg-gray-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-gray-700 transition-colors"
                >
                  2
                </button>
                <button
                  onClick={() => handleNumberClick('3')}
                  className="bg-gray-100 hover:bg-gray-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-gray-700 transition-colors"
                >
                  3
                </button>
                <button
                  onClick={() => handleNumberClick('10')}
                  className="bg-blue-100 hover:bg-blue-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-blue-600 transition-colors"
                >
                  10
                </button>
              </div>

              {/* Row 2 */}
              <div className="grid grid-cols-4 gap-3 mb-3">
                <button
                  onClick={() => handleNumberClick('4')}
                  className="bg-gray-100 hover:bg-gray-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-gray-700 transition-colors"
                >
                  4
                </button>
                <button
                  onClick={() => handleNumberClick('5')}
                  className="bg-gray-100 hover:bg-gray-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-gray-700 transition-colors"
                >
                  5
                </button>
                <button
                  onClick={() => handleNumberClick('6')}
                  className="bg-gray-100 hover:bg-gray-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-gray-700 transition-colors"
                >
                  6
                </button>
                <button
                  onClick={() => handleNumberClick('20')}
                  className="bg-blue-100 hover:bg-blue-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-blue-600 transition-colors"
                >
                  20
                </button>
              </div>

              {/* Row 3 */}
              <div className="grid grid-cols-4 gap-3 mb-3">
                <button
                  onClick={() => handleNumberClick('7')}
                  className="bg-gray-100 hover:bg-gray-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-gray-700 transition-colors"
                >
                  7
                </button>
                <button
                  onClick={() => handleNumberClick('8')}
                  className="bg-gray-100 hover:bg-gray-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-gray-700 transition-colors"
                >
                  8
                </button>
                <button
                  onClick={() => handleNumberClick('9')}
                  className="bg-gray-100 hover:bg-gray-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-gray-700 transition-colors"
                >
                  9
                </button>
                <button
                  onClick={handleBackspace}
                  className="bg-red-100 hover:bg-red-200 rounded-full w-14 h-14 flex items-center justify-center text-red-500 transition-colors"
                >
                  ×
                </button>
              </div>

              {/* Row 4 */}
              <div className="grid grid-cols-4 gap-3">
                <button
                  onClick={() => handleNumberClick('C')}
                  className="bg-red-100 hover:bg-red-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-red-500 transition-colors"
                >
                  C
                </button>
                <button
                  onClick={() => handleNumberClick('0')}
                  className="bg-gray-100 hover:bg-gray-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-gray-700 transition-colors"
                >
                  0
                </button>
                <button
                  onClick={() => handleNumberClick('.')}
                  className="bg-gray-100 hover:bg-gray-200 rounded-full w-14 h-14 flex items-center justify-center text-lg font-medium text-gray-700 transition-colors"
                >
                  .
                </button>
                <button
                  onClick={() => handleNumberClick('Add')}
                  className="bg-gray-700 hover:bg-gray-800 rounded-full w-14 h-14 flex items-center justify-center text-sm font-medium text-white transition-colors"
                >
                  Add
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 mt-8">
          <button
            onClick={handleApply}
            className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-full font-medium flex-1 transition-colors"
          >
            Apply
          </button>
          <button
            onClick={onClose}
            className="border border-orange-500 text-orange-500 hover:bg-orange-50 px-8 py-3 rounded-full font-medium flex-1 transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </CustomModal>
  );
};

export default DiscountModal;
