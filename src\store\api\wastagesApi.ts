import { baseApi } from './baseApi';

export interface Wastage {
  id: string;
  IngredientName: {
    _id: string;
    IngredientName: string;
  };
  userId: string;
  Quantity: number;
  ReasonOfWastage: string;
  PersonResponsible: string;
  Cost: number;
  Supplier: {
    SupplierName: string;
    _id: string;
    name: string;
  };
  LocationOfWastage: string;
  DisposalPlan: string;
  PreventiveMeasure: string;
  createdAt?: string;
  updatedAt?: string;
}

export const wastageApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getWastages: builder.query<Wastage[], string>({
      query: (userId) => `/StockWastage?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          IngredientName: item.IngredientName,
          userId: item.userId,
          Quantity: item.Quantity,
          ReasonOfWastage: item.ReasonOfWastage,
          PersonResponsible: item.PersonResponsible,
          Cost: item.Cost,
          Supplier: item.Supplier,
          LocationOfWastage: item.LocationOfWastage,
          DisposalPlan: item.DisposalPlan,
          PreventiveMeasure: item.PreventiveMeasure,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "Wastage" as const, id })),
              { type: "Wastage", id: "LIST" },
            ]
          : [{ type: "Wastage", id: "LIST" }],
    }),

    getWastage: builder.query<Wastage, string>({
      query: (id) => `/StockWastage/${id}`,
      transformResponse: (response: any) => ({
        id: response._id,
        IngredientName: response.IngredientName,
        userId: response.userId,
        Quantity: response.Quantity,
        ReasonOfWastage: response.ReasonOfWastage,
        PersonResponsible: response.PersonResponsible,
        Cost: response.Cost,
        Supplier: response.Supplier,
        LocationOfWastage: response.LocationOfWastage,
        DisposalPlan: response.DisposalPlan,
        PreventiveMeasure: response.PreventiveMeasure,
        createdAt: response.createdAt,
        updatedAt: response.updatedAt
      }),
      providesTags: (_result, _error, id) => [{ type: "Wastage", id }],
    }),

    deleteWastage: builder.mutation<void, string>({
      query: (id) => ({
        url: `/StockWastage/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            wastageApi.util.invalidateTags([
              { type: "Wastage", id },
              { type: "Wastage", id: "LIST" }
            ])
          );
        } catch (error) {
          console.error("Failed to delete stock wastage", error);
        }
      },
    }),

    postWastage: builder.mutation<any, any>({
      query: (data) => {
        console.log("Sending POST request with data:", data);
        return {
          url: "/StockWastage",
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: [{ type: "Wastage", id: "LIST" }],
    }),

    putWastage: builder.mutation<any, any>({
      query: (data) => ({
        url: `/StockWastage/${data._id}`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: (_result, _error, data) => [
        { type: "Wastage", id: data._id },
        { type: "Wastage", id: "LIST" },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetWastagesQuery,
  useGetWastageQuery,
  useDeleteWastageMutation,
  usePostWastageMutation,
  usePutWastageMutation,
} = wastageApi;