import { baseApi } from "./baseApi";

export const loyaltyApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getLoyaltyOffers: builder.query({
      query: () => {
        const userId = localStorage.getItem('userId');
        return {
          url: `/loyalty?userId=${userId}`,
          method: 'GET',
        };
      },
      providesTags: ['Loyalty'],
    }),
    
    createLoyaltyOffer: builder.mutation({
      query: (newOffer) => {
        const userId = localStorage.getItem('userId');
        return {
          url: '/loyalty',
          method: 'POST',
          body: { ...newOffer, userId },
        };
      },
      invalidatesTags: ['Loyalty'],
    }),
    
    updateLoyaltyOffer: builder.mutation({
      query: ({ id, ...updatedOffer }) => {
        return {
          url: `/loyalty/${id}`,
          method: 'PUT',
          body: { ...updatedOffer,  },
        };
      },
      invalidatesTags: ['Loyalty'],
    }),
    
    deleteLoyaltyOffer: builder.mutation({
      query: (_id) => {
        return {
          url: `/loyalty/${_id}`,
          method: 'DELETE',
        };
      },
      invalidatesTags: ['Loyalty'],
    }),
  }),
});

export const {
  useGetLoyaltyOffersQuery,
  useCreateLoyaltyOfferMutation,
  useUpdateLoyaltyOfferMutation,
  useDeleteLoyaltyOfferMutation,
} = loyaltyApi;