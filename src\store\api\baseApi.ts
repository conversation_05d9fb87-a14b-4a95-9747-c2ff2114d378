import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const baseApi = createApi({
  reducerPath: "api",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL,
    credentials: "include",
    prepareHeaders: (headers) => {
      const token = localStorage.getItem("token");
      if (token) {
        headers.set("Authorization", `Bearer ${token}`);
      }
      return headers;
    },
  }),
  endpoints: () => ({}),
  tagTypes: ["ParentCategory", "User", "Category", "Product", "IngredientCategory", "Ingredient", "Supplier", "UnitOfMeasurement", "Wastage", "Loyalty", "Customer", "EmployeeList", "Deductions", "Administration", "PaymentTypes", "Tax", "Customization", "Modifier", "Charges", "Subscriptions", "Customers", "Products", "SellerAccounts", "TableManagement", "SiteManagement", "EmailMarketing", "Coupon", "posOrderItems", "onlineOrderItems", "CustomerOrderItem",
    "Employees",
    "pos",
    "EmployeeTime",
    "LastMonthOrders",
    "AggregatedOrdersByDate",
    'pos',
    "AggregatedOrders",
  "PosKitchenOrders",
  "OnlineKitchenOrders"
  ]

});
