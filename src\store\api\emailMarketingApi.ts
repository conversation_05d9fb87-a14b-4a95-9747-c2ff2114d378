
import { baseApi } from "./baseApi";
import type { EmailMarketingItem } from '../slices/emailMarketingslice';

export const emailMarketingApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getEmailMarketing: builder.query<EmailMarketingItem[], string>({
      query: (userId) => `/emailMarketing?userId=${userId}`,
      providesTags: ["EmailMarketing"],
    }),
    
    createEmailMarketing: builder.mutation<EmailMarketingItem, Partial<EmailMarketingItem>>({
      query: (data) => ({
        url: `/emailMarketing`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["EmailMarketing"],
    }),
    
    deleteEmailMarketing: builder.mutation<void, string>({
      query: (id) => ({
        url: `/emailMarketing/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["EmailMarketing"],
    }),
  }),
});

export const {
  useGetEmailMarketingQuery,
  useCreateEmailMarketingMutation,
  useDeleteEmailMarketingMutation,
} = emailMarketingApi;