
import React, { useState, useEffect } from "react";
import { Hi<PERSON>rrowLeft, HiArrowRight } from "react-icons/hi";
// import SearchBar from "../SearchBar";
import { useGetCustomersQuery } from "../../store/api/pos/customer";
import CustomModal from "../CustomModal";
import EditCustomerModal from "./EditCustomerModal";
import AddCustomer from "../posModals/AddCustomer";
import { FiSearch } from "react-icons/fi";
import { useDispatch } from "react-redux";
import { setSelectedCustomer } from "../../store/slices/selectedcustomer";

const LoadCustomerModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onAddNewCustomer?: () => void;
  onSelectCustomer?: (customer: any) => void;
}> = ({ isOpen, onClose, onAddNewCustomer, onSelectCustomer }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [customerToEdit, setCustomerToEdit] = useState<any>(null);
  const [isAddCustomerModalOpen, setIsAddCustomerModalOpen] = useState(false);
  const itemsPerPage = 5;
  const dispatch = useDispatch()
  const userId = localStorage.getItem("userId") || "";
  const {
    data: customers = [],
    isLoading,
    error,
    refetch
  } = useGetCustomersQuery(userId);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      // Reset state when the main modal closes
      setSearchQuery("");
      setCurrentPage(1);
      setSelectedCustomerId(null);
    } else {
      // Refetch customers when modal opens
      refetch();
    }
  }, [isOpen, refetch]);

  // Reset edit modal state when it closes
  useEffect(() => {
    if (!isEditModalOpen) {
      setCustomerToEdit(null);
    }
  }, [isEditModalOpen]);

  // Filter customers based on search query
  const filteredCustomers = customers.filter((customer: any) =>
    `${customer.FirstName || customer.firstName || ''} ${customer.LastName || customer.lastName || ''} ${customer.Email || customer.email || ''} ${customer.Phone || customer.phone || ''}`
      .toLowerCase()
      .includes(searchQuery.toLowerCase())
  );

  // Calculate pagination
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedCustomers = filteredCustomers.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  const handleLoadCustomer = (customer: any) => {
    if (onSelectCustomer) {

      dispatch(setSelectedCustomer(customer))
    }
    onClose();
  };

  const handleRowClick = (customerId: string) => {
    setSelectedCustomerId(selectedCustomerId === customerId ? null : customerId);
  };

  const handleEditCustomer = (customer: any, e: React.MouseEvent) => {
    e.stopPropagation();
    setCustomerToEdit(customer);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    // Refetch customers after editing to get updated data
    refetch();
  };

  const handleAddNewCustomer = () => {
    setIsAddCustomerModalOpen(true);
    // Also call the prop callback if provided (for backward compatibility)
    if (onAddNewCustomer) {
      onAddNewCustomer();
    }
  };

  const handleCloseAddCustomerModal = () => {
    setIsAddCustomerModalOpen(false);
    // Refetch customers after adding to get updated data
    refetch();
  };

  // Footer component for the modal
  const footer = (
    <div className="flex justify-between py-2">
      <div className="flex">
        <button
          className="py-3 px-4 w-[120px] border border-gray-300 rounded-l-full flex items-center text-sm gap-2 text-gray-500 font-medium cursor-pointer"
          onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
          disabled={currentPage === 1}
        >
          <HiArrowLeft size={14} />
          Previous
        </button>
        <div className="px-5 py-3 border-t border-b border-gray-300 flex items-center font-semibold">
          {currentPage}
        </div>
        <button
          className="py-3 px-4 w-[120px] border border-gray-300 rounded-r-full text-sm flex items-center justify-end gap-2 text-gray-500 font-medium cursor-pointer"
          onClick={() => setCurrentPage((prev) => prev + 1)}
          disabled={paginatedCustomers.length < itemsPerPage}
        >
          Next
          <HiArrowRight size={14} />
        </button>
      </div>

      <div className="flex gap-2">
        <button
          className="px-4 py-2 w-[200px] border border-orange-500 text-orange-500 rounded-full font-semibold text-lg cursor-pointer"
          onClick={onClose}
        >
          Cancel
        </button>
        <button
          className="px-4 py-2 w-[200px] bg-orange-500 text-white rounded-full font-semibold text-lg cursor-pointer"
          onClick={handleAddNewCustomer}
        >
          Add New Customer
        </button>
      </div>
    </div>
  );

  return (
    <>
      <CustomModal
        isOpen={isOpen}
        onClose={onClose}
        title="Load Customer"
        width="max-w-6xl"
        footer={footer}
      >
        <div className="p-4">
          <div className="relative mb-4 border border-gray-300 rounded-lg overflow-hidden">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3">
              <FiSearch className="text-gray-500" size={18} />
            </div>
            <input
              type="text"
              placeholder="Search Customer"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="text-left text-gray-500 border-b border-b-gray-300">
                  <th className="py-3 px-4 text-sm font-medium">Customer ID</th>
                  <th className="py-3 px-4 text-sm font-medium">
                    Customer Name
                  </th>
                  <th className="py-3 px-4 text-sm font-medium">
                    Phone Number
                  </th>
                  <th className="py-3 px-4 text-sm font-medium">
                    Email Address
                  </th>
                  <th className="py-3 px-4 text-sm font-medium">
                    City Location
                  </th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan={5} className="text-center py-4">
                      Loading...
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan={5} className="text-center py-4 text-red-500">
                      Error loading customers
                    </td>
                  </tr>
                ) : paginatedCustomers.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="text-center py-4">
                      No customers found
                    </td>
                  </tr>
                ) : (
                  paginatedCustomers.map((customer: any) => (
                    <tr
                      key={customer._id || customer.id}
                      className={`border-b border-b-gray-300 hover:bg-gray-50 cursor-pointer ${selectedCustomerId === (customer._id || customer.id) ? "bg-gray-50" : ""
                        }`}
                      onClick={() => handleRowClick(customer._id || customer.id)}
                    >
                      <td className="py-3 px-4">{customer?.CustomerId || customer?.id}</td>
                      <td className="py-3 px-4">{`${customer?.FirstName || customer?.firstName || ''} ${customer?.LastName || customer?.lastName || ''}`}</td>
                      <td className="py-3 px-4">{customer?.Phone || customer?.phone}</td>
                      <td className="py-3 px-4">{customer?.Email || customer?.email}</td>
                      <td className="py-3 px-4">
                        {selectedCustomerId === (customer._id || customer.id) ? (
                          <div className="flex gap-2" onClick={(e) => e.stopPropagation()}>
                            <button
                              className="text-orange-500 cursor-pointer px-3 font-medium"
                              onClick={(e) => handleEditCustomer(customer, e)}
                            >
                              Edit info
                            </button>
                            <button
                              className="bg-orange-500 text-white px-3 py-1 rounded-full cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleLoadCustomer(customer);
                              }}
                            >
                              Load customer
                            </button>
                          </div>
                        ) : (
                          <span>{customer?.City || customer?.city} {customer?.Address}</span>
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </CustomModal>

      {/* Edit Customer Modal - Only render when open */}
      {isEditModalOpen && customerToEdit && (
        <EditCustomerModal
          isOpen={isEditModalOpen}
          onClose={handleCloseEditModal}
          customerData={customerToEdit}
        />
      )}

      {/* Add Customer Modal - Only render when open */}
      {isAddCustomerModalOpen && (
        <AddCustomer
          isOpen={isAddCustomerModalOpen}
          onClose={handleCloseAddCustomerModal}
        />
      )}
    </>
  );
};

export default LoadCustomerModal;
