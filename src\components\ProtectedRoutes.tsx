// src/routes/ProtectedRoute.tsx
import React from 'react';
import { useSelector } from 'react-redux';
import { Navigate } from 'react-router-dom';
import { selectIsAuthenticated } from '../store/slices/authSlice.ts';
import { useEffect } from 'react';
import {toast} from "react-toastify"

interface ProtectedRouteProps {
  children: React.ReactElement;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const isAuthenticated = useSelector(selectIsAuthenticated);

  useEffect(() => {
    if (!isAuthenticated) {
      toast.warning('Please login to access this page');
    }
  }, [isAuthenticated]);

  return isAuthenticated ? <>{children}</> : <Navigate to="/auth" replace />;
};


export default ProtectedRoute;
