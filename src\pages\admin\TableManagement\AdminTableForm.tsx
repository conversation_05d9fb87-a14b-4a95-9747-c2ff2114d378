import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  usePostTableMutation,
  usePutTableMutation,
  useGetTableQuery,
  useGetTablesQuery,
} from "../../../store/api/TableManagementApi";
import { useGetSitesQuery } from "../../../store/api/siteManagementApi";
import { toast } from "react-toastify";
import Swal from "sweetalert2";

// Define types for chair positions
interface ChairPosition {
  className: string;
  size: string;
  style?: React.CSSProperties;
}

const AdminTableForm = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const userId = localStorage.getItem("userId") || "";

  const [postTable, { isLoading: isCreating }] = usePostTableMutation();
  const [putTable, { isLoading: isUpdating }] = usePutTableMutation();

  const { data: tableData, isLoading: isLoadingTable } = useGetTableQuery(
    id || "",
    {
      skip: !isEditMode,
    }
  );

  const { data: sitesData, isLoading: isLoadingSites } = useGetSitesQuery(userId);
  const { data: allTablesData } = useGetTablesQuery(userId);

  const [formData, setFormData] = useState({
    tableName: "",
    seatingCapacity: "",
    tableNo: "",
    description: "",
    site: "",
    siteId: "",
    hasLampixDevice: false,
    isVisible: false,
  });

  useEffect(() => {
    if (isEditMode && tableData) {
      let siteNameValue = "";
      let siteIdValue = "";

      if (typeof tableData.location === "object" && tableData.location) {
        if ("siteName" in tableData.location) {
          siteNameValue = (tableData.location as { siteName: string }).siteName;
        }
        if ("_id" in tableData.location) {
          siteIdValue = (tableData.location as { _id: string })._id;
        }
      } else if (typeof tableData.location === "string") {
        const matchingSite = sitesData?.find(
          (site) => site._id === tableData.location
        );
        if (matchingSite) {
          siteNameValue = matchingSite.siteName;
          siteIdValue = matchingSite._id || "";
        } else {
          siteNameValue = String(tableData.location);
        }
      }

      setFormData({
        tableName: tableData.tableName || "",
        seatingCapacity: tableData.capacity?.toString() || "",
        tableNo: tableData.tableNo || "",
        description: tableData.description || "",
        site: siteNameValue,
        siteId: siteIdValue,
        hasLampixDevice:
          tableData.hasLampixDevice === true ||
          (typeof tableData.hasLampixDevice === "string" &&
            tableData.hasLampixDevice === "true"),
        isVisible: tableData.Status === "Available",
      });
    }
  }, [tableData, isEditMode, sitesData]);

  const handleChange = (e: { target: { name: any; value: any } }) => {
    const { name, value } = e.target;

    if (name === "site") {
      const selectedSite = sitesData?.find((site) => site.siteName === value);
      setFormData((prev) => ({
        ...prev,
        [name]: value,
        siteId: selectedSite?._id || "",
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleToggle = () => {
    setFormData((prev) => ({ ...prev, hasLampixDevice: !prev.hasLampixDevice }));
  };

  // Function to check site capacity
  const checkSiteCapacity = (siteId: string) => {
    if (!sitesData || !allTablesData) return { isExceeded: false, currentCount: 0, maxCapacity: 0 };

    const selectedSite = sitesData.find(site => site._id === siteId);
    const maxCapacity = selectedSite?.numberOfTables || 0;

    // Count existing tables for this site (excluding current table if editing)
    const currentTablesCount = allTablesData.filter(table => {
      const tableLocationId = typeof table.location === 'object' && table.location && '_id' in table.location 
        ? table.location._id 
        : table.location;
      
      // If editing, exclude current table from count
      if (isEditMode && table._id === id) {
        return false;
      }
      
      return tableLocationId === siteId;
    }).length;

    return {
      isExceeded: currentTablesCount >= maxCapacity,
      currentCount: currentTablesCount,
      maxCapacity
    };
  };

  const handleSubmit = async (e: { preventDefault: () => void }) => {
    e.preventDefault();

    try {
      if (!formData.tableName.trim()) {
        toast.error("Please enter table name");
        return;
      }

      if (!formData.seatingCapacity.trim()) {
        toast.error("Please enter seating capacity");
        return;
      }

      if (!formData.tableNo.trim()) {
        toast.error("Please enter table number");
        return;
      }

      if (!formData.site.trim() || !formData.siteId.trim()) {
        toast.error("Please select a site");
        return;
      }

      const capacity = parseInt(formData.seatingCapacity);
      if (isNaN(capacity) || capacity <= 0) {
        toast.error("Please enter a valid seating capacity");
        return;
      }

      // Check site capacity limit
      const capacityCheck = checkSiteCapacity(formData.siteId);
      
      if (capacityCheck.isExceeded) {
        await Swal.fire({
          icon: "error",
          title: "Site Capacity Full!",
          html: `
            <div style="text-align: left;">
              <p><strong>Site:</strong> ${formData.site}</p>
              <p><strong>Current Tables:</strong> ${capacityCheck.currentCount}</p>
              <p><strong>Maximum Capacity:</strong> ${capacityCheck.maxCapacity}</p>
              <br>
              <p style="color: #dc2626;">This site has reached its maximum table capacity. Please select a different site or remove existing tables first.</p>
            </div>
          `,
          confirmButtonColor: "#dc2626",
          confirmButtonText: "OK"
        });
        return;
      }

      const submitFormData = new FormData();

      if (isEditMode && id) {
        submitFormData.append("_id", id);
      } else {
        submitFormData.append("_id", "");
      }

      submitFormData.append("tableName", formData.tableName);
      submitFormData.append("capacity", formData.seatingCapacity);
      submitFormData.append("tableNo", formData.tableNo);
      submitFormData.append("description", formData.description || "");
      submitFormData.append("location", formData.siteId);
      submitFormData.append(
        "hasLampixDevice",
        formData.hasLampixDevice ? "true" : "false"
      );
      submitFormData.append("userId", userId);
      submitFormData.append(
        "Status",
        formData.isVisible ? "Available" : "Reserved"
      );

      if (isEditMode && id) {
        await putTable({ id, data: submitFormData }).unwrap();
        Swal.fire({
          icon: "success",
          title: "Success!",
          text: "Table updated successfully!",
          confirmButtonColor: "#6366f1",
        });
      } else {
        await postTable(submitFormData).unwrap();
        const newCapacityCheck = checkSiteCapacity(formData.siteId);
        
        Swal.fire({
          icon: "success",
          title: "Success!",
          html: `
            <div style="text-align: left;">
              <p><strong>Table added successfully!</strong></p>
              <br>
              <p><strong>Site:</strong> ${formData.site}</p>
              <p><strong>Tables Used:</strong> ${newCapacityCheck.currentCount + 1}/${newCapacityCheck.maxCapacity}</p>
              ${newCapacityCheck.currentCount + 1 >= newCapacityCheck.maxCapacity ? 
                '<p style="color: #f59e0b;"><strong>⚠️ Site capacity is now full!</strong></p>' : ''}
            </div>
          `,
          confirmButtonColor: "#6366f1",
        });
      }

      navigate("/admin/tables-management/tables");
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error(
        `Failed to ${isEditMode ? "update" : "add"} table. Please try again.`
      );
    }
  };

  const handleCancel = () => {
    navigate("/admin/tables-management/tables");
  };

  // Enhanced function to render chairs/seats around the table based on capacity
  const renderTableWithSeats = () => {
    const capacity = parseInt(formData.seatingCapacity) || 0;
    const tableNumber = formData.tableNo || "T-No";

    if (capacity === 0) {
      return (
        <div className="border border-gray-400 rounded-md p-6 w-40 h-24 flex items-center justify-center">
          <div className="bg-orange-100 rounded-full w-20 h-20 flex items-center justify-center">
            <span className="text-gray-700 font-semibold text-xl">
              {tableNumber}
            </span>
          </div>
        </div>
      );
    }

    // Enhanced chair rendering function
    const renderChairs = () => {
      const chairs: JSX.Element[] = [];
      
      if (capacity <= 8) {
        // Handle specific cases for 1-8 capacity
        const positions = getChairPositions(capacity);
        positions.forEach((pos, index) => {
          chairs.push(
            <div 
              key={`chair-${index}`} 
              className={`absolute ${pos.className}`}
              style={pos.style}
            >
              <div className={`bg-gray-50 rounded-md border border-gray-400 ${pos.size}`}></div>
            </div>
          );
        });
      } else {
        // For capacity > 8, distribute chairs evenly around the table
        const perimeter = [
          { side: 'top', count: Math.ceil(capacity / 4) },
          { side: 'right', count: Math.ceil((capacity - Math.ceil(capacity / 4)) / 3) },
          { side: 'bottom', count: Math.ceil((capacity - Math.ceil(capacity / 4) - Math.ceil((capacity - Math.ceil(capacity / 4)) / 3)) / 2) },
          { side: 'left', count: capacity - Math.ceil(capacity / 4) - Math.ceil((capacity - Math.ceil(capacity / 4)) / 3) - Math.ceil((capacity - Math.ceil(capacity / 4) - Math.ceil((capacity - Math.ceil(capacity / 4)) / 3)) / 2) }
        ];

        let chairIndex = 0;
        perimeter.forEach(({ side, count }) => {
          for (let i = 0; i < count; i++) {
            const position = getChairPositionForSide(side, i, count);
            chairs.push(
              <div 
                key={`chair-${chairIndex}`} 
                className={`absolute ${position.className}`}
                style={position.style}
              >
                <div className={`bg-gray-50 rounded-md border border-gray-400 ${position.size}`}></div>
              </div>
            );
            chairIndex++;
          }
        });
      }

      return chairs;
    };

    return (
      <div className="relative">
        <div className="border border-gray-400 rounded-md p-6 w-40 h-30 flex items-center justify-center">
          <div className="bg-orange-100 rounded-full w-20 h-20 flex items-center justify-center">
            <span className="text-gray-700 font-semibold text-xl">
              {tableNumber}
            </span>
          </div>
        </div>
        {renderChairs()}
      </div>
    );
  };

  // Helper function to get chair positions for specific capacities
  const getChairPositions = (capacity: number): ChairPosition[] => {
    const positions: ChairPosition[] = [];
    
    switch (capacity) {
      case 1:
        positions.push({ className: "-top-4 left-1/2 transform -translate-x-1/2", size: "w-10 h-3" });
        break;
      case 2:
        positions.push(
          { className: "-top-4 left-1/2 transform -translate-x-1/2", size: "w-10 h-3" },
          { className: "-bottom-4 left-1/2 transform -translate-x-1/2", size: "w-10 h-3" }
        );
        break;
      case 3:
        positions.push(
          { className: "-top-4 left-1/2 transform -translate-x-1/2", size: "w-10 h-3" },
          { className: "-bottom-4 left-1/2 transform -translate-x-1/2", size: "w-10 h-3" },
          { className: "-left-4 top-1/2 transform -translate-y-1/2", size: "w-3 h-10" }
        );
        break;
      case 4:
        positions.push(
          { className: "-top-4 left-1/2 transform -translate-x-1/2", size: "w-10 h-3" },
          { className: "-bottom-4 left-1/2 transform -translate-x-1/2", size: "w-10 h-3" },
          { className: "-left-4 top-1/2 transform -translate-y-1/2", size: "w-3 h-10" },
          { className: "-right-4 top-1/2 transform -translate-y-1/2", size: "w-3 h-10" }
        );
        break;
      case 5:
        positions.push(
          { className: "-top-4 left-1/2 transform -translate-x-1/2", size: "w-10 h-3" },
          { className: "-bottom-4 left-1/3 transform -translate-x-1/2", size: "w-8 h-3" },
          { className: "-bottom-4 right-1/3 transform translate-x-1/2", size: "w-8 h-3" },
          { className: "-left-4 top-1/2 transform -translate-y-1/2", size: "w-3 h-10" },
          { className: "-right-4 top-1/2 transform -translate-y-1/2", size: "w-3 h-10" }
        );
        break;
      case 6:
        positions.push(
          { className: "-top-4 left-1/3 transform -translate-x-1/2", size: "w-8 h-3" },
          { className: "-top-4 right-1/3 transform translate-x-1/2", size: "w-8 h-3" },
          { className: "-left-4 top-1/2 transform -translate-y-1/2", size: "w-3 h-10" },
          { className: "-right-4 top-1/2 transform -translate-y-1/2", size: "w-3 h-10" },
          { className: "-bottom-4 left-1/3 transform -translate-x-1/2", size: "w-8 h-3" },
          { className: "-bottom-4 right-1/3 transform translate-x-1/2", size: "w-8 h-3" }
        );
        break;
      case 7:
        positions.push(
          { className: "-top-4 left-1/3 transform -translate-x-1/2", size: "w-8 h-3" },
          { className: "-top-4 right-1/3 transform translate-x-1/2", size: "w-8 h-3" },
          { className: "-left-4 top-1/2 transform -translate-y-1/2", size: "w-3 h-10" },
          { className: "-right-4 top-1/2 transform -translate-y-1/2", size: "w-3 h-10" },
          { className: "-bottom-4 left-1/4 transform -translate-x-1/2", size: "w-7 h-3" },
          { className: "-bottom-4 left-1/2 transform -translate-x-1/2", size: "w-7 h-3" },
          { className: "-bottom-4 right-1/4 transform translate-x-1/2", size: "w-7 h-3" }
        );
        break;
      case 8:
        positions.push(
          { className: "-top-4 left-1/3 transform -translate-x-1/2", size: "w-8 h-3" },
          { className: "-top-4 right-1/3 transform translate-x-1/2", size: "w-8 h-3" },
          { className: "-left-4 top-1/3 transform -translate-y-1/2", size: "w-3 h-8" },
          { className: "-left-4 bottom-1/3 transform translate-y-1/2", size: "w-3 h-8" },
          { className: "-right-4 top-1/3 transform -translate-y-1/2", size: "w-3 h-8" },
          { className: "-right-4 bottom-1/3 transform translate-y-1/2", size: "w-3 h-8" },
          { className: "-bottom-4 left-1/3 transform -translate-x-1/2", size: "w-8 h-3" },
          { className: "-bottom-4 right-1/3 transform translate-x-1/2", size: "w-8 h-3" }
        );
        break;
    }
    
    return positions;
  };

  // Helper function for chairs on specific sides (for capacity > 8)
  const getChairPositionForSide = (side: string, index: number, total: number): ChairPosition => {
    const spacing = 100 / (total + 1);
    const position = spacing * (index + 1);
    
    switch (side) {
      case 'top':
        return {
          className: "-top-4",
          style: { left: `${position}%`, transform: 'translateX(-50%)' },
          size: "w-8 h-3"
        };
      case 'bottom':
        return {
          className: "-bottom-4",
          style: { left: `${position}%`, transform: 'translateX(-50%)' },
          size: "w-8 h-3"
        };
      case 'left':
        return {
          className: "-left-4",
          style: { top: `${position}%`, transform: 'translateY(-50%)' },
          size: "w-3 h-8"
        };
      case 'right':
        return {
          className: "-right-4",
          style: { top: `${position}%`, transform: 'translateY(-50%)' },
          size: "w-3 h-8"
        };
      default:
        return { className: "", style: {}, size: "w-8 h-3" };
    }
  };

  if ((isEditMode && isLoadingTable) || isLoadingSites) {
    return (
      <div className="flex justify-center items-start bg-white h-screen pt-[38vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 rounded-lg shadow-sm w-full bg-gray-50 min-h-screen">
      <div className="flex items-center mb-6 p-2 rounded-xl bg-white border border-gray-200">
        <button
          className="flex items-center text-gray-700"
          onClick={handleCancel}
          type="button"
        >
          <svg
            className="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
          <span className="text-xl font-medium">
            {isEditMode ? "Edit Table" : "Add Table"}
          </span>
        </button>
      </div>

      <form onSubmit={handleSubmit} className="bg-white rounded-xl ">
        <div className="border border-gray-200 rounded-xl mb-6">
          <div className="bg-orange-50 px-4 py-3 rounded-xl mb-2">
            <h2 className="text-md font-medium text-gray-800">Table Details</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 p-3">
            <div>
              <label
                htmlFor="tableName"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Table Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="tableName"
                name="tableName"
                placeholder="Enter Table Name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.tableName}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <label
                htmlFor="seatingCapacity"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Seating Capacity <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                id="seatingCapacity"
                name="seatingCapacity"
                placeholder="Enter Seating Capacity"
                min="1"
                max="20"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.seatingCapacity}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <label
                htmlFor="tableNo"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Table No <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="tableNo"
                name="tableNo"
                placeholder="Enter Table No"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.tableNo}
                onChange={handleChange}
                required
              />
            </div>
            <div>
              <label
                htmlFor="site"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Site Information <span className="text-red-500">*</span>
              </label>
              <select
                id="site"
                name="site"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.site}
                onChange={handleChange}
                required
              >
                <option value="">Select site</option>
                {sitesData &&
                  sitesData.map((site) => {
                    const capacityCheck = checkSiteCapacity(site._id || "");
                    const isDisabled = !isEditMode && capacityCheck.isExceeded;
                    
                    return (
                      <option 
                        key={site._id} 
                        value={site.siteName}
                        disabled={isDisabled}
                        style={{ 
                          color: isDisabled ? '#9ca3af' : 'inherit',
                          backgroundColor: isDisabled ? '#f3f4f6' : 'inherit'
                        }}
                      >
                        {site.siteName} ({capacityCheck.currentCount}/{capacityCheck.maxCapacity})
                        {isDisabled ? ' - FULL' : ''}
                      </option>
                    );
                  })}
              </select>
              <input type="hidden" name="siteId" value={formData.siteId} />
              
              {/* Show capacity info for selected site */}
              {formData.siteId && (
                <div className="mt-2 text-sm">
                  {(() => {
                    const capacityCheck = checkSiteCapacity(formData.siteId);
                    const isNearCapacity = capacityCheck.currentCount >= capacityCheck.maxCapacity - 1;
                    
                    return (
                      <div className={`p-2 rounded-md ${
                        capacityCheck.isExceeded 
                          ? 'bg-red-50 text-red-700 border border-red-200' 
                          : isNearCapacity 
                            ? 'bg-yellow-50 text-yellow-700 border border-yellow-200'
                            : 'bg-green-50 text-green-700 border border-green-200'
                      }`}>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {capacityCheck.isExceeded ? '⚠️' : isNearCapacity ? '⚡' : '✅'}
                          </span>
                          <span>
                            Tables: {capacityCheck.currentCount}/{capacityCheck.maxCapacity}
                            {capacityCheck.isExceeded ? ' (Full)' : 
                             isNearCapacity ? ' (Almost Full)' : ' (Available)'}
                          </span>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}
            </div>

            <div>
              <label
                htmlFor="description"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Description
              </label>
              <input
                type="text"
                id="description"
                name="description"
                placeholder="Enter Table Description"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.description}
                onChange={handleChange}
              />
            </div>
          </div>
        </div>
        
        <div className="border border-gray-100 mt-2 rounded-2xl">
          <h2 className="text-base bg-orange-50 p-3 font-medium text-gray-800 mb-4">Permissions</h2>
           
          <div className="flex items-center p-2">
            <label className="inline-flex relative items-center cursor-pointer">
              <input 
                type="checkbox" 
                className="sr-only peer"
                id="hasLampixDevice"
                name="hasLampixDevice"
                checked={formData.hasLampixDevice}
                onChange={handleToggle}
              />
              <div className={`
                w-11 h-6 bg-gray-200 rounded-full peer 
                peer-checked:after:translate-x-full 
                peer-checked:after:border-white 
                after:content-[''] after:absolute after:top-0.5 after:left-[2px] 
                after:bg-white after:border-gray-300 after:border after:rounded-full 
                after:h-5 after:w-5 after:transition-all 
                ${formData.hasLampixDevice ? 'bg-orange-500' : ''}
              `}></div>
            </label>
            <span className="ml-3 text-sm font-medium text-gray-700">Visibility</span>
          </div>
        </div>
        
        <div className="mt-3 rounded-xl border border-gray-200">
          <h2 className="text-md font-medium text-gray-800 bg-orange-50 p-3 rounded-xl mb-3">
            Table Preview - Seating Capacity: {formData.seatingCapacity || 0}
          </h2>
          <div className="flex justify-center mb-6 py-8">
            {renderTableWithSeats()}
          </div>
        </div>

        <div className="flex justify-end space-x-4 mt-3">
          <button
            type="button"
            onClick={handleCancel}
            className="border border-orange-500 cursor-pointer text-orange-500 hover:bg-orange-50 py-2 px-10 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isCreating || isUpdating}
            className={`bg-orange-500 ${
              isCreating || isUpdating ? "opacity-70" : "hover:bg-orange-600"
            } text-white py-2 px-10 cursor-pointer rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500`}
          >
            {isEditMode
              ? isUpdating
                ? "Updating..."
                : "Update Table"
              : isCreating
              ? "Adding..."
              : "Add Table"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AdminTableForm;