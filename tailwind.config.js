/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  safelist: [
    'text-orange',
    'text-natural',
    'bg-orange',
    'bg-natural',
    'border-orange',
    'border-natural',
  ],
  theme: {
    extend: {
      fontFamily: {
        roboto: ['Roboto', 'sans-serif'],  
      },
      colors: {
        orange: "#FF5C00",
        lightBlack: "#19191C",
        black: "#000000",
        lightWhite: "#F8F9FD",
        white: "#ffffff",
        green: "#13C91B",
        natural: "#828487",
      },
      backgroundImage: {
        'primary-gradient': "linear-gradient(to right, rgba(255, 171, 24, 1), rgba(255, 33, 151, 1))",
      },
    },
  },
  plugins: [],
}
