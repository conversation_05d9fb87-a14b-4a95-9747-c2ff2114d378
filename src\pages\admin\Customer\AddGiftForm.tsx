import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronDown } from 'lucide-react';
import { useGetCustomersQuery, usePutCustomerMutation } from "../../../store/api/customerApi";
import { useNavigate, useParams } from 'react-router-dom';
import Swal from 'sweetalert2';

interface Customer {
  _id: string;
  CustomerId: string;
  FirstName: string;
  LastName: string;
  Email: string;
  Phone: string;
  Address: string;
  City: string;
  State: string;
  isActive: boolean;
  CustomerLoyalty?: {
    CardNo?: string;
    StartDate?: string | Date;
    ExpiresIn?: string | Date;
    LastVisit?: string | Date;
    ActivateCard?: boolean;
    creditLimits?: number;
  };
}

interface GiftCardFormData {
  customerId: string;
  cardNumber: string;
  validThru: string; // Will store date as "YYYY-MM-DD"
  firstName: string;
  lastName: string;
  email: string;
  contactNumber: string;
  address: string;
  city: string;
  state: string;
  existingCredit: number;
  newCredit: number | null;
  totalCredit: number;
  isActive: boolean;
  selectedCustomerId: string; // Store the _id of the selected customer
}

export default function GiftCardForm() {
  const { id } = useParams();
  const isEditMode = !!id;
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [lastIssuedNumber, setLastIssuedNumber] = useState(0);
  const [formData, setFormData] = useState<GiftCardFormData>({
    customerId: '',
    cardNumber: '',
    validThru: '',
    firstName: '',
    lastName: '',
    email: '',
    contactNumber: '',
    address: '',
    city: '',
    state: '',
    existingCredit: 0,
    newCredit: null,
    totalCredit: 0,
    isActive: false,
    selectedCustomerId: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Fetch customers data from API
  const { data: customersData, isLoading, refetch } = useGetCustomersQuery();
  
  // Update customer mutation
  const [updateCustomer] = usePutCustomerMutation();
  
  const customers = customersData || [];

  console.log(customers);
  
  // Set default validThru date for new cards
  useEffect(() => {
    if (!isEditMode) {
      setFormData(prev => ({
        ...prev,
        validThru: calculateExpiryDate()
      }));
    }
  }, [isEditMode]);

  // Populate form with customer data in edit mode
  useEffect(() => {
    if (isEditMode && customersData && customersData.length > 0) {
      // Find the customer by ID
      const customer = customersData.find(cust => cust._id === id);
      
      if (customer) {
        setFormData({
          customerId: customer.CustomerId || '',
          cardNumber: customer.CustomerLoyalty?.CardNo || '',
          validThru: customer.CustomerLoyalty?.ExpiresIn ? 
                    new Date(customer.CustomerLoyalty.ExpiresIn).toISOString().split('T')[0] :
                    calculateExpiryDate(),
          firstName: customer.FirstName || '',
          lastName: customer.LastName || '',
          email: customer.Email || '',
          contactNumber: customer.Phone || '',
          address: customer.Address || '',
          city: customer.City || '',
          state: customer.State || '',
          existingCredit: customer.CustomerLoyalty?.creditLimits || 0,
          newCredit: null,
          totalCredit: customer.CustomerLoyalty?.creditLimits || 0,
          isActive: customer.CustomerLoyalty?.ActivateCard || false,
          selectedCustomerId: customer._id || ''
        });
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Customer not found',
          position: 'center',
          showConfirmButton: true,
        });
        navigate(-1);
      }
    }
  }, [customersData, id, isEditMode]);

  const existingCardNumbers = customersData
    ?.map(c => c.CustomerLoyalty?.CardNo)
    .filter(Boolean) as string[] || [];

  // Function to generate the next card number in sequence
  const generateCardNumber = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    
    // Extract all existing sequence numbers
    const sequenceNumbers = existingCardNumbers
      .map(cardNo => {
        const parts = cardNo.split('-');
        return parts.length > 3 ? parseInt(parts[3], 10) : 0;
      })
      .filter(num => !isNaN(num));
    
    // Find the highest existing number
    const maxSequence = sequenceNumbers.length > 0 ? Math.max(...sequenceNumbers) : 0;
  
    const nextNumber = String(maxSequence + 1).padStart(4, '0');
    
    return `${year}-${month}${day}-0000-${nextNumber}`;
  };

  const handleCustomerSelect = (customer: Customer) => {
    const cardNumber = generateCardNumber();
    
    setFormData({
      ...formData,
      customerId: customer.CustomerId,
      firstName: customer.FirstName,
      lastName: customer.LastName,
      email: customer.Email,
      contactNumber: customer.Phone,
      address: customer.Address,
      city: customer.City,
      state: customer.State,
      cardNumber: cardNumber,
      selectedCustomerId: customer._id,
      existingCredit: 0,
      newCredit: 0,
      totalCredit: 0
    });
    
    setIsDropdownOpen(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    if (name === 'newCredit') {
      const newCreditValue = value === '' ? null : parseFloat(value);
      setFormData({
        ...formData,
        newCredit: newCreditValue,
        totalCredit: formData.existingCredit + (newCreditValue || 0)
      });
    } else {
      setFormData({
        ...formData,
        [name]: type === 'checkbox' ? checked : value
      });
    }
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      validThru: e.target.value
    });
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!formData.validThru) errors.validThru = "Valid thru date is required";
    if (!isEditMode && !formData.selectedCustomerId) errors.customerId = "Customer ID is required";

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.MouseEvent) => {
    e.preventDefault();
    if (!validateForm()) {
      Swal.fire({
        icon: 'error',
        title: 'Please fill in all required fields',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 2000,
        timerProgressBar: true
      });
      return;
    }

    if (!formData.selectedCustomerId) {
      Swal.fire({
        icon: 'error',
        title: 'Please select a customer',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 2000,
        timerProgressBar: true
      });
      return;
    }
    
    try {
      setIsSubmitting(true);

      // Find customer in customers list to preserve existing data
      const customerToUpdate = customersData?.find(c => c._id === formData.selectedCustomerId);
      
      if (!customerToUpdate && isEditMode) {
        Swal.fire({
          icon: 'error',
          title: 'Customer not found',
          position: 'center',
          showConfirmButton: true,
        });
        return;
      }

      // Calculate the final credit amount
      const finalCreditAmount = isEditMode ? 
        formData.existingCredit + (formData.newCredit || 0) : 
        formData.newCredit || 0;

      // Preserve existing CustomerLoyalty data if it exists
      const existingLoyalty = customerToUpdate?.CustomerLoyalty || {};

      // Correctly structure the update request with _id as a top-level property
      const customerUpdate = {
        _id: formData.selectedCustomerId,
        CustomerLoyalty: {
          ...existingLoyalty,
          CardNo: formData.cardNumber || existingLoyalty.CardNo,
          StartDate: existingLoyalty.StartDate || new Date().toISOString(),
          ExpiresIn: new Date(formData.validThru).toISOString(),
          LastVisit: new Date().toISOString(),
          ActivateCard: formData.isActive,
          creditLimits: finalCreditAmount
        }
      };

      // Call the update customer mutation
      await updateCustomer(customerUpdate).unwrap();
   
      await refetch();
      
      // Update the lastIssuedNumber for the next card
      if (!isEditMode) {
        setLastIssuedNumber(lastIssuedNumber + 1);
      }
      
      Swal.fire({
        icon: 'success',
        title: isEditMode ? 'Gift card updated successfully!' : 'Gift card issued successfully!',
        position: 'center',
        showConfirmButton: true,
      });
      // Navigate away
      navigate(-1);
    } catch (error) {
      console.error("Error updating customer with gift card:", error);
     
Swal.fire({
  icon: 'error',
  title: 'Failed to process gift card',
  text: 'Please try again.',
  position: 'center',
  showConfirmButton: true,
});
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    if (!isEditMode) {
      setFormData({
        customerId: '',
        cardNumber: '',
        validThru: calculateExpiryDate(),
        firstName: '',
        lastName: '',
        email: '',
        contactNumber: '',
        address: '',
        city: '',
        state: '',
        existingCredit: 0,
        newCredit: 0,
        totalCredit: 0,
        isActive: false,
        selectedCustomerId: ''
      });
    }
  };
  
  const navigate = useNavigate();

  const handleCancel = () => {
    resetForm();
    navigate(-1);
  };

  const customersWithoutCards = customersData?.filter(customer => 
    !customer.CustomerLoyalty?.CardNo
  ) || [];

  // Calculate expiry date (3 years from today) in YYYY-MM-DD format for date input
  const calculateExpiryDate = () => {
    const today = new Date();
    const expiryDate = new Date(today);
    expiryDate.setFullYear(today.getFullYear() + 3);
    const year = expiryDate.getFullYear();
    const month = String(expiryDate.getMonth() + 1).padStart(2, '0');
    const day = String(expiryDate.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  };

  // Format date for display on card (DD/MM/YYYY)
  const formatDateForDisplay = (dateString: string) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
  };

  // Calculate today's date for display
  const formatTodayDate = () => {
    const today = new Date();
    const day = String(today.getDate()).padStart(2, '0');
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const year = today.getFullYear();
    
    return `${day}/${month}/${year}`;
  };

  // Show loading state while fetching customer data
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 rounded-lg shadow-sm p-4">
      {/* Header */}
      <div className="flex items-center mb-6 bg-white rounded-2xl p-4 border border-gray-200">
        <button 
          onClick={handleCancel}
          className="mr-2 cursor-pointer text-gray-700">
          <ChevronLeft size={20} />
        </button>
        <h1 className="text-lg font-medium">{isEditMode ? 'Edit Gift Card' : 'Add Gift Card'}</h1>
      </div>

      <div className="border border-gray-200 rounded-2xl bg-white">
        <div className="mb-6">
          <div className="bg-orange-50 px-4 py-2 rounded-t-2xl mb-4">
            <h2 className="text-gray-800 font-medium">Gift Card Details</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-3">
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Customer ID<span className="text-red-500">*</span>
              </label>
              {isEditMode ? (
                <input
                  type="text"
                  value={formData.customerId}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50"
                  readOnly
                />
              ) : (
                <>
                  <div 
                    className="w-full border border-gray-300 rounded-md px-3 py-2 flex items-center justify-between cursor-pointer"
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  >
                    <span className={formData.customerId ? "" : "text-gray-400"}>
                      {formData.customerId || "Select Customer ID"}
                    </span>
                    <ChevronDown size={16} className="text-gray-400" />
                  </div>
                  {formErrors.customerId && <p className="text-red-500 text-xs mt-1">{formErrors.customerId}</p>}
                  
                  {isDropdownOpen && (
                    <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                      {isLoading ? (
                        <div className="px-4 py-2 text-gray-500">Loading customers...</div>
                      ) : customersWithoutCards.length > 0 ? (
                        customersWithoutCards.map((customer: Customer) => (
                          <div 
                            key={customer._id}
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => handleCustomerSelect(customer)}
                          >
                            {customer.CustomerId} - {customer.FirstName} {customer.LastName}
                          </div>
                        ))
                      ) : (
                        <div className="px-4 py-2 text-gray-500">
                          {customersData?.length ? 
                            "All customers already have gift cards" : 
                            "No customers found"}
                        </div>
                      )}
                    </div>
                  )}
                </>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Card Number
              </label>
              <input
                type="text"
                name="cardNumber"
                value={formData.cardNumber}
                className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50"
                readOnly
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Valid Thru<span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="date"
                  name="validThru"
                  value={formData.validThru}
                  onChange={handleDateChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                  required
                  min={new Date().toISOString().split('T')[0]} // Set min date to today
                />
              </div>
              {formErrors.validThru && <p className="text-red-500 text-xs mt-1">{formErrors.validThru}</p>}
            </div>
          </div>
        </div>

        {/* Personal Details Section */}
        <div className="mb-6">
          <div className="bg-orange-50 px-4 py-2 rounded-md mb-4">
            <h2 className="text-gray-800 font-medium">Personal Details</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name<span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="firstName"
                placeholder="Enter First Name"
                value={formData.firstName}
                className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50"
                readOnly
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name<span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="lastName"
                placeholder="Enter Last Name"
                value={formData.lastName}
                className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50"
                readOnly
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email Address<span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                name="email"
                placeholder="Enter Email Address"
                value={formData.email}
                className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50"
                readOnly
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Number<span className="text-red-500">*</span>
              </label>
              <input
                type="tel"
                name="contactNumber"
                placeholder="Enter Contact Number"
                value={formData.contactNumber}
                className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50"
                readOnly
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Address<span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="address"
                placeholder="Enter Address"
                value={formData.address}
                className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50"
                readOnly
              />
            </div>
          </div>
        </div>

        {/* Balance Section */}
        <div className="mb-6">
          <div className="bg-orange-50 px-4 py-2 rounded-md mb-4">
            <h2 className="text-gray-800 font-medium">Balance</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-3">
            {isEditMode && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Credit<span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  name="existingCredit"
                  value={formData.existingCredit}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50"
                  readOnly
                />
              </div>
            )}
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {isEditMode ? 'Add New Credit' : 'Credit'}<span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="newCredit"
                value={formData.newCredit ?? ''}
                onChange={handleChange}
                placeholder="0"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>
            
            {isEditMode && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Balance<span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  value={formData.totalCredit}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50"
                  readOnly
                />
              </div>
            )}
          </div>
        </div>

        {/* Card Image Section */}
        <div className="mb-6">
          <div className="bg-orange-50 px-4 py-2 rounded-md mb-4">
            <h2 className="text-gray-800 font-medium">Card Image</h2>
          </div>
          
          <div className="flex justify-center mb-4">
            <div className="relative w-96 h-56 bg-purple-900 rounded-lg overflow-hidden">
              {/* Card Design */}
              <div className="p-6 flex flex-col justify-between h-full">
                <div>
                  <div className="text-blue-400 text-xl font-bold inline">
                    Patr<span className="text-orange-500">on</span><span className="text-white">Works</span>
                  </div>
                </div>
                
                <div>
                  <p className="text-white text-sm mb-1">
                    {formData.firstName && formData.lastName 
                      ? `${formData.firstName} ${formData.lastName}` 
                      : 'Cardholder Name'}
                  </p>
                  <p className="text-white text-xl font-bold mb-2">
                    {formData.cardNumber || '0000-0000-0000-0000'}
                  </p>
                  <div className="flex text-white text-xs">
                    <div className="mr-6">
                      <p>VALID<br />FROM</p>
                      <p>{formatTodayDate()}</p>
                    </div>
                    <div>
                      <p>VALID<br />THRU</p>
                      <p>{formData.validThru ? formatDateForDisplay(formData.validThru) : formatDateForDisplay(calculateExpiryDate())}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Red ribbon and "GIFT CARD" text */}
              <div className="absolute right-0 top-0 bottom-0 w-16 flex flex-col items-center">
                <div className="h-full w-1 bg-red-500 mr-2"></div>
                <div className="absolute top-1/2 right-0 transform -translate-y-1/2 rotate-90 origin-right">
                  <p className="text-white font-bold tracking-wider">GIFT CARD</p>
                </div>
                <div className="absolute top-1/3 right-6">
                  <div className="w-10 h-10 bg-red-500 rounded-full transform rotate-45"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Permissions Section */}
        <div className="mb-6">
          <div className="bg-orange-50 px-4 py-2 rounded-md mb-4">
            <h2 className="text-gray-800 font-medium">Permissions</h2>
          </div>
          
          <div className="flex items-center p-3">
            <label className="inline-flex items-center cursor-pointer">
              <div className={`w-11 h-6 relative rounded-full transition-colors duration-200 ease-in-out ${formData.isActive ? 'bg-orange-500' : 'bg-gray-300'}`}>
                <input
                  type="checkbox"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleChange}
                  className="opacity-0 w-0 h-0"
                />
                <span className={`absolute top-1 left-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${formData.isActive ? 'transform translate-x-5' : ''}`}></span>
              </div>
              <span className="ml-2 text-sm font-medium">Activate</span>
            </label>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 m-4">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 border cursor-pointer border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            className={`px-4 py-2 ${isSubmitting ? 'bg-orange-300' : 'bg-orange-500 hover:bg-orange-600'} text-white rounded-md cursor-pointer`}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Processing...' : isEditMode ? 'Update Card' : 'Issue Card'}
          </button>
        </div>
      </div>
    </div>
  );
}