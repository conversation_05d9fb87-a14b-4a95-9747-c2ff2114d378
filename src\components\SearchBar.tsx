import { FiSearch } from "react-icons/fi";

type Props = {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

const SearchBar: React.FC<Props> = ({ value, onChange }) => {
  return (
    <div className="relative w-full py-3 px-3 lg-px-0 ">
      <input
        type="text"
        value={value}
        onChange={onChange}
        placeholder="Search Menu"
        className="w-full  py-2 text-lg rounded-full focus:outline-none border lg:border-none md:px-16 px-10"
      />
      <FiSearch className="absolute top-1/2 left-8 transform -translate-y-1/2 text-gray-400" size={22} color="#19191c" />
    </div>
  );
};

export default SearchBar;
