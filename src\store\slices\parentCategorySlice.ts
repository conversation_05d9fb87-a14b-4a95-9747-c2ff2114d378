import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import { productParentCategoryApi } from '../api/parentCategoryApi'; // API import

// Interface for Parent Category Data
export interface ParentCategory {
  id: string;
  name: string;
  subCategoriesCount: number;
}

interface ParentCategoryState {
  parentCategories: ParentCategory[];
  isLoading: boolean;
  error: string | null;
}

const initialState: ParentCategoryState = {
  parentCategories: [],
  isLoading: false,
  error: null,
};

const parentCategorySlice = createSlice({
  name: 'parentCategory',
  initialState,
  reducers: {
    setParentCategories: (state, action: PayloadAction<ParentCategory[]>) => {
      state.parentCategories = action.payload;
    },
    clearParentCategories: (state) => {
      state.parentCategories = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET
      .addMatcher(
        productParentCategoryApi.endpoints.getParentCategories.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        productParentCategoryApi.endpoints.getParentCategories.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.parentCategories = payload;
        }
      )
      .addMatcher(
        productParentCategoryApi.endpoints.getParentCategories.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch categories';
        }
      )

      // DELETE
      .addMatcher(
        productParentCategoryApi.endpoints.deleteParentCategory.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        productParentCategoryApi.endpoints.deleteParentCategory.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const categoryId = action.meta.arg.originalArgs as string;
          state.parentCategories = state.parentCategories.filter(
            (category) => category.id !== categoryId
          );
        }
      )
      .addMatcher(
        productParentCategoryApi.endpoints.deleteParentCategory.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete category';
        }
      )

      // POST
      .addMatcher(
        productParentCategoryApi.endpoints.postParentCategory.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        productParentCategoryApi.endpoints.postParentCategory.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.parentCategories.push({
            id: payload._id,
            name: payload.name,
            subCategoriesCount: 0,
          });
        }
      )
      .addMatcher(
        productParentCategoryApi.endpoints.postParentCategory.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create category';
        }
      )
// put
      .addMatcher(
        productParentCategoryApi.endpoints.putParentCategory.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        productParentCategoryApi.endpoints.putParentCategory.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          const updated = {
            id: payload.data._id,
            name: payload.data.name,
            subCategoriesCount: 0,
          };
          state.parentCategories = state.parentCategories.map(cat =>
            cat.id === updated.id ? updated : cat
          );
        }
      )
      .addMatcher(
        productParentCategoryApi.endpoints.putParentCategory.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update category';
        }
      );
  },
});

// Export Actions
export const { setParentCategories, clearParentCategories } = parentCategorySlice.actions;

// Selectors
export const selectParentCategories = (state: RootState) => state.parentCategory.parentCategories;
export const selectIsLoading = (state: RootState) => state.parentCategory.isLoading;
export const selectError = (state: RootState) => state.parentCategory.error;

// Export Reducer
export default parentCategorySlice.reducer;
