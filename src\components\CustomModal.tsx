import { type ReactNode, useRef } from "react";
import { IoMdClose } from "react-icons/io";

export interface ModalProps {
  isOpen: boolean;
  title?: string;
  onClose: () => void;
  children: ReactNode;
  footer?: ReactNode;
  width?: string;
  zIndex?: number; // Add zIndex prop to control stacking order
}
const CustomModal: React.FC<ModalProps> = ({
  isOpen,
  title,
  onClose,
  children,
  footer,
  width = "max-w-3xl",
  zIndex = 50, // Default z-index is 50
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // No need for useEffect to handle clicks outside
  // We'll handle this directly in the onClick of the backdrop

  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center bg-black/50`}
      style={{ zIndex }}
      onClick={(e) => {
        // Only close if clicking directly on the backdrop (not on modal content)
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        ref={modalRef}
        className={`bg-white rounded-xl shadow-lg w-full ${width} mx-4 flex flex-col max-h-[90vh]`}
      >
        <div className="flex items-center justify-between px-6 py-4">
          <h2 className="text-xl font-bold">{title}</h2>
          <button
            onClick={onClose}
            className="cursor-pointer rounded-full p-1 transition-colors hover:bg-gray-100"
          >
            <IoMdClose className="w-6 h-6 text-gray-700" />
          </button>
        </div>
        <div className="flex-1 overflow-y-auto">
          {children}
        </div>
        {footer && (
          <div className="px-6 pb-4">
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomModal;
