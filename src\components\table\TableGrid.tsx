import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { addTable, removeTable, selectSelectedTables } from '../../store/slices/selectedTablesSlice';
import { IoInformationCircleOutline } from 'react-icons/io5';

interface Table {
  id: string;
  status: 'available' | 'reserved' | 'billed' | 'available-soon';
  selected?: boolean;
  capacity?: number;
  tableName?: string;
  tableno: string
}

interface TableGridProps {
  tables: Table[];
  onTableSelect: (tableId: string) => void;
  selectedTable?: string;
}

const TableGrid: React.FC<TableGridProps> = ({ tables, onTableSelect }) => {
  const dispatch = useDispatch();
  const selectedTables = useSelector(selectSelectedTables);

  const getTableStatusStyle = (status: Table['status']) => {
    switch (status) {
      case 'available':
        return { bg: 'bg-blue-50', text: 'text-blue-500', border: 'border-blue-100' };
      case 'reserved':
        return { bg: 'bg-orange-50/70', text: 'text-orange-400', border: 'border-orange-100' };
      case 'billed':
        return { bg: 'bg-green-50', text: 'text-green-500', border: 'border-green-100' };
      case 'available-soon':
        return { bg: 'bg-yellow-50', text: 'text-yellow-500', border: 'border-yellow-100' };
      default:
        return { bg: 'bg-gray-50', text: 'text-gray-500', border: 'border-gray-100' };
    }
  };

  const handleTableClick = (table: Table) => {
    onTableSelect(table.id);
    if (selectedTables.find(t => t.id === table.id)) {
      dispatch(removeTable(table.id));
    } else {
      dispatch(addTable({
        id: table.id,
        name: table.tableName || table.id,
        number: table.tableno
      }));
    }
  };

  return (
    <div className="grid grid-cols-3 gap-8 p-6 bg-gray-50">
      {tables.map((table) => {
        const isSelected = selectedTables.find(t => t.id === table.id) !== undefined;
        const statusStyle = getTableStatusStyle(table.status);
        const isReserved = table.status === 'reserved';
        const capacity = table.capacity || 4;

        // Dynamic width
        const baseWidth = 160;
        const extraPerSeat = 16;
        const containerWidth = baseWidth + Math.max(0, capacity - 4) * extraPerSeat;

        // Chair distribution
        const base = Math.floor(capacity / 4);
        const remainder = capacity % 4;
        const chairDistribution = { top: base, bottom: base, left: base, right: base };
        const order = ['top', 'bottom', 'left', 'right'] as const;
        for (let i = 0; i < remainder; i++) {
          chairDistribution[order[i]]++;
        }

        return (
          <div
            key={table.id}
            className={`relative rounded-lg p-6 bg-white ${isSelected ? 'border-2 border-orange-500' : 'border border-gray-200'} transition-all`}
            style={{ width: `${containerWidth}px` }}
            onClick={() => handleTableClick(table)}
          >
            {/* Reserved icon */}
            {isReserved && (
              <div className="absolute top-2 right-2 text-red-500">
                <IoInformationCircleOutline size={18} />
              </div>
            )}

            {/* Chairs: Top */}
            {Array.from({ length: chairDistribution.top }).map((_, i) => (
              <div
                key={`top-${i}`}
                className="absolute w-6 h-2 border border-gray-400 rounded"
                style={{
                  top: '-16px',
                  left: `${(100 / (chairDistribution.top + 1)) * (i + 1)}%`,
                  transform: 'translateX(-50%)',
                }}
              />
            ))}
            {/* Chairs: Bottom */}
            {Array.from({ length: chairDistribution.bottom }).map((_, i) => (
              <div
                key={`bottom-${i}`}
                className="absolute w-6 h-2 border border-gray-400 rounded"
                style={{
                  bottom: '-16px',
                  left: `${(100 / (chairDistribution.bottom + 1)) * (i + 1)}%`,
                  transform: 'translateX(-50%)',
                }}
              />
            ))}
            {/* Chairs: Left */}
            {Array.from({ length: chairDistribution.left }).map((_, i) => (
              <div
                key={`left-${i}`}
                className="absolute w-2 h-6 border border-gray-400 rounded"
                style={{
                  left: '-16px',
                  top: `${(100 / (chairDistribution.left + 1)) * (i + 1)}%`,
                  transform: 'translateY(-50%)',
                }}
              />
            ))}
            {/* Chairs: Right */}
            {Array.from({ length: chairDistribution.right }).map((_, i) => (
              <div
                key={`right-${i}`}
                className="absolute w-2 h-6 border border-gray-400 rounded"
                style={{
                  right: '-16px',
                  top: `${(100 / (chairDistribution.right + 1)) * (i + 1)}%`,
                  transform: 'translateY(-50%)',
                }}
              />
            ))}

            {/* Main rectangular table with circular badge inside */}
            <div
              className={`mx-auto h-12 flex items-center justify-center relative ${statusStyle.bg} ${statusStyle.border} border rounded-full`}

            >
              <div className={` rounded-full flex items-center justify-center ${statusStyle.text} ${statusStyle.bg} bg-white border ${statusStyle.border}`}>
                <span className="font-semibold text-md">{table.tableName}</span>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default TableGrid;
