import React, { useState, useEffect, useRef } from "react";
import CustomModal from "../../CustomModal";
import clsx from "clsx";

interface BillDemonstrationProps {
  isOpen: boolean;
  onClose: () => void;
}

interface DenominationRow {
  amount: number;
  quantity: string;
  total: string;
}

const BillDemonstration: React.FC<BillDemonstrationProps> = ({ isOpen, onClose }) => {
  const [denominations, setDenominations] = useState<DenominationRow[]>([
    { amount: 0.01, quantity: "", total: "" },
    { amount: 0.05, quantity: "", total: "" },
    { amount: 0.10, quantity: "", total: "" },
    { amount: 0.25, quantity: "", total: "" },
    { amount: 1.00, quantity: "", total: "" },
    { amount: 2.00, quantity: "", total: "" },
    { amount: 5.00, quantity: "", total: "" },
    { amount: 10.00, quantity: "", total: "" },
    { amount: 20.00, quantity: "", total: "" },
    { amount: 50.00, quantity: "", total: "" },
    { amount: 100.00, quantity: "", total: "" },
  ]);

  const [totalCash, setTotalCash] = useState<number>(0);
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Calculate total cash whenever denominations change
  useEffect(() => {
    const sum = denominations.reduce((acc, row) => {
      const rowTotal = row.total ? parseFloat(row.total) : 0;
      return acc + rowTotal;
    }, 0);

    setTotalCash(sum);
  }, [denominations]);

  const handleQuantityChange = (index: number, value: string) => {
    const newDenominations = [...denominations];
    newDenominations[index].quantity = value;

    // Calculate the total for this row
    const quantity = value === "" ? 0 : parseFloat(value);
    const rowTotal = (quantity * newDenominations[index].amount).toFixed(2);
    newDenominations[index].total = rowTotal;

    setDenominations(newDenominations);
  };

  const handleKeypadClick = (button: string) => {
    if (focusedIndex === null) return;

    const currentValue = denominations[focusedIndex].quantity;

    switch (button) {
      case 'C':
        // Clear the current input
        handleQuantityChange(focusedIndex, "");
        break;
      case 'X':
        // Delete the last character
        if (currentValue.length > 0) {
          handleQuantityChange(focusedIndex, currentValue.slice(0, -1));
        }
        break;
      case 'Add':
        // Move focus to the next input
        if (focusedIndex < denominations.length - 1) {
          const nextIndex = focusedIndex + 1;
          inputRefs.current[nextIndex]?.focus();
          setFocusedIndex(nextIndex);
        }
        break;
      case '10':
        // Set value to 10
        handleQuantityChange(focusedIndex, "10");
        break;
      case '20':
        // Set value to 20
        handleQuantityChange(focusedIndex, "20");
        break;
      case '.':
        // Add decimal point if not already present
        if (!currentValue.includes('.')) {
          handleQuantityChange(focusedIndex, currentValue + '.');
        }
        break;
      default:
        // Add the number to the current value
        handleQuantityChange(focusedIndex, currentValue + button);
        break;
    }
  };

  const numberPadButtons = [
    ['1', '2', '3', '10'],
    ['4', '5', '6', '20'],
    ['7', '8', '9', 'X'],
    ['C', '0', '.', 'Add'],
  ];

  const footer = (
    <div className="flex gap-4 justify-end">
      <button
        onClick={onClose}
        className="px-10 py-2 border text-orange border-orange text-xl font-semibold rounded-full cursor-pointer"
      >
        Cancel
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Bill Demonstration"
      width="max-w-6xl"
      footer={footer}
    >
      <div className="p-6 flex gap-8 h-[70vh] overflow-hidden">
        {/* Left side with fixed header and scrollable content */}
        <div className="flex-1 flex flex-col h-full overflow-hidden">
          {/* Fixed header - outside the scrollable area */}
          <div className="flex gap-4 mb-2 text-xs text-natural">
            <div className="w-36">
              Demonstration
            </div>
            <div className="flex-1">
              Quantity
            </div>
            <div className="flex-1">
              Total
            </div>
          </div>

          {/* Scrollable denominations list - with overflow-y-auto and overflow-x-hidden */}
          <div className="overflow-y-auto overflow-x-hidden flex-1 pr-2">
            <div className="space-y-4">
              {denominations.map((row, index) => (
                <div key={index} className="flex gap-4">
                  <div className="w-36 bg-[#E4E4E4] p-2 px-5 text-center rounded-lg">
                    ${row.amount.toFixed(2)}
                  </div>
                  <input
                    type="text"
                    inputMode="numeric"
                    value={row.quantity}
                    onChange={(e) => handleQuantityChange(index, e.target.value)}
                    onFocus={() => setFocusedIndex(index)}
                    ref={el => {
                      inputRefs.current[index] = el;
                    }}
                    placeholder="Enter Amount"
                    className={clsx(
                      "flex-1 p-2 border rounded-xl focus:outline-none",
                      focusedIndex === index ? "border-orange" : "border-[#E4E4E4]"
                    )}
                  />
                  <input
                    type="text"
                    value={row.total}
                    readOnly
                    placeholder="Total"
                    className="flex-1 p-2 border border-[#E4E4E4] rounded-xl focus:outline-none"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right side - fixed, not scrolling */}
        <div className="flex-1 flex flex-col">
          <div className="grid grid-cols-4 gap-2">
            {numberPadButtons.map((row, rowIndex) => (
              row.map((button, colIndex) => (
                <button
                  key={`${rowIndex}-${colIndex}`}
                  onClick={() => handleKeypadClick(button)}
                  className={clsx(
                    "h-16 rounded-3xl font-bold text-lg",
                    button === 'X' && "text-orange",
                    button === 'C' && "text-orange-600",
                    button === 'Add' && "text-black font-bold",
                    button === '10' && "bg-blue-100 text-blue-600",
                    button === '20' && "bg-blue-100 text-blue-600",
                    !['X', 'C', 'Add', '10', '20'].includes(button) && "bg-gray-100"
                  )}
                >
                  {button}
                </button>
              ))
            ))}
          </div>

          <div className="mt-8 bg-gray-50 p-4 rounded-lg">
            <div className="flex flex-col font-bold justify-between items-center">
              <span className="block text-2xl">Total Cash on Hand</span>
              <span className="block text-2xl font-bold text-orange">
                $ {totalCash.toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default BillDemonstration;