import { useState, useEffect } from 'react';
import { Search, Edit, Trash2, ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { 
  useGetLoyaltyOffersQuery,
  useDeleteLoyaltyOfferMutation,
} from '../../../store/api/loyaltyOfferAPi';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

interface LoyaltyOffer {
  _id: string;
  productName: {
    name: string;
  };
  offerQty: number | string;
  description: string;
  active: string;
  userId: string;
}

export default function LoyaltyOffers() {
  const userId = localStorage.getItem('userId') || '';
  const { 
    data: loyaltyOffers = [], 
    isLoading, 
    isError, 
    refetch 
  } = useGetLoyaltyOffersQuery(userId);
  
  const [deleteLoyaltyOffer, { isLoading: isDeleting }] = useDeleteLoyaltyOfferMutation();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredOffers, setFilteredOffers] = useState<LoyaltyOffer[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [offerToDelete, setOfferToDelete] = useState<string | null>(null);

  const itemsPerPage = 5;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const displayedOffers = filteredOffers.slice(startIndex, endIndex);
  const totalPages = Math.ceil(filteredOffers.length / itemsPerPage);

  const navigate = useNavigate();

  // Initialize filtered offers when data loads
  useEffect(() => {
    setFilteredOffers(loyaltyOffers);
  }, [loyaltyOffers]);

  // Search functionality
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredOffers(loyaltyOffers);
    } else {
      const filtered = loyaltyOffers.filter((offer: LoyaltyOffer) => 
        (offer.productName?.name?.toLowerCase().includes(searchQuery.toLowerCase()) || 
        offer.description.toLowerCase().includes(searchQuery.toLowerCase())
      ));
      setFilteredOffers(filtered);
    }
    setCurrentPage(1);
  }, [searchQuery, loyaltyOffers]);

  const handleEdit = (_id: string) => {
    navigate(`/admin/marketing/loyalty-offers/loyalty-offer-form/${_id}`);
  };

  const handleDeleteClick = (_id: string) => {
    setOfferToDelete(_id);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (offerToDelete) {
      try {
        await deleteLoyaltyOffer(offerToDelete).unwrap();
        toast.success('Loyalty offer deleted successfully!');
        refetch();
      } catch (error) {
        toast.error('Failed to delete loyalty offer');
      } finally {
        setShowDeleteModal(false);
        setOfferToDelete(null);
      }
    }
  };

  const handleCreateOffer = () => {
    navigate('/admin/marketing/loyalty-offers/loyalty-offer-form');
  };

  return (
    <div className="bg-gray-50 min-h-screen p-4">
      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-50">
          <div className="bg-white p-8 rounded-xl shadow-xl w-full max-w-md text-center relative">
            <div className="flex justify-center mb-4">
              <div className="bg-orange-100 text-orange-500 p-4 rounded-full">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01M12 5c.512 0 1.023.195 1.414.586C13.805 6.977 14 7.488 14 8s-.195 1.023-.586 1.414A1.993 1.993 0 0112 10a1.993 1.993 0 01-1.414-.586A1.993 1.993 0 0110 8c0-.512.195-1.023.586-1.414A1.993 1.993 0 0112 5z"
                  />
                </svg>
              </div>
            </div>
            <h3 className="text-2xl font-semibold text-gray-800">
              Are you sure?
            </h3>
            <p className="mt-2 text-gray-600">
              Do you want to delete this loyalty offer?
            </p>
            <div className="mt-6 flex justify-center gap-4">
              <button
                onClick={confirmDelete}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-5 py-2 rounded-md font-medium"
                disabled={isDeleting}
              >
                {isDeleting ? 'Deleting...' : 'Yes, delete it!'}
              </button>
              <button
                onClick={() => setShowDeleteModal(false)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-5 py-2 rounded-md font-medium"
              >
                No, cancel!
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Header Section */}
      <div className="flex items-center justify-between md:flex-row flex-col mb-6 border border-gray-200 p-2 rounded-2xl bg-white">
        <h1 className="text-3xl p-3 font-bold text-gray-800">Loyalty Offers</h1>
        <div className="flex md:flex-row flex-col-reverse gap-4">
          <form onSubmit={(e) => e.preventDefault()} className="relative">
            <input
              type="text"
              placeholder="Search Loyalty Offer"
              className="pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <button type="submit" className="absolute right-3 top-2.5">
              <Search className="text-gray-400 w-5 h-5" />
            </button>
          </form>
          <button 
            onClick={handleCreateOffer}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-orange-600 transition-colors"
          >
            Create Loyalty Offer
            <Plus size={18} />
          </button>
        </div>
      </div>

      {/* Main Content */}
      {isLoading ? (
        <div className="text-center py-10">
          <p className="text-gray-600">Loading loyalty offers...</p>
        </div>
      ) : isError ? (
        <div className="text-center py-10">
          <p className="text-red-600">Error loading loyalty offers. Please try again.</p>
          <button 
            onClick={() => refetch()}
            className="mt-2 px-4 py-2 bg-orange-500 text-white rounded-lg"
          >
            Retry
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-lg overflow-x-auto">
          {displayedOffers.length === 0 ? (
            <div className="text-center py-10">
              <p className="text-gray-600">No loyalty offers found.</p>
            </div>
          ) : (
            <table className="w-full">
              <thead className="bg-orange-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-600">No</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-600">Product Name</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-600">Quantity</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-600">Description</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-600">Status</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-600">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {displayedOffers.map((offer: LoyaltyOffer, index: number) => (
                  <tr key={offer._id} className="hover:bg-orange-50">
                    <td className="px-6 py-4 text-sm text-gray-700">{startIndex + index + 1}</td>
                    <td className="px-6 py-4 text-sm text-gray-700">{offer?.productName?.name || 'N/A'}</td>
                    <td className="px-6 py-4 text-sm text-gray-700">{offer?.offerQty || 0}</td>
                    <td className="px-6 py-4 text-sm text-gray-700">{offer?.description || 'No description'}</td>
                    <td className="px-6 py-4 text-sm">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        offer?.active === "true" 
                          ? "bg-green-100 text-green-600" 
                          : "bg-orange-100 text-orange-600"
                      }`}>
                        {offer?.active === "true" ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-right text-sm space-x-3">
                      <button 
                        onClick={() => handleEdit(offer._id)}
                        className="text-blue-500 hover:text-blue-600"
                      >
                        <Edit size={18} />
                      </button>
                      <button 
                        onClick={() => handleDeleteClick(offer._id)}
                        className="text-red-500 hover:text-red-700"
                        disabled={isDeleting}
                      >
                        <Trash2 size={18} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      )}
      
      {/* Pagination */}
      {totalPages > 0 && (
        <div className="flex justify-start items-start space-x-2 p-4">
          <button
            className={`p-1 rounded-md border border-gray-200 ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-orange-200'}`}
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft size={18} className="text-gray-600" />
          </button>

          {/* Page numbers */}
          {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
            const pageNum = i + 1;
            return (
              <button
                key={pageNum}
                className={`w-8 h-8 rounded-md flex items-center justify-center ${pageNum === currentPage ? "bg-orange-500 text-white" : "border border-gray-200 text-gray-600 hover:bg-orange-100"}`}
                onClick={() => setCurrentPage(pageNum)}
              >
                {pageNum}
              </button>
            );
          })}

          {totalPages > 5 && (
            <>
              <span className="px-2">...</span>
              <button
                className="w-8 h-8 rounded-md border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-orange-100"
                onClick={() => setCurrentPage(totalPages)}
              >
                {totalPages}
              </button>
            </>
          )}

          <button
            className={`p-1 rounded-md border border-gray-200 ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-orange-200'}`}
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            <ChevronRight size={18} className="text-gray-600" />
          </button>
        </div>
      )}
    </div>
  );
}