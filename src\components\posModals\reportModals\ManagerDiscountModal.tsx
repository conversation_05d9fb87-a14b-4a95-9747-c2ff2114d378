import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import CustomModal from '../../CustomModal';

interface ManagerDiscountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (amount: string, reason: string, discountType: string) => void;
  title?: string;
  initialAmount?: string;
  initialReason?: string;
  initialDiscountType?: string;
}

const ManagerDiscountModal: React.FC<ManagerDiscountModalProps> = ({
  isOpen,
  onClose,
  onApply,
  title = "Manager Discount",
  initialAmount = '',
  initialReason = '',
  initialDiscountType = 'percentage'
}) => {
  const [discountAmount, setDiscountAmount] = useState('');
  const [reason, setReason] = useState('');
  const [discountType, setDiscountType] = useState('percentage');

  // Initialize form when modal opens with existing values
  useEffect(() => {
    if (isOpen) {
      setDiscountAmount(initialAmount);
      setReason(initialReason);
      setDiscountType(initialDiscountType);
    }
  }, [isOpen, initialAmount, initialReason, initialDiscountType]);

  const handleNumberClick = (num: string) => {
    if (num === 'C') {
      setDiscountAmount('');
    } else if (num === '.') {
      if (!discountAmount.includes('.')) {
        setDiscountAmount(prev => prev + '.');
      }
    } else if (num === 'x') {
      setDiscountAmount(prev => prev.slice(0, -1));
    } else {
      const newValue = discountAmount + num;
      const numericValue = parseFloat(newValue);

      // For percentage, limit to 100%
      if (discountType === 'percentage') {
        if (!isNaN(numericValue) && numericValue <= 100) {
          setDiscountAmount(newValue);
        }
      } else {
        // For fixed amount, no specific limit
        setDiscountAmount(newValue);
      }
    }
  };

  const handleApply = () => {
    onApply(discountAmount, reason, discountType);
    onClose();
  };

  const handleCancel = () => {
    // Reset to initial values when canceling
    setDiscountAmount(initialAmount);
    setReason(initialReason);
    setDiscountType(initialDiscountType);
    onClose();
  };

  const handleClear = () => {
    // Clear all discount values
    setDiscountAmount('');
    setReason('');
    setDiscountType('percentage');
    onApply('', '', 'percentage');
    onClose();
  };

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      width="max-w-2xl"
      zIndex={1000000}
      footer={null}
    >
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          {/* Left Column - Enter Discount Amount */}
          <div>
            <input
              type="text"
              value={discountAmount + (discountType === 'percentage' ? '%' : '')}
              onChange={(e) => setDiscountAmount(e.target.value)}
              placeholder={`Enter Discount ${discountType === 'percentage' ? 'Percentage' : 'Amount'}`}
              className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none text-gray-700"
              readOnly
            />
          </div>

          {/* Right Column - Select Reason */}
          <div>
            <select
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none appearance-none text-gray-500"
            >
              <option value="">Select Reason</option>
              <option value="Customer Complaint">Customer Complaint</option>
              <option value="Loyalty Program">Loyalty Program</option>
              <option value="Promotion">Promotion</option>
              <option value="Manager Approval">Manager Approval</option>
              <option value="Product Issue">Product Issue</option>
            </select>
          </div>
        </div>

        {/* Discount Type Selector */}
        <div className="mb-6">
          <div className="flex gap-2">
            <button
              onClick={() => setDiscountType('percentage')}
              className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${discountType === 'percentage'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
            >
              Percentage (%)
            </button>
            <button
              onClick={() => setDiscountType('fixed')}
              className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${discountType === 'fixed'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
            >
              Fixed Amount ($)
            </button>
          </div>
        </div>

        {/* Number Pad */}
        <div className="grid grid-cols-4 gap-2">
          {/* Row 1 */}
          <button
            onClick={() => handleNumberClick('1')}
            className="bg-gray-50 hover:bg-gray-100 rounded-full py-4 text-lg font-medium text-gray-800"
          >
            1
          </button>
          <button
            onClick={() => handleNumberClick('2')}
            className="bg-gray-50 hover:bg-gray-100 rounded-full py-4 text-lg font-medium text-gray-800"
          >
            2
          </button>
          <button
            onClick={() => handleNumberClick('3')}
            className="bg-gray-50 hover:bg-gray-100 rounded-full py-4 text-lg font-medium text-gray-800"
          >
            3
          </button>
          <button
            onClick={() => handleNumberClick('10')}
            className="bg-blue-100 hover:bg-blue-200 rounded-full py-4 text-lg font-medium text-blue-600"
          >
            10
          </button>

          {/* Row 2 */}
          <button
            onClick={() => handleNumberClick('4')}
            className="bg-gray-50 hover:bg-gray-100 rounded-full py-4 text-lg font-medium text-gray-800"
          >
            4
          </button>
          <button
            onClick={() => handleNumberClick('5')}
            className="bg-gray-50 hover:bg-gray-100 rounded-full py-4 text-lg font-medium text-gray-800"
          >
            5
          </button>
          <button
            onClick={() => handleNumberClick('6')}
            className="bg-gray-50 hover:bg-gray-100 rounded-full py-4 text-lg font-medium text-gray-800"
          >
            6
          </button>
          <button
            onClick={() => handleNumberClick('20')}
            className="bg-blue-100 hover:bg-blue-200 rounded-full py-4 text-lg font-medium text-blue-600"
          >
            20
          </button>

          {/* Row 3 */}
          <button
            onClick={() => handleNumberClick('7')}
            className="bg-gray-50 hover:bg-gray-100 rounded-full py-4 text-lg font-medium text-gray-800"
          >
            7
          </button>
          <button
            onClick={() => handleNumberClick('8')}
            className="bg-gray-50 hover:bg-gray-100 rounded-full py-4 text-lg font-medium text-gray-800"
          >
            8
          </button>
          <button
            onClick={() => handleNumberClick('9')}
            className="bg-gray-50 hover:bg-gray-100 rounded-full py-4 text-lg font-medium text-gray-800"
          >
            9
          </button>
          <button
            onClick={() => handleNumberClick('x')}
            className="bg-red-100 hover:bg-red-200 rounded-full py-4 flex items-center justify-center text-red-500"
          >
            <X size={24} />
          </button>

          {/* Row 4 */}
          <button
            onClick={() => handleNumberClick('C')}
            className="bg-red-50 hover:bg-red-100 rounded-full py-4 text-lg font-medium text-red-500"
          >
            C
          </button>
          <button
            onClick={() => handleNumberClick('0')}
            className="bg-gray-50 hover:bg-gray-100 rounded-full py-4 text-lg font-medium text-gray-800"
          >
            0
          </button>
          <button
            onClick={() => handleNumberClick('.')}
            className="bg-gray-50 hover:bg-gray-100 rounded-full py-4 text-lg font-medium text-gray-800"
          >
            .
          </button>
          <div></div> {/* Empty cell for alignment */}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between gap-3 mt-6">
          <button
            onClick={handleApply}
            className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-full font-medium flex-1"
          >
            Apply
          </button>
          {(initialAmount || initialReason) && (
            <button
              onClick={handleClear}
              className="bg-red-50 hover:bg-red-100 text-red-600 px-8 py-3 rounded-full font-medium border border-red-300"
            >
              Clear
            </button>
          )}
          <button
            onClick={handleCancel}
            className="bg-white hover:bg-gray-100 text-gray-700 px-8 py-3 rounded-full font-medium border border-gray-300 flex-1"
          >
            Cancel
          </button>
        </div>
      </div>
    </CustomModal>
  );
};

export default ManagerDiscountModal;