import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import Navbar from "../components/Navbar";
import TableHeader from "../components/table/TableHeader";
import TableGrid from "../components/table/TableGrid";
import FloorSelector from "../components/table/FloorSelector";
import { FaRegClipboard } from "react-icons/fa";
import { IoClose } from "react-icons/io5";
import ReservationModal from "../components/table/ReservationModal";
import RunningOrdersModal from "../components/table/RunningOrdersModal";
import { useGetSitesQuery, useGetTablesQuery } from "../store/api/pos/tableapi";
import { removeTable, selectSelectedTables } from "../store/slices/selectedTablesSlice";


interface LocationState {
  openReservationModal?: boolean;
  openRunningOrdersModal?: boolean;
}

const Table: React.FC = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate()
  const userId = localStorage.getItem("userId");
  const { data: tableData, isLoading: isTablesLoading } = useGetTablesQuery(userId || "");
  const { data: siteData, isLoading: isSitesLoading } = useGetSitesQuery(userId || "");
  const isLoading = isTablesLoading || isSitesLoading;
  const selectedTables = useSelector(selectSelectedTables);
  const [activeTab, setActiveTab] = useState<
    "Tables" | "Reservation" | "Running Orders"
  >("Tables");
  const [selectedFloor, setSelectedFloor] = useState("");
  const [selectedTable, setSelectedTable] = useState<string | undefined>();
  const [isReservationModalOpen, setIsReservationModalOpen] = useState(false);
  const [isRunningOrdersModalOpen, setIsRunningOrdersModalOpen] = useState(false);


  const state = location.state as LocationState;

  useEffect(() => {
    if (state?.openReservationModal) {
      setIsReservationModalOpen(true);
      setActiveTab("Reservation");
      window.history.replaceState({}, document.title);
    }

    if (state?.openRunningOrdersModal) {
      setIsRunningOrdersModalOpen(true);
      setActiveTab("Running Orders");
      window.history.replaceState({}, document.title);
    }
  }, [state]);

  // Set the first floor as selected when site data is loaded
  useEffect(() => {
    if (siteData && siteData.length > 0 && !selectedFloor) {
      console.log("Setting initial floor:", siteData[0]);
      setSelectedFloor(siteData[0]._id);
    }
  }, [siteData, selectedFloor]);

  // Log data for debugging
  useEffect(() => {
    if (siteData) {
      console.log("Site data loaded:", siteData);
    }
    if (tableData) {
      console.log("Table data loaded:", tableData);
    }
  }, [siteData, tableData]);

  const handleNewReservation = () => {
    setIsReservationModalOpen(false);
  };

  // Map site data to floors format expected by FloorSelector
  const floors = siteData ? siteData.map(site => ({
    id: site._id,
    name: site.siteName
  })) : [];

  // Map table data to tables format expected by TableGrid
  // Filter tables by selected floor (site)
  const tables = tableData ? tableData
    .filter(table => table.location && table.location._id === selectedFloor)
    .map(table => {
      // Map API status to the expected status values
      let status: "available" | "reserved" | "billed" | "available-soon" = "available";

      if (table.Status) {
        const lowerStatus = table.Status.toLowerCase();
        if (lowerStatus.includes("reserved")) {
          status = "reserved";
        } else if (lowerStatus.includes("billed")) {
          status = "billed";
        } else if (lowerStatus.includes("soon") || lowerStatus.includes("cleaning")) {
          status = "available-soon";
        }
      }

      return {
        id: table._id,
        status,
        capacity: table.capacity,
        tableName: table.tableName,
        tableno: table.tableNo
      };
    }) : [];

  // We're now using Redux for selected tables, so we don't need this anymore
  // The selectedTables variable is coming from the Redux store

  return (
    <div className="h-screen flex flex-col">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 right-0 z-10">
        <Navbar />
      </div>

      {/* Content Area */}
      <div className="mt-20"> {/* Adjust mt-16 based on your Navbar height */}
        <TableHeader activeTab={activeTab} onTabChange={setActiveTab} />
        {/* Main Content with Fixed Floor Selector */}
        <div className="relative flex h-[calc(100vh-180px)]">
          {/* Scrollable Table Grid */}
          <div className="flex-1 overflow-y-auto pr-72"> {/* Added right padding to account for fixed floor selector */}
            {isLoading ? (
              <div className="flex flex-col justify-center items-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
                <p className="text-lg font-medium text-gray-700">Loading tables...</p>
                <p className="text-sm text-gray-500 mt-2">Please wait while we prepare your tables</p>
              </div>
            ) : (
              <TableGrid
                tables={tables}
                selectedTable={selectedTable}
                onTableSelect={setSelectedTable}
              />
            )}
          </div>

          {/* Fixed Floor Selector */}
          <div className="fixed right-0 top-[136px] w-64 h-[calc(100vh-180px)] bg-white border-l border-[#E4E4E4] overflow-y-auto">
            {isLoading ? (
              <div className="flex flex-col justify-center items-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
                <p className="text-lg font-medium text-gray-700">Loading floors...</p>
                <p className="text-sm text-gray-500 mt-2">Please wait</p>
              </div>
            ) : (
              <FloorSelector
                floors={floors}
                selectedFloor={selectedFloor}
                onFloorSelect={setSelectedFloor}
              />
            )}
          </div>
        </div>
      </div>

      {/* Fixed Bottom Panel */}
      <div className="fixed w-[90%] md:w-[80%] bottom-5 left-5 md:left-10 right-0 p-4 bg-white rounded-lg shadow-lg">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 md:gap-0">
          <div className="flex items-center gap-4 md:gap-8 w-full md:w-auto">
            <FaRegClipboard size={20} className="text-orange" />
            <div className="flex flex-col md:flex-row items-start md:items-center gap-2 md:gap-4 w-full md:w-auto">
              <div className="flex flex-col pr-0 md:pr-5 border-b md:border-r md:border-b-0 border-[#E4E4E4] pb-2 md:pb-0 w-full md:w-auto">
                <span className="mr-2 font-semibold">Table</span>
                <span className="text-natural">Order #ABC123WXQXAVA</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {selectedTables.map((table) => (
                  <div key={table.id} className="flex relative items-center bg-gray-50 text-center rounded-md px-3 py-2 border border-[#E4E4E4]">
                    <span className="text-sm font-medium text-gray-700">{table.name}</span>
                    <IoClose
                      className="text-white absolute top-0 right-0 bg-[#ea4f3b] rounded-full cursor-pointer ml-2"
                      size={16}
                      onClick={(e) => {
                        e.stopPropagation();
                        dispatch(removeTable(table.id));
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
          <button onClick={() => navigate("/pos/pos-dashboard")} className="bg-[#FF5C00] text-white text-lg font-bold px-6 py-3 rounded-full cursor-pointer w-full md:w-auto">
            Place Order
          </button>
        </div>
      </div>

      <ReservationModal
        isOpen={isReservationModalOpen}
        onClose={() => setIsReservationModalOpen(false)}
        onNewReservation={handleNewReservation}
      />

      <RunningOrdersModal
        isOpen={isRunningOrdersModalOpen}
        onClose={() => setIsRunningOrdersModalOpen(false)}
      />
    </div>
  );
};

export default Table;