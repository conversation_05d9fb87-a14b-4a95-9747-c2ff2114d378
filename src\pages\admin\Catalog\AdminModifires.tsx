import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import {
  useGetModifiersByUserQuery,
  useDeleteModifierMutation,
  type Modifier,
  type ModifierItem,
  type Property,
} from "../../../store/api/modifier";
import { Edit, Trash2, ChevronUp, ChevronDown } from "lucide-react";
import Swal from "sweetalert2";

const Modifiers: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  const {
    data: modifiersData,
    isLoading,
    error,
    refetch,
  } = useGetModifiersByUserQuery();
  const [deleteModifier, { isLoading: isDeleting }] =
    useDeleteModifierMutation();

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleAddModifier = () => {
    navigate("/admin/catalog/modifiers/modifier-form");
  };

  const handleEdit = (id: string) => {
    navigate(`/admin/catalog/modifiers/modifier-form/${id}`);
  };

  const handleDelete = async (id: string) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Are you sure you want to delete this Modifier?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#6b7280",
      focusConfirm: false,
      showLoaderOnConfirm: true,
      preConfirm: async () => {
        try {
          await deleteModifier(id).unwrap();
          return true;
        } catch (error) {
          Swal.showValidationMessage("Failed to delete modifier");
          return false;
        }
      },
      allowOutsideClick: () => !Swal.isLoading(),
    });

    if (result.isConfirmed) {
      if (result.value) {
        await Swal.fire({
          title: "Deleted!",
          text: "Modifier has been deleted successfully.",
          icon: "success",
          confirmButtonColor: "#6366f1",
        });
        refetch();
      }
    }
  };

  const toggleRowExpand = (id: string) => {
    setExpandedRows((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const filteredModifiers =
    modifiersData?.filter((modifier: Modifier) => {
      const productName = modifier.productId?.name?.toLowerCase() || "";
      const modifierNames =
        modifier.Modifier?.map((m: ModifierItem) => m.name?.toLowerCase()).join(
          " "
        ) || "";
      const searchLower = searchTerm.toLowerCase();

      return (
        productName.includes(searchLower) || modifierNames.includes(searchLower)
      );
    }) || [];

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentModifiers = filteredModifiers.slice(
    indexOfFirstItem,
    indexOfLastItem
  );
  const totalPages = Math.ceil(filteredModifiers.length / itemsPerPage);

  const renderPaginationButtons = () => {
    const buttons = [];

    buttons.push(
      <button
        key="prev"
        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className={`h-8 w-8 mr-1 flex justify-center items-center rounded-md border ${
          currentPage === 1
            ? "border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed"
            : "border-gray-300 bg-gray-100 text-gray-500 hover:bg-gray-200"
        }`}
      >
        <svg
          className="h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 19l-7-7 7-7"
          />
        </svg>
      </button>
    );

    for (let i = 1; i <= totalPages; i++) {
      buttons.push(
        <button
          key={i}
          onClick={() => setCurrentPage(i)}
          className={`h-8 w-8 mx-1 flex justify-center items-center rounded-md ${
            currentPage === i
              ? "border border-orange-500 bg-orange-500 text-white"
              : "border border-gray-300 text-gray-700 hover:bg-gray-100"
          }`}
        >
          {i}
        </button>
      );
    }

    buttons.push(
      <button
        key="next"
        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
        disabled={currentPage === totalPages || totalPages === 0}
        className={`h-8 w-8 ml-1 flex justify-center items-center rounded-md border ${
          currentPage === totalPages || totalPages === 0
            ? "border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed"
            : "border-gray-300 bg-gray-100 text-gray-500 hover:bg-gray-200"
        }`}
      >
        <svg
          className="h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>
    );

    return buttons;
  };
  if (isLoading) {
    return (
      <div className="flex justify-center items-start bg-white h-screen pt-[40vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading Modifires...</p>
        </div>
      </div>
    );
  }

  if (error) {
    Swal.fire({
      icon: "error",
      title: "Error",
      text: "Error loading modifiers. Please try again later.",
      confirmButtonColor: "#6366f1",
    });
    return null;
  }

  return (
    <div className="bg-gray-50 p-4 shadow min-h-screen">
      <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row justify-between items-center p-2 bg-white rounded-xl border border-gray-200 shadow mb-4">
        <h2 className="text-lg font-medium text-gray-800">Modifiers</h2>

        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row space-x-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Modifiers"
              value={searchTerm}
              onChange={handleSearchChange}
              className="pl-3 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg
                className="h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>

          <button
            onClick={handleAddModifier}
            className="bg-orange-500 text-white rounded-md px-4 py-2 flex items-center cursor-pointer"
          >
            Add Modifier
            <svg
              className="ml-2 h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto border border-gray-200 rounded-2xl">
        <table className="min-w-full">
          <thead className="bg-orange-50 bg-opacity-30">
            <tr>
              <th className="px-6 py-6 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider w-20">
                No
              </th>
              <th className="px-6 py-5 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                Menu Item Name
              </th>
              <th className="px-6 py-5 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                Modifier Name
              </th>
              <th className="px-6 py-5 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-5 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                Actions
              </th>
              <th className="px-6 py-5 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider">
                Expand
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentModifiers.length > 0 ? (
              currentModifiers.map((modifier: Modifier, index: number) => (
                <React.Fragment key={modifier._id}>
                  <tr
                    className={`hover:bg-orange-50 ${
                      expandedRows[modifier._id] ? "bg-orange-50" : ""
                    }`}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {indexOfFirstItem + index + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-700">
                        {modifier.productId?.name || "Unknown Product"}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex flex-wrap gap-2">
                        {modifier.Modifier?.slice(0, 2).map(
                          (mod: ModifierItem, idx: number) => (
                            <span
                              key={idx}
                              className="px-3 py-1 inline-flex text-sm leading-5 font-medium rounded-full border border-orange-300 text-orange-700 bg-orange-50"
                            >
                              {mod.name}
                            </span>
                          )
                        )}
                        {(modifier.Modifier?.length || 0) > 2 && (
                          <span className="text-gray-500 text-sm">
                            +{(modifier.Modifier?.length || 0) - 2} more
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-3 py-1 inline-flex text-xs leading-5 font-medium rounded-full ${
                          modifier.isActive
                            ? "bg-green-100 text-green-800 border border-green-400"
                            : "bg-orange-100 text-red-600 border border-red-300"
                        }`}
                      >
                        {modifier.isActive ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-6">
                        <button
                          onClick={() => handleEdit(modifier._id)}
                          className="text-blue-500 hover:text-blue-700 transition-colors"
                          title="Edit"
                        >
                          <Edit
                            id="edit-icon"
                            data-tooltip-id="edit-tooltip"
                            data-tooltip-content="Edit"
                            size={20}
                            className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                          />
                          <Tooltip
                            id="edit-tooltip"
                            place="bottom"
                            className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                          />
                        </button>
                        <button
                          onClick={() => handleDelete(modifier._id)}
                          className="text-red-500 hover:text-red-700 transition-colors"
                          title="Delete"
                          disabled={isDeleting}
                        >
                          <Trash2
                            id="delete-icon"
                            data-tooltip-id="delete-tooltip"
                            data-tooltip-content="Delete"
                            size={20}
                            className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                          />
                          <Tooltip
                            id="delete-tooltip"
                            place="bottom"
                            className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                          />
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-center">
                      <button
                        onClick={() => toggleRowExpand(modifier._id)}
                        className="text-gray-500 hover:text-orange-500 focus:outline-none"
                        data-tooltip-id={`toggle-tooltip-${modifier._id}`}
                        data-tooltip-content={
                          expandedRows[modifier._id] ? "Collapse" : "Expand"
                        }
                      >
                        {expandedRows[modifier._id] ? (
                          <ChevronUp size={20} className="mx-auto" />
                        ) : (
                          <ChevronDown size={20} className="mx-auto" />
                        )}
                      </button>

                      <Tooltip
                        id={`toggle-tooltip-${modifier._id}`}
                        place="bottom"
                        className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                      />
                    </td>
                  </tr>

                  {/* Expanded row */}
                  {expandedRows[modifier._id] && (
                    <tr className="bg-orange-50 hover:bg-orange-50">
                      <td colSpan={6} className="px-8 py-4">
                        <div className="bg-white rounded-lg shadow p-4">
                          <h3 className="text-lg font-medium text-gray-800 mb-3">
                            Modifier Details
                          </h3>

                          {modifier.Modifier?.map(
                            (mod: ModifierItem, modIdx: number) => (
                              <div
                                key={modIdx}
                                className="mb-4 border border-gray-200 rounded-md p-3"
                              >
                                <div className="flex items-center mb-2">
                                  <span className="px-3 py-1 inline-flex text-sm leading-5 font-medium rounded-full border border-orange-300 text-orange-700 bg-orange-50">
                                    {mod.name}
                                  </span>
                                </div>

                                {mod.properties &&
                                  mod.properties.length > 0 && (
                                    <div className="ml-3 mt-2">
                                      <p className="text-sm font-medium text-gray-700 mb-2">
                                        Properties:
                                      </p>
                                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                        {mod.properties.map(
                                          (prop: Property, propIdx: number) => (
                                            <div
                                              key={propIdx}
                                              className="flex justify-between px-3 py-2 border border-gray-200 rounded-md bg-gray-50"
                                            >
                                              <span className="text-sm text-gray-700">
                                                {prop.name}
                                              </span>
                                              <span className="text-sm font-medium text-orange-600">
                                                $
                                                {typeof prop.price === "number"
                                                  ? prop.price.toFixed(2)
                                                  : parseFloat(
                                                      prop.price
                                                    ).toFixed(2)}
                                              </span>
                                            </div>
                                          )
                                        )}
                                      </div>
                                    </div>
                                  )}
                              </div>
                            )
                          )}
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))
            ) : (
              <tr>
                <td
                  colSpan={6}
                  className="px-6 py-8 text-center text-gray-500 bg-gray-50"
                >
                  {searchTerm ? (
                    <>
                      <p className="text-lg mb-2">
                        No modifiers found matching "{searchTerm}"
                      </p>
                      <p className="text-sm">
                        Try adjusting your search or clear the search field
                      </p>
                    </>
                  ) : (
                    <>
                      <p className="text-lg mb-2">No modifiers found</p>
                      <p className="text-sm">
                        Click the "Add Modifier" button to create your first
                        modifier
                      </p>
                    </>
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {filteredModifiers.length > 0 && (
        <div className="flex px-5 py-3 md:flex-row flex-col justify-between md:items-start items-center  xs:flex-row xs:justify-between">
          <div className="flex items-center mt-2">
            {renderPaginationButtons()}
          </div>
          <div className="text-xs text-gray-500 mt-4 ">
            Showing {indexOfFirstItem + 1} to{" "}
            {Math.min(indexOfLastItem, filteredModifiers.length)} of{" "}
            {filteredModifiers.length} modifiers
          </div>
        </div>
      )}
    </div>
  );
};

export default Modifiers;
