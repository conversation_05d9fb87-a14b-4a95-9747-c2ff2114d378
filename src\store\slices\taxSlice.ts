import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { Tax } from '../api/taxApi';
import { taxApi } from '../api/taxApi';

interface TaxState {
  taxes: Tax[];
  isLoading: boolean;
  error: string | null;
}

const initialState: TaxState = {
  taxes: [],
  isLoading: false,
  error: null,
};

const taxSlice = createSlice({
  name: 'taxes',
  initialState,
  reducers: {
    setTaxes: (state, action: PayloadAction<Tax[]>) => {
      state.taxes = action.payload;
    },
    clearTaxes: (state) => {
      state.taxes = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET
      .addMatcher(
        taxApi.endpoints.getTaxs.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        taxApi.endpoints.getTaxs.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.taxes = payload;
        }
      )
      .addMatcher(
        taxApi.endpoints.getTaxs.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch taxes';
        }
      )

      // DELETE
      .addMatcher(
        taxApi.endpoints.deleteTax.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        taxApi.endpoints.deleteTax.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const taxId = action.meta.arg.originalArgs as string;
          state.taxes = state.taxes.filter(
            (tax) => tax.id !== taxId
          );
        }
      )
      .addMatcher(
        taxApi.endpoints.deleteTax.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete tax';
        }
      )

      // POST
      .addMatcher(
        taxApi.endpoints.postTax.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        taxApi.endpoints.postTax.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.taxes.push({
            id: payload._id,
            name: payload.name,
            taxValue: payload.taxValue,
            active: payload.active,
            byDefault: payload.byDefault,
            userId: payload.userId
          });
        }
      )
      .addMatcher(
        taxApi.endpoints.postTax.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create tax';
        }
      )

      // PUT
      .addMatcher(
        taxApi.endpoints.putTax.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        taxApi.endpoints.putTax.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          const updated = {
            id: payload.data._id,
            name: payload.data.name,
            taxValue: payload.data.taxValue,
            active: payload.data.active,
            byDefault: payload.data.byDefault,
            userId: payload.data.userId
          };
          state.taxes = state.taxes.map((tax) =>
            tax.id === updated.id ? updated : tax
          );
        }
      )
      .addMatcher(
        taxApi.endpoints.putTax.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update tax';
        }
      );
  },
});

export const { setTaxes, clearTaxes } = taxSlice.actions;

export const selectTaxes = (state: RootState) => state.taxes.taxes;
export const selectTaxesLoading = (state: RootState) => state.taxes.isLoading;
export const selectTaxesError = (state: RootState) => state.taxes.error;

export default taxSlice.reducer;