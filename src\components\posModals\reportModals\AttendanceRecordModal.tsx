import React, { useState } from "react";
import CustomModal from "../../CustomModal";
import { FiInfo } from "react-icons/fi";

interface AttendanceRecordModalProps {
    isOpen: boolean;
    onClose: () => void;
}

interface AttendanceRecord {
    employeeName: string;
    date: string;
    checkIn: string;
    checkOut: string;
    status: string;
}

const AttendanceRecordModal: React.FC<AttendanceRecordModalProps> = ({
    isOpen,
    onClose,
}) => {
    const [currentPage, setCurrentPage] = useState(1);

    // This would normally come from an API
    const [records, _setRecords] = useState<AttendanceRecord[]>([]);

    // Number of records to display per page
    const recordsPerPage = 5;

    // Calculate total number of pages
    const totalPages = Math.ceil(records.length / recordsPerPage);

    // Handle next page
    const handleNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    // Handle previous page
    const handlePrevPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    const footer = (
        <div className="flex justify-between items-center p-2">
            <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
                <button
                    className={`px-4 py-2 text-sm font-medium ${currentPage === 1
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-[#9C9C9C] cursor-pointer"
                        }`}
                    onClick={handlePrevPage}
                    disabled={currentPage === 1}
                    type="button"
                >
                    ← Previous
                </button>
                <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
                    {currentPage}
                </span>
                <button
                    className={`px-4 py-2 text-sm font-medium ${currentPage >= totalPages
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-[#9C9C9C] cursor-pointer"
                        }`}
                    onClick={handleNextPage}
                    disabled={currentPage >= totalPages}
                    type="button"
                >
                    Next →
                </button>
            </div>
            <button
                onClick={onClose}
                className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
                type="button"
            >
                Cancel
            </button>
        </div>
    );

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={onClose}
            title="Employees Attendance Record"
            width="max-w-6xl"
            zIndex={50} // Higher z-index than the parent modal
            footer={footer}
        >
            <div className="p-6">
                {records.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-16">
                        <div className="rounded-full bg-gray-100 p-4 mb-4">
                            <FiInfo className="text-gray-500" size={32} />
                        </div>
                        <p className="text-gray-600 text-lg">No Employee Record Found.</p>
                    </div>
                ) : (
                    <>
                        {/* Records Table */}
                        <div className="mb-6">
                            <div className="grid grid-cols-5 gap-4 px-4 py-2 bg-gray-50 rounded-t-lg">
                                <div className="text-gray-600 font-medium">Employee Name</div>
                                <div className="text-gray-600 font-medium">Date</div>
                                <div className="text-gray-600 font-medium">Check In</div>
                                <div className="text-gray-600 font-medium">Check Out</div>
                                <div className="text-gray-600 font-medium">Status</div>
                            </div>
                            <div className="divide-y divide-gray-100">
                                {records
                                    .slice(
                                        (currentPage - 1) * recordsPerPage,
                                        currentPage * recordsPerPage
                                    )
                                    .map((record, index) => (
                                        <div
                                            key={index}
                                            className="grid grid-cols-5 gap-4 px-4 py-3 hover:bg-gray-50"
                                        >
                                            <div className="text-gray-800">{record.employeeName}</div>
                                            <div className="text-gray-800">{record.date}</div>
                                            <div className="text-gray-800">{record.checkIn}</div>
                                            <div className="text-gray-800">{record.checkOut}</div>
                                            <div className="text-gray-800">{record.status}</div>
                                        </div>
                                    ))}
                            </div>
                            {records.length > 0 && (
                                <div className="text-right text-sm text-gray-500 mt-2">
                                    Showing {Math.min((currentPage - 1) * recordsPerPage + 1, records.length)} to {Math.min(currentPage * recordsPerPage, records.length)} of {records.length} records
                                </div>
                            )}
                        </div>
                    </>
                )}
            </div>
        </CustomModal>
    );
};

export default AttendanceRecordModal;