// import { useState, FormEvent, useEffect } from 'react';
// import { ChevronLeft } from 'lucide-react';
// import { useNavigate, useParams } from 'react-router-dom';
// import { 
//   useAddCouponMutation, 
//   useUpdateCouponMutation,
//   useGetCouponsByUserQuery,
//   Coupon
// } from '../../../store/api/couponsApi';
// import { toast } from 'react-toastify';

// interface CouponFormData {
//   series: string;
//   description: string;
//   discount: number | null;
//   discountType: string;
//   start: number | null;
//   end: number | null;
//   startDate: string;
//   endDate: string;
//   publish: boolean;
// }

// interface FormErrors {
//   [key: string]: string;
// }

// export default function CouponForm() {
//   const { id } = useParams();
//   const navigate = useNavigate();
//   const userId = localStorage.getItem("userId") || '';
  
//   // API hooks
//   const { data: coupons, isLoading: isLoadingCoupons } = useGetCouponsByUserQuery(userId, { 
//     skip: !userId,
//     refetchOnMountOrArgChange: true
//   });
  
//   const [addCoupon, { isLoading: isAdding }] = useAddCouponMutation();
//   const [updateCoupon, { isLoading: isUpdating }] = useUpdateCouponMutation();
  
//   const [formData, setFormData] = useState<CouponFormData>({
//     series: '',
//     description: '',
//     discount: null,
//     discountType: '',
//     start: null,
//     end: null,
//     startDate: '',
//     endDate: '',
//     publish: false,
//   });

//   const [formErrors, setFormErrors] = useState<FormErrors>({});
//   const [isSubmitting, setIsSubmitting] = useState(false);

//   // Check if we're in edit mode and populate form if needed
//   useEffect(() => {
//     if (id && coupons) {
//       console.log("id", id);
     
//       const couponToEdit = coupons.find(coupon => 
//         // Check _id to find the matching coupon
//         coupon._id === id
//       );
      
//       if (couponToEdit) {
//         console.log("Found coupon to edit:", couponToEdit);
//         setFormData({
//           series: couponToEdit.series || '',
//           description: couponToEdit.description || '',
//           discount: typeof couponToEdit.discount === 'string' 
//             ? parseFloat(couponToEdit.discount) 
//             : couponToEdit.discount || null,
//           discountType: couponToEdit.discountType || '',
//           start: couponToEdit.start || null,
//           end: couponToEdit.end || null,
//           startDate: couponToEdit.startDate ? couponToEdit.startDate.split('T')[0] : '',
//           endDate: couponToEdit.endDate ? couponToEdit.endDate.split('T')[0] : '',
//           publish: couponToEdit.publish || false,
//         });
//       } else if (id && !isLoadingCoupons) {
//         toast.error("Coupon not found");
//         navigate(-1);
//       }
//     }
//   }, [id, coupons, isLoadingCoupons, navigate]);

//   const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
//     const { name, value, type } = e.target;
//     const updatedValue = type === 'number' ? (value === '' ? null : parseFloat(value)) : value;

//     setFormData(prev => ({ ...prev, [name]: updatedValue }));
//     setFormErrors(prev => ({ ...prev, [name]: '' }));
//   };

//   const handlePublishChange = () => {
//     setFormData(prev => ({ ...prev, publish: !prev.publish }));
//   };

//   const validateForm = () => {
//     const errors: FormErrors = {};

//     if (!formData.series.trim()) errors.series = 'Coupon name is required';
//     if (!formData.description.trim()) errors.description = 'Description is required';
//     if (formData.discount === null) errors.discount = 'Discount is required';
//     if (!formData.discountType) errors.discountType = 'Discount type is required';
//     if (formData.start === null) errors.start = 'Start number is required';
//     if (formData.end === null) errors.end = 'End number is required';
//     if (!formData.startDate) errors.startDate = 'Start date is required';
//     if (!formData.endDate) errors.endDate = 'End date is required';

//     // Validate end date is after start date
//     if (formData.startDate && formData.endDate && new Date(formData.endDate) <= new Date(formData.startDate)) {
//       errors.endDate = 'End date must be after start date';
//     }

//     setFormErrors(errors);
//     return Object.keys(errors).length === 0;
//   };

//   const handleSubmit = async (e: FormEvent) => {
//     e.preventDefault();
  
//     if (!validateForm()) return;
    
//     setIsSubmitting(true);
  
//     try {
//       const payload: Partial<Coupon> = {
//         series: formData.series,
//         description: formData.description,
//         discount: formData.discount ?? 0,
//         discountType: formData.discountType,
//         start: formData.start ?? 0,
//         end: formData.end ?? 0,
//         startDate: formData.startDate,
//         endDate: formData.endDate,
//         publish: formData.publish,
//         userId,
//       };
  
//       if (id) {
//         // Update existing coupon - Fixed to match API expectation
//         console.log("Updating coupon with ID:", id);
//         console.log("Update Payload:", payload);
        
//         const result = await updateCoupon({ 
//           id, 
//           couponData: payload 
//         }).unwrap();
        
//         console.log("Update result:", result);
//         toast.success(result.message || 'Coupon updated successfully!');
//       } else {
//         // Create new coupon
//         const result = await addCoupon(payload).unwrap();
//         toast.success(result.message || 'Coupon added successfully!');
//       }
      
//       navigate(-1); // Go back after success
//     } catch (err: any) {
//       console.error('Failed to save coupon:', err);
//       toast.error(`Failed to save coupon: ${
//         err?.data?.message || err?.message || 'Unknown error'
//       }`);
//     } finally {
//       setIsSubmitting(false);
//     }
//   };

//   const renderError = (field: keyof CouponFormData) =>
//     formErrors[field] && <p className="text-red-500 text-xs mt-1">{formErrors[field]}</p>;

//   const isLoading = isAdding || isUpdating || isSubmitting;

//   if (isLoadingCoupons && id) {
//     return (
//       <div className="bg-gray-50 min-h-screen p-4 flex justify-center items-center">
//         <div className="text-xl text-gray-600">Loading coupon data...</div>
//       </div>
//     );
//   }

//   return (
//     <div className="bg-gray-50 min-h-screen p-4">
//       <div className="bg-white rounded-2xl border border-gray-200 shadow-sm p-2 flex items-center">
//         <ChevronLeft onClick={() => navigate(-1)} className="mr-2 w-5 h-5 cursor-pointer" />
//         <h1 className="font-bold text-lg">{id ? 'Edit Coupon' : 'Add Coupon'}</h1>
//       </div>

//       <form onSubmit={handleSubmit}>
//         <div className="bg-white border border-gray-200 rounded-2xl mt-4">
//           <h2 className="font-semibold mb-4 bg-orange-50 rounded-t-2xl p-3">Coupon Details</h2>
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
//             {/* Name */}
//             <div>
//               <label className="block mb-2 text-sm">Name<span className="text-red-500">*</span></label>
//               <input
//                 type="text"
//                 name="series"
//                 value={formData.series}
//                 onChange={handleChange}
//                 placeholder="Enter Coupon Name"
//                 className="w-full p-2 border border-gray-200 rounded"
//               />
//               {renderError('series')}
//             </div>

//             {/* Description */}
//             <div>
//               <label className="block mb-2 text-sm">Description<span className="text-red-500">*</span></label>
//               <input
//                 type="text"
//                 name="description"
//                 value={formData.description}
//                 onChange={handleChange}
//                 placeholder="Enter Description"
//                 className="w-full p-2 border border-gray-200 rounded"
//               />
//               {renderError('description')}
//             </div>

//             {/* Discount */}
//             <div>
//               <label className="block mb-2 text-sm">Discount<span className="text-red-500">*</span></label>
//               <input
//                 type="number"
//                 name="discount"
//                 value={formData.discount ?? ''}
//                 onChange={handleChange}
//                 placeholder="Enter Discount"
//                 className="w-full p-2 border border-gray-200 rounded"
//               />
//               {renderError('discount')}
//             </div>

//             {/* Discount Type */}
//             <div>
//               <label className="block mb-2 text-sm">Discount Type<span className="text-red-500">*</span></label>
//               <select
//                 name="discountType"
//                 value={formData.discountType}
//                 onChange={handleChange}
//                 className="w-full p-2 border border-gray-200 rounded"
//               >
//                 <option value="" disabled>Select Discount Type</option>
//                 <option value="%">%</option>
//                 <option value="$">$</option>
//               </select>
//               {renderError('discountType')}
//             </div>

//             {/* Start */}
//             <div>
//               <label className="block mb-2 text-sm">Start No<span className="text-red-500">*</span></label>
//               <input
//                 type="number"
//                 name="start"
//                 value={formData.start ?? ''}
//                 onChange={handleChange}
//                 placeholder="Enter Start No"
//                 className="w-full p-2 border border-gray-200 rounded"
//               />
//               {renderError('start')}
//             </div>

//             {/* End */}
//             <div>
//               <label className="block mb-2 text-sm">End No<span className="text-red-500">*</span></label>
//               <input
//                 type="number"
//                 name="end"
//                 value={formData.end ?? ''}
//                 onChange={handleChange}
//                 placeholder="Enter End No"
//                 className="w-full p-2 border border-gray-200 rounded"
//               />
//               {renderError('end')}
//             </div>

//             {/* Start Date */}
//             <div>
//               <label className="block mb-2 text-sm">Start Date<span className="text-red-500">*</span></label>
//               <input
//                 type="date"
//                 name="startDate"
//                 value={formData.startDate}
//                 onChange={handleChange}
//                 className="w-full p-2 border border-gray-200 rounded"
//               />
//               {renderError('startDate')}
//             </div>

//             {/* End Date */}
//             <div>
//               <label className="block mb-2 text-sm">End Date<span className="text-red-500">*</span></label>
//               <input
//                 type="date"
//                 name="endDate"
//                 value={formData.endDate}
//                 onChange={handleChange}
//                 className="w-full p-2 border border-gray-200 rounded"
//               />
//               {renderError('endDate')}
//             </div>
//           </div>
//         </div>

//         {/* Publish */}
//         <div className="bg-white border border-gray-200 rounded-2xl mt-4">
//           <h2 className="font-semibold mb-4 bg-orange-50 p-3 rounded-t-2xl">Permissions</h2>
//           <div className="flex items-center p-3">
//             <label className="inline-flex relative items-center cursor-pointer mr-3">
//               <input
//                 type="checkbox"
//                 name="publish"
//                 className="sr-only"
//                 checked={formData.publish}
//                 onChange={handlePublishChange}
//               />
//               <div className={`w-12 h-6 rounded-full transition-colors ${formData.publish ? 'bg-orange-500' : 'bg-gray-300'}`}>
//                 <div className={`w-4 h-4 bg-white rounded-full shadow transform transition-transform ${
//                   formData.publish ? 'translate-x-7' : 'translate-x-1'
//                 } mt-1`}></div>
//               </div>
//             </label>
//             <span>Publish</span>
//           </div>
//         </div>

//         {/* Footer Buttons */}
//         <div className="flex justify-end mt-6 gap-3">
//           <button 
//             type="button" 
//             onClick={() => navigate(-1)} 
//             className="px-6 py-2 border border-orange-500 text-orange-500 rounded"
//           >
//             Cancel
//           </button>
//           <button 
//             type="submit" 
//             className={`px-6 py-2 bg-orange-500 text-white rounded ${
//               isLoading ? 'opacity-70 cursor-not-allowed' : ''
//             }`}
//             disabled={isLoading}
//           >
//             {isLoading ? 'Saving...' : id ? 'Update Coupon' : 'Add Coupon'}
//           </button>
//         </div>
//       </form>
//     </div>
//   );
// }


import { useState, type FormEvent, useEffect } from 'react';
import { ChevronLeft } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  useAddCouponMutation, 
  useUpdateCouponMutation,
  useGetCouponsByUserQuery,
  type Coupon
} from '../../../store/api/couponsApi';
import { toast } from 'react-toastify';

interface CouponFormData {
  series: string;
  description: string;
  discount: number | null;
  discountType: string;
  start: number | null;
  end: number | null;
  startDate: string;
  endDate: string;
  publish: boolean;
}

interface FormErrors {
  [key: string]: string;
}

export default function CouponForm() {
  const { id } = useParams();
  const navigate = useNavigate();
  const userId = localStorage.getItem("userId") || '';
  
  // API hooks
  const { data: coupons, isLoading: isLoadingCoupons } = useGetCouponsByUserQuery(userId, { 
    skip: !userId,
    refetchOnMountOrArgChange: true
  });
  
  const [addCoupon, { isLoading: isAdding }] = useAddCouponMutation();
  const [updateCoupon, { isLoading: isUpdating }] = useUpdateCouponMutation();
  
  const [formData, setFormData] = useState<CouponFormData>({
    series: '',
    description: '',
    discount: null,
    discountType: '',
    start: null,
    end: null,
    startDate: '',
    endDate: '',
    publish: false,
  });

  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentCoupon, setCurrentCoupon] = useState<Coupon | null>(null);

  // Check if we're in edit mode and populate form if needed
  useEffect(() => {
    if (id && coupons) {
      console.log("Looking for coupon with id:", id);
     
      const couponToEdit = coupons.find(coupon => 
        // Check _id to find the matching coupon
        coupon._id === id
      );
      
      if (couponToEdit) {
        console.log("Found coupon to edit:", couponToEdit);
        setCurrentCoupon(couponToEdit);
        
        setFormData({
          series: couponToEdit.series || '',
          description: couponToEdit.description || '',
          discount: typeof couponToEdit.discount === 'string' 
            ? parseFloat(couponToEdit.discount) 
            : couponToEdit.discount || null,
          discountType: couponToEdit.discountType || '',
          start: couponToEdit.start || null,
          end: couponToEdit.end || null,
          startDate: couponToEdit.startDate ? couponToEdit.startDate.split('T')[0] : '',
          endDate: couponToEdit.endDate ? couponToEdit.endDate.split('T')[0] : '',
          publish: couponToEdit.publish || false,
        });
      } else if (id && !isLoadingCoupons) {
        toast.error("Coupon not found");
        navigate(-1);
      }
    }
  }, [id, coupons, isLoadingCoupons, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const updatedValue = type === 'number' ? (value === '' ? null : parseFloat(value)) : value;

    setFormData(prev => ({ ...prev, [name]: updatedValue }));
    setFormErrors(prev => ({ ...prev, [name]: '' }));
  };

  const handlePublishChange = () => {
    setFormData(prev => ({ ...prev, publish: !prev.publish }));
  };

  const validateForm = () => {
    const errors: FormErrors = {};

    if (!formData.series.trim()) errors.series = 'Coupon name is required';
    if (!formData.description.trim()) errors.description = 'Description is required';
    if (formData.discount === null) errors.discount = 'Discount is required';
    if (!formData.discountType) errors.discountType = 'Discount type is required';
    if (formData.start === null) errors.start = 'Start number is required';
    if (formData.end === null) errors.end = 'End number is required';
    if (!formData.startDate) errors.startDate = 'Start date is required';
    if (!formData.endDate) errors.endDate = 'End date is required';

    // Validate end date is after start date
    if (formData.startDate && formData.endDate && new Date(formData.endDate) <= new Date(formData.startDate)) {
      errors.endDate = 'End date must be after start date';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
  
    if (!validateForm()) return;
    
    setIsSubmitting(true);
  
    try {
      if (id && currentCoupon && currentCoupon._id) {
        // Update existing coupon
        console.log("Updating coupon with ID:", currentCoupon._id);
        
        // Important: Include the _id in the payload for updates
        // since the backend is using req.body._id to find the document
        const payload: Partial<Coupon> = {
          _id: currentCoupon._id, // This is crucial for your backend
          series: formData.series,
          description: formData.description,
          discount: formData.discount ?? 0,
          discountType: formData.discountType,
          start: formData.start ?? 0,
          end: formData.end ?? 0,
          startDate: formData.startDate,
          endDate: formData.endDate,
          publish: formData.publish,
          userId,
        };
        
        console.log("Update payload:", payload);
        
        const result = await updateCoupon({ 
          id, 
          couponData: payload 
        }).unwrap();
        
        console.log("Update result:", result);
        toast.success(result.message || 'Coupon updated successfully!');
      } else {
        // Create new coupon
        const payload: Partial<Coupon> = {
          series: formData.series,
          description: formData.description,
          discount: formData.discount ?? 0,
          discountType: formData.discountType,
          start: formData.start ?? 0,
          end: formData.end ?? 0,
          startDate: formData.startDate,
          endDate: formData.endDate,
          publish: formData.publish,
          userId,
        };
        
        const result = await addCoupon(payload).unwrap();
        toast.success(result.message || 'Coupon added successfully!');
      }
      
      navigate(-1); // Go back after success
    } catch (err: any) {
      console.error('Failed to save coupon:', err);
      toast.error(`Failed to save coupon: ${
        err?.data?.message || err?.message || 'Unknown error'
      }`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderError = (field: keyof CouponFormData) =>
    formErrors[field] && <p className="text-red-500 text-xs mt-1">{formErrors[field]}</p>;

  const isLoading = isAdding || isUpdating || isSubmitting;

  if (isLoadingCoupons && id) {
    return (
      <div className="bg-gray-50 min-h-screen p-4 flex justify-center items-center">
        <div className="text-xl text-gray-600">Loading coupon data...</div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen p-4">
      <div className="bg-white rounded-2xl border border-gray-200 shadow-sm p-2 flex items-center">
        <ChevronLeft onClick={() => navigate(-1)} className="mr-2 w-5 h-5 cursor-pointer" />
        <h1 className="font-bold text-lg">{id ? 'Edit Coupon' : 'Add Coupon'}</h1>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="bg-white border border-gray-200 rounded-2xl mt-4">
          <h2 className="font-semibold mb-4 bg-orange-50 rounded-t-2xl p-3">Coupon Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
            {/* Name */}
            <div>
              <label className="block mb-2 text-sm">Name<span className="text-red-500">*</span></label>
              <input
                type="text"
                name="series"
                value={formData.series}
                onChange={handleChange}
                placeholder="Enter Coupon Name"
                className="w-full p-2 border border-gray-200 rounded"
              />
              {renderError('series')}
            </div>

            {/* Description */}
            <div>
              <label className="block mb-2 text-sm">Description<span className="text-red-500">*</span></label>
              <input
                type="text"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Enter Description"
                className="w-full p-2 border border-gray-200 rounded"
              />
              {renderError('description')}
            </div>

            {/* Discount */}
            <div>
              <label className="block mb-2 text-sm">Discount<span className="text-red-500">*</span></label>
              <input
                type="number"
                name="discount"
                value={formData.discount ?? ''}
                onChange={handleChange}
                placeholder="Enter Discount"
                className="w-full p-2 border border-gray-200 rounded"
              />
              {renderError('discount')}
            </div>

            {/* Discount Type */}
            <div>
              <label className="block mb-2 text-sm">Discount Type<span className="text-red-500">*</span></label>
              <select
                name="discountType"
                value={formData.discountType}
                onChange={handleChange}
                className="w-full p-2 border border-gray-200 rounded"
              >
                <option value="" disabled>Select Discount Type</option>
                <option value="%">%</option>
                <option value="$">$</option>
              </select>
              {renderError('discountType')}
            </div>

            {/* Start */}
            <div>
              <label className="block mb-2 text-sm">Start No<span className="text-red-500">*</span></label>
              <input
                type="number"
                name="start"
                value={formData.start ?? ''}
                onChange={handleChange}
                placeholder="Enter Start No"
                className="w-full p-2 border border-gray-200 rounded"
              />
              {renderError('start')}
            </div>

            {/* End */}
            <div>
              <label className="block mb-2 text-sm">End No<span className="text-red-500">*</span></label>
              <input
                type="number"
                name="end"
                value={formData.end ?? ''}
                onChange={handleChange}
                placeholder="Enter End No"
                className="w-full p-2 border border-gray-200 rounded"
              />
              {renderError('end')}
            </div>

            {/* Start Date */}
            <div>
              <label className="block mb-2 text-sm">Start Date<span className="text-red-500">*</span></label>
              <input
                type="date"
                name="startDate"
                value={formData.startDate}
                onChange={handleChange}
                className="w-full p-2 border border-gray-200 rounded"
              />
              {renderError('startDate')}
            </div>

            {/* End Date */}
            <div>
              <label className="block mb-2 text-sm">End Date<span className="text-red-500">*</span></label>
              <input
                type="date"
                name="endDate"
                value={formData.endDate}
                onChange={handleChange}
                className="w-full p-2 border border-gray-200 rounded"
              />
              {renderError('endDate')}
            </div>
          </div>
        </div>

        {/* Publish */}
        <div className="bg-white border border-gray-200 rounded-2xl mt-4">
          <h2 className="font-semibold mb-4 bg-orange-50 p-3 rounded-t-2xl">Permissions</h2>
          <div className="flex items-center p-3">
            <label className="inline-flex relative items-center cursor-pointer mr-3">
              <input
                type="checkbox"
                name="publish"
                className="sr-only"
                checked={formData.publish}
                onChange={handlePublishChange}
              />
              <div className={`w-12 h-6 rounded-full transition-colors ${formData.publish ? 'bg-orange-500' : 'bg-gray-300'}`}>
                <div className={`w-4 h-4 bg-white rounded-full shadow transform transition-transform ${
                  formData.publish ? 'translate-x-7' : 'translate-x-1'
                } mt-1`}></div>
              </div>
            </label>
            <span>Publish</span>
          </div>
        </div>

        {/* Footer Buttons */}
        <div className="flex justify-end mt-6 gap-3">
          <button 
            type="button" 
            onClick={() => navigate(-1)} 
            className="px-6 py-2 border border-orange-500 text-orange-500 rounded"
          >
            Cancel
          </button>
          <button 
            type="submit" 
            className={`px-6 py-2 bg-orange-500 text-white rounded ${
              isLoading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : id ? 'Update Coupon' : 'Add Coupon'}
          </button>
        </div>
      </form>
    </div>
  );
}