import { baseApi } from "./baseApi";

// Updated interface for stock history entries
interface StockHistoryEntry {
  stock: number;
  price: number;
  entryDate?: string;
  expiry: string;
  createdAt?: string;
  _id?: string;
}

export interface Ingredient {
  stockHistory: StockHistoryEntry[];
  createdAt: string;
  id: string;
  _id?: string; // MongoDB ID
  IngredientID: string;
  IngredientName: string;
  UnitPrice: number;
  CurrentStock: number;
  UnitofMeasurement: any;
  supplierId: any;
  CategoryType: any;
  Expiry?: string;
  ThresholdLevel?: number;
  ShelfLife?: string;
  StorageInstructions?: string;
  Alternative?: string;
  NutritionalInformation?: string;
  Notes?: string;
  Active?: boolean;
  updatedAt?: string;
  userId?: string;
}

export const ingredientsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getIngredients: builder.query<Ingredient[], string>({
      query: (userId) => `/Ingredients?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          stockHistory: item.stockHistory || [],
          createdAt: item.createdAt,
          id: item._id,
          _id: item._id,
          IngredientID: item.IngredientID,
          IngredientName: item.IngredientName,
          UnitPrice: item.UnitPrice,
          CurrentStock: item.CurrentStock,
          UnitofMeasurement: item.UnitofMeasurement,
          supplierId: item.supplierId,
          CategoryType: item.CategoryType,
          Expiry: item.Expiry,
          ThresholdLevel: item.ThresholdLevel,
          ShelfLife: item.ShelfLife,
          StorageInstructions: item.StorageInstructions,
          Alternative: item.Alternative,
          NutritionalInformation: item.NutritionalInformation,
          Notes: item.Notes,
          Active: item.Active,
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "Ingredient" as const, id })),
              { type: "Ingredient", id: "LIST" },
            ]
          : [{ type: "Ingredient", id: "LIST" }],
    }),

    getIngredient: builder.query<Ingredient, string>({
      query: (id) => `/Ingredients/${id}`,
      transformResponse: (response: any) => ({
        stockHistory: response.stockHistory || [],
        createdAt: response.createdAt,
        id: response._id,
        _id: response._id, // Include MongoDB ID
        IngredientID: response.IngredientID,
        IngredientName: response.IngredientName,
        UnitPrice: response.UnitPrice,
        CurrentStock: response.CurrentStock,
        UnitofMeasurement: response.UnitofMeasurement,
        supplierId: response.supplierId,
        CategoryType: response.CategoryType,
        Expiry: response.Expiry,
        ThresholdLevel: response.ThresholdLevel,
        ShelfLife: response.ShelfLife,
        StorageInstructions: response.StorageInstructions,
        Alternative: response.Alternative,
        NutritionalInformation: response.NutritionalInformation,
        Notes: response.Notes,
        Active: response.Active,
        updatedAt: response.updatedAt,
        userId: response.userId
      }),
      providesTags: (_result, _error, id) => [{ type: "Ingredient", id }],
    }),

    deleteIngredient: builder.mutation<void, string>({
      query: (id) => ({
        url: `/Ingredients/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            ingredientsApi.util.invalidateTags([
              { type: "Ingredient", id },
            ])
          );
        } catch (error) {
          console.error("Failed to delete ingredient", error);
        }
      },
      invalidatesTags: [{ type: "Ingredient", id: "LIST" }],
    }),

    postIngredient: builder.mutation<any, any>({
      query: (data) => {
        // Ensure key names match expected backend parameters
        const formattedData = {
          IngredientName: data.IngredientName,
          UnitPrice: data.UnitPrice,
          CurrentStock: data.CurrentStock,
          UnitofMeasurement: data.UnitofMeasurement,
          supplierId: data.supplierId,
          CategoryType: data.CategoryType,
          Expiry: data.Expiry,
          ThresholdLevel: data.ThresholdLevel,
          ShelfLife: data.ShelfLife,
          StorageInstructions: data.StorageInstructions,
          Alternative: data.Alternative,
          NutritionalInformation: data.NutritionalInformation,
          Notes: data.Notes,
          Active: data.Active,
          userId: data.userId,
          // Don't include IngredientID as backend generates it
        };
        
        console.log("Sending POST request with formatted data:", formattedData);
        return {
          url: "/Ingredients",
          method: "POST",
          body: formattedData,
        };
      },
      invalidatesTags: [{ type: "Ingredient", id: "LIST" }],
    }),

    putIngredient: builder.mutation<
      any,
      { id: string; formData: any }
    >({
      query: ({ id, formData }) => {
        // Ensure StorageInstructions key is correct
        const formattedData = {
          ...formData,
          StorageInstructions: formData.StorageInstructions || formData.StorageInstruction
        };
        
        return {
          url: `/Ingredients/${id}`,
          method: "PUT",
          body: formattedData,
        };
      },
      invalidatesTags: (_result, _error, { id }) => [
        { type: "Ingredient", id },
        { type: "Ingredient", id: "LIST" },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetIngredientsQuery,
  useGetIngredientQuery,
  useDeleteIngredientMutation,
  usePostIngredientMutation,
  usePutIngredientMutation,
} = ingredientsApi;