import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useGetParentCategoriesQuery } from "../../../store/api/parentCategoryApi";
import { useGetCategoriesQuery } from "../../../store/api/CategoryApi";
import {
  useGetProductsQuery,
  useDeleteProductMutation,
} from "../../../store/api/menuitemApi";
import { Edit, Trash2, ChevronLeft, ChevronRight } from "lucide-react";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import Swal from "sweetalert2";

interface Category {
  id: string;
  name: string;
  subcategories: Subcategory[];
}

interface Subcategory {
  id: string;
  name: string;
}

interface DisplayProduct {
  id: string;
  name: string;
  price: number;
  Product_pic?: string | undefined;
  categoryParents?: { name: string }[];
  categoryId?: { name: string }[];
  hasPicture?: boolean;
}

const MenuItems: React.FC = () => {
  const userId = localStorage.getItem("userId") || "";
  const {
    data: products,
    isLoading,
    error,
    refetch,
  } = useGetProductsQuery(userId);
  const { data: parentCategoriesData } = useGetParentCategoriesQuery(userId);
  const { data: categoriesData } = useGetCategoriesQuery(userId);
  const [deleteProduct, { isLoading: isDeleting }] = useDeleteProductMutation();

  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState("All Categories");
  const [displayedSubcategories, setDisplayedSubcategories] = useState<
    string[]
  >(["All Sub Categories"]);
  const [activeSubcategory, setActiveSubcategory] =
    useState("All Sub Categories");
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredProducts, setFilteredProducts] = useState<DisplayProduct[]>(
    []
  );
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8;

  useEffect(() => {
    if (parentCategoriesData && categoriesData) {
      const transformedCategories: Category[] = [
        { id: "all", name: "All Categories", subcategories: [] },
      ];

      parentCategoriesData.forEach((parentCat) => {
        const subcats = categoriesData
          .filter((cat) => cat.parentCategory === parentCat.name)
          .map((cat) => ({ id: cat.id, name: cat.name }));

        transformedCategories.push({
          id: parentCat.id,
          name: parentCat.name,
          subcategories: subcats,
        });
      });

      setCategories(transformedCategories);
    }
  }, [parentCategoriesData, categoriesData]);

  useEffect(() => {
    if (selectedCategory === "All Categories" && categoriesData) {
      setDisplayedSubcategories([
        "All Sub Categories",
        ...categoriesData.map((cat) => cat.name),
      ]);
    } else {
      const category = categories.find((cat) => cat.name === selectedCategory);
      setDisplayedSubcategories([
        "All Sub Categories",
        ...(category?.subcategories.map((sc) => sc.name) || []),
      ]);
    }

    setActiveSubcategory("All Sub Categories");
    setCurrentPage(1);
  }, [selectedCategory, categories, categoriesData]);

  useEffect(() => {
    if (!products || !categoriesData) return;

    console.log("=== DEBUGGING ===");
    console.log("All Products:", products);
    console.log("Categories Data:", categoriesData);
    console.log("Selected Category:", selectedCategory);
    console.log("Active Subcategory:", activeSubcategory);

    const mappedProducts = products.map((product) => {
      const productCategoryIds = Array.isArray(product.categoryId)
        ? product.categoryId.map((cat) => cat._id)
        : [];

      // Log product category mapping
      console.log("Product Category IDs:", productCategoryIds);

      const matchingCategories = categoriesData.filter((cat) =>
        productCategoryIds.includes(cat.id)
      );

      // Log matching categories for each product
      console.log("Matching Categories for Product:", matchingCategories);

      // Extract parent category names and subcategory names
      const categoryParents = matchingCategories
        .map((cat) => ({ name: cat.parentCategory || "" }))
        .filter((cat) => cat.name !== "");

      const categoryIds = matchingCategories
        .map((cat) => ({ name: cat.name || "" }))
        .filter((cat) => cat.name !== "");

      const price =
        typeof product.price === "string"
          ? parseFloat(product.price)
          : product.price;

      return {
        id: product.id,
        name: product.name,
        price,
        Product_pic: product.pictureUrl,
        hasPicture: product.hasPicture,
        categoryParents,
        categoryId: categoryIds,
      };
    });

    let filtered = [...mappedProducts];

    // Filter by parent category if selected
    if (selectedCategory !== "All Categories") {
      const parentCategory = categories.find(
        (cat) => cat.name === selectedCategory
      );
      const subcategoryNames =
        parentCategory?.subcategories.map((sc) => sc.name) || [];

      console.log("Selected Parent Category Subcategories:", subcategoryNames);

      // Filtering products based on subcategory names under the selected parent category
      filtered = filtered.filter((product) => {
        const productCategories = product.categoryId.map((ci) => ci.name) || [];

        return productCategories.some((pc) => subcategoryNames.includes(pc));
      });

      console.log("Filtered Products After Parent Category Check:", filtered);
    }

    // Filter by subcategory if selected
    if (activeSubcategory !== "All Sub Categories") {
      filtered = filtered.filter((product) => {
        const productCategories = product.categoryId.map((ci) => ci.name) || [];
        console.log(
          "Product Categories for Subcategory Filter:",
          productCategories
        );

        return productCategories.includes(activeSubcategory);
      });

      console.log("Filtered Products After Subcategory Check:", filtered);
    }

    // Filter by search term if any
    if (searchTerm.trim()) {
      filtered = filtered.filter((product) =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredProducts(filtered);
  }, [
    products,
    categoriesData,
    selectedCategory,
    activeSubcategory,
    searchTerm,
    categories,
  ]);

  const navigate = useNavigate();
  const handleMenuForm = () =>
    navigate("/admin/catalog/menu-items/menu-items-form");

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleDeleteProduct = async (productId: string) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Are you sure you want to delete this product?",
      icon: "warning",
      cancelButtonText: "No, cancel!",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#6b7280",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        await deleteProduct(productId).unwrap();
        await Swal.fire({
          title: "Deleted!",
          text: "Product has been deleted successfully.",
          icon: "success",
          confirmButtonText: "OK",
        });
        refetch(); // Refresh your data after successful deletion
      } catch (error) {
        await Swal.fire({
          title: "Error!",
          text: "Failed to delete product.",
          icon: "error",
          confirmButtonText: "OK",
        });
      }
    }
  };

  const handleEditProduct = (product: any) => {
    console.log("product in main page", product);
    navigate(`/admin/catalog/menu-items/menu-items-form/${product.id}`);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row justify-between items-center mb-6 p-2 bg-white rounded-2xl border border-gray-200">
        <h2 className="text-3xl p-3 font-bold cursor-pointer">Menu Items</h2>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Menu Items"
              className="border border-gray-300 rounded-md py-3 px-4 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <button className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </button>
          </div>
          <button
            onClick={handleMenuForm}
            className="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-md flex items-center gap-2 text-sm"
          >
            <h1 className="text-md font-bold cursor-pointer">Add Menu Item</h1>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Categories */}
      <div className="flex flex-wrap gap-2 mb-4">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.name)}
            className={`py-1 px-3 rounded-full text-sm ${
              selectedCategory === category.name
                ? "bg-orange-500 text-white"
                : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-100"
            }`}
          >
            {category.name}
          </button>
        ))}
      </div>

      {/* Subcategories */}
      <div className="flex flex-wrap gap-2 mb-6">
        {displayedSubcategories.map((subcategory, index) => (
          <button
            key={`${subcategory}-${index}`}
            onClick={() => setActiveSubcategory(subcategory)}
            className={`py-1 px-3 rounded-full text-xs ${
              activeSubcategory === subcategory
                ? "bg-orange-100 text-black border border-orange-400"
                : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-100"
            }`}
          >
            {subcategory}
          </button>
        ))}
      </div>

      {/* Product Grid */}
      {isLoading ? (
        <div className="flex justify-center items-start bg-white h-screen pt-[20vh]">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
            <p className="text-gray-600 font-medium">Loading Menu Items...</p>
          </div>
        </div>
      ) : error ? (
        <div className="p-4 bg-red-100 text-red-700 rounded">
          Error loading products
        </div>
      ) : paginatedProducts.length === 0 ? (
        <div className="p-8 text-center text-gray-500">No products found</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-4">
          {paginatedProducts.map((product) => (
            <div
              key={product.id}
              className="bg-white rounded-lg shadow overflow-hidden border border-gray-200 flex flex-col"
            >
              {/* Product image container with update/delete buttons */}
              <div className="relative">
                {/* Image */}
                <div className="h-48 bg-gray-100 flex justify-center items-center">
                  {product.Product_pic ? (
                    <img
                      src={product.Product_pic}
                      alt={product.name}
                      className="h-full w-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = "";
                        (
                          e.target as HTMLImageElement
                        ).parentElement!.innerHTML = "No image";
                      }}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full w-full text-gray-400">
                      No image
                    </div>
                  )}
                </div>

                <div className="absolute top-2 right-2 flex space-x-2">
                  <button
                    className="bg-white rounded-lg p-2 shadow-md hover:shadow-lg transition-shadow relative z-20"
                    onClick={() => handleEditProduct(product)}
                    onMouseEnter={(e) => e.stopPropagation()}
                    onMouseLeave={(e) => e.stopPropagation()}
                  >
                    <Edit
                      id={`edit-icon-${product.id}`}
                      data-tooltip-id={`edit-tooltip-${product.id}`}
                      data-tooltip-content="Edit"
                      size={20}
                      className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                    />
                    <Tooltip
                      id={`edit-tooltip-${product.id}`}
                      place="bottom"
                      className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg !z-50"
                    />
                  </button>

                  <button
                    className="bg-white p-2 rounded-lg shadow-md hover:shadow-lg transition-shadow relative z-20"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleDeleteProduct(product.id);
                    }}
                    onMouseEnter={(e) => e.stopPropagation()}
                    onMouseLeave={(e) => e.stopPropagation()}
                    disabled={isDeleting}
                  >
                    <Trash2
                      id={`delete-icon-${product.id}`}
                      data-tooltip-id={`delete-tooltip-${product.id}`}
                      data-tooltip-content="Delete"
                      size={20}
                      className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                    />
                    <Tooltip
                      id={`delete-tooltip-${product.id}`}
                      place="bottom"
                      className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg !z-50"
                    />
                  </button>
                </div>
              </div>

              {/* Product details centered below image */}
              <div className="p-4 flex-grow flex flex-col items-center text-center">
                {/* Product name */}
                <h3 className="text-lg font-semibold mb-2">{product.name}</h3>

                {/* Price */}
                <p className="text-gray-700 font-bold mb-3">
                  ${product.price.toFixed(2)}
                </p>

                {/* Category info - optional for debugging */}
                {/* <div className="text-xs text-gray-500">
                  <p>Categories: {product.categoryId?.map(c => c.name).join(', ')}</p>
                  <p>Parent: {product.categoryParents?.map(c => c.name).join(', ')}</p>
                </div> */}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-start items-center gap-2 mt-6">
          <button
            onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
            disabled={currentPage === 1}
            className="border border-gray-300 text-gray-700 rounded-md p-2 disabled:opacity-50"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          {Array.from({ length: totalPages }, (_, i) => (
            <button
              key={i}
              onClick={() => setCurrentPage(i + 1)}
              className={`border px-3 py-1 rounded-md ${
                currentPage === i + 1
                  ? "bg-orange-500 text-white"
                  : "bg-white text-gray-700"
              }`}
            >
              {i + 1}
            </button>
          ))}
          <button
            onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
            disabled={currentPage === totalPages}
            className="border border-gray-300 text-gray-700 rounded-md p-2 disabled:opacity-50"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  );
};

export default MenuItems;
