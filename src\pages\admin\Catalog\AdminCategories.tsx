import React, { useState } from "react";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import {
  Search,
  Edit,
  Trash2,
  Plus,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  useGetCategoriesQuery,
  useDeleteCategoryMutation,
  type Category,
} from "../../../store/api/CategoryApi";

import "react-toastify/dist/ReactToastify.css";
import Swal from "sweetalert2";

const Categories: React.FC = () => {
  const navigate = useNavigate();
  const userId: string = localStorage.getItem("userId") || "";

  const {
    data: categories = [] as Category[],
    isLoading,
    error,
    refetch,
  } = useGetCategoriesQuery(userId, {
    skip: !userId,
    refetchOnMountOrArgChange: true,
  });

  const [deleteCategory, { isLoading: isDeleting }] =
    useDeleteCategoryMutation();
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage] = useState<number>(5);

  const filteredCategories: Category[] = categories.filter(
    (category: Category) =>
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (category.parentCategory?.toLowerCase() || "").includes(
        searchTerm.toLowerCase()
      )
  );

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentCategories: Category[] = filteredCategories.slice(
    indexOfFirstItem,
    indexOfLastItem
  );

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  const handleAddCategory = () => {
    navigate("/admin/catalog/categories/categories-form");
  };

  const handleDeleteCategory = async (categoryId: string) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Are you sure to delete this category!",
      icon: "warning",
      cancelButtonText: "No, cancel!",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#6b7280",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        await deleteCategory(categoryId).unwrap();
        await Swal.fire(
          "Deleted!",
          "Parent category has been deleted.",
          "success"
        );
        refetch();
      } catch (error) {
        Swal.fire("Error!", "Failed to delete parent category.", "error");
      }
    }
  };

  const handleEditCategory = (id: string) => {
    navigate(`/admin/catalog/categories/categories-form/${id}`);
  };

  const renderCategoryStatus = (status: boolean) => (
    <span
      className={`px-3 py-1 text-xs font-medium rounded-full ${
        status
          ? "text-green-800 bg-green-100 border border-green-400"
          : "text-red-800 bg-red-100 border border-red-400"
      }`}
    >
      {status ? "Active" : "Inactive"}
    </span>
  );

  const renderCategoryImage = (category: Category) =>
    category.pictureUrl ? (
      <img
        src={category.pictureUrl}
        alt={category.name}
        className="h-8 w-8 rounded-md object-cover"
      />
    ) : (
      <span className="text-gray-500">N/A</span>
    );

  return (
    <div className="p-3 w-full bg-gray-50">
      {/* Delete Confirmation Modal */}

      <div className="mb-6">
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row justify-between items-center mt-4 bg-white rounded-3xl border border-gray-200 shadow p-2">
          <h1 className="text-3xl font-bold p-3 text-gray-800">Categories</h1>
          <div className="flex flex-col md:flex-row items-center gap-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search Category"
                className="pl-10 pr-4 py-2 border border-gray-300 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setSearchTerm(e.target.value)
                }
              />
              <div className="absolute right-3 top-2.5 text-gray-400">
                <Search size={20} />
              </div>
            </div>
            <button
              onClick={handleAddCategory}
              className="bg-orange-500 cursor-pointer hover:bg-orange-600 text-white font-medium py-2 px-4 rounded-md flex items-center"
            >
              Add Category <Plus size={20} className="ml-1" />
            </button>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-start bg-white h-screen pt-[20vh]">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
            <p className="text-gray-600 font-medium">Loading Categories...</p>
          </div>
        </div>
      ) : error ? (
        <div className="p-4 text-center text-red-500">
          Error loading categories
        </div>
      ) : (
        <>
          <div className="bg-white rounded-md shadow overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-orange-50">
                <tr>
                   <th className="px-6 py-6 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider w-20">
                    No
                  </th>
                  <th className="px-6 py-6 text-left text-sm font-simibold text-gray-700 uppercase tracking-wider">
                    Category Name
                  </th>
                  <th className="px-6 py-6 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Parent Category
                  </th>
                  <th className="px-6 py-6 text-left text-sm font-simibold text-gray-700 uppercase tracking-wider">
                    Total Menu Items
                  </th>
                  <th className="px-6 py-6 text-left text-sm font-simibold text-gray-700 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-6 text-left text-sm font-simibold text-gray-700 uppercase tracking-wider">
                    Picture
                  </th>
                  <th className="px-6 py-6 text-right text-sm font-simibold text-gray-700 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentCategories.map((category: Category, index: number) => (
                  <tr key={category.id} className="hover:bg-orange-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {indexOfFirstItem + index + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {category.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {category.parentCategory || "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {category.totalMenuItems}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {renderCategoryStatus(category.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {renderCategoryImage(category)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleEditCategory(category.id)}
                        className="text-blue-400 hover:text-blue-600 mr-3"
                      >
                        <Edit
                          id="edit-icon"
                          data-tooltip-id="edit-tooltip"
                          data-tooltip-content="Edit"
                          size={20}
                          className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                        />
                        <Tooltip
                          id="edit-tooltip"
                          place="bottom"
                          className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                        />
                      </button>
                      <button
                        onClick={() => handleDeleteCategory(category.id)}
                        disabled={isDeleting}
                        className={`text-red-400 hover:text-red-600 ${
                          isDeleting ? "opacity-50 cursor-not-allowed" : ""
                        }`}
                      >
                        <Trash2
                          id="delete-icon"
                          data-tooltip-id="delete-tooltip"
                          data-tooltip-content="Delete"
                          size={20}
                          className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                        />
                        <Tooltip
                          id="delete-tooltip"
                          place="bottom"
                          className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                        />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="flex items-center justify-start mt-4 gap-2">
            <button
              onClick={() => paginate(currentPage - 1)}
              disabled={currentPage === 1}
              className={`flex items-center justify-center w-8 h-8 rounded-md border ${
                currentPage === 1
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-white text-gray-700 hover:bg-gray-50"
              }`}
            >
              <ChevronLeft size={16} />
            </button>

            {Array.from(
              { length: Math.ceil(filteredCategories.length / itemsPerPage) },
              (_, i) => i + 1
            ).map((number) => (
              <button
                key={number}
                onClick={() => paginate(number)}
                className={`w-8 h-8 rounded-md flex items-center justify-center border ${
                  currentPage === number
                    ? "bg-orange-500 text-white"
                    : "bg-white text-gray-700 hover:bg-gray-50"
                }`}
              >
                {number}
              </button>
            ))}

            <button
              onClick={() => paginate(currentPage + 1)}
              disabled={indexOfLastItem >= filteredCategories.length}
              className={`flex items-center justify-center w-8 h-8 rounded-md border ${
                indexOfLastItem >= filteredCategories.length
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-white text-gray-700 hover:bg-gray-50"
              }`}
            >
              <ChevronRight size={16} />
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default Categories;
