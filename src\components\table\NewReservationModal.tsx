import React, { useState, useRef, useEffect } from 'react';
import { IoClose } from "react-icons/io5";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { format, addDays, subDays, startOfWeek } from 'date-fns';
import GuestDetailsModal from './GuestDetailsModal';

interface NewReservationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContinue: () => void;
}

const NewReservationModal: React.FC<NewReservationModalProps> = ({ isOpen, onClose, onContinue }) => {
  const [partySize, setPartySize] = useState<number | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [currentDate, setCurrentDate] = useState(new Date());
  const [isGuestDetailsModalOpen, setIsGuestDetailsModalOpen] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  const partySizes = [1, 2, 3, 4, 5, 6, 7, '8+'];

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const generateWeekDays = (startDate: Date) => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      days.push(addDays(startDate, i));
    }
    return days;
  };

  const weekDays = generateWeekDays(startOfWeek(currentDate));

  if (!isOpen) return null;

  return (
    <>
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="bg-black/50 absolute inset-0" />
      <div ref={modalRef} className="relative bg-white rounded-lg w-[500px] p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">New Reservation</h2>
          <button 
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            className="hover:bg-gray-100 p-1 rounded-full"
          >
            <IoClose size={24} />
          </button>
        </div>

        {/* Party Size Selection */}
        <div className="mb-6">
          <label className="block text-gray-600 mb-2">Select party size</label>
          <div className="grid grid-cols-8 gap-1 rounded-full border border-gray-200 p-1">
            {partySizes.map((size, index) => (
              <button
                key={index}
                className={`py-2 rounded-full text-center ${
                  partySize === size
                    ? 'bg-orange-100 text-orange-500'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setPartySize(size as number)}
              >
                {size}
              </button>
            ))}
          </div>
        </div>

        {/* Site Selection */}
        <div className="mb-6">
          <label className="block text-gray-600 mb-2">Site</label>
          <div className="relative">
            <select className="w-full p-3 border border-gray-200 rounded-lg appearance-none">
              <option value="">Select Site</option>
              <option value="">Basement</option>
              <option value="">First Floor</option>
              <option value="">Second Floor</option>

              {/* Add your site options here */}
            </select>
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>

        {/* Tables Selection */}
        <div className="mb-6">
          <label className="block text-gray-600 mb-2">Tables</label>
          <div className="relative">
            <select className="w-full p-3 border border-gray-200 rounded-lg appearance-none">
              <option value="">Select Tables</option>
              <option value="">T-001</option>
              <option value="">T-002  </option>
              <option value="">T-003</option>
              {/* Add your table options here */}
            </select>
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>

        {/* Date Selection */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <label className="text-gray-600">Select Date</label>
            <div className="flex items-center gap-4">
              <button onClick={() => setCurrentDate(subDays(currentDate, 7))}>
                <IoIosArrowBack />
              </button>
              <span className="font-medium">{format(currentDate, 'MMMM yyyy')}</span>
              <button onClick={() => setCurrentDate(addDays(currentDate, 7))}>
                <IoIosArrowForward />
              </button>
            </div>
          </div>
          <div className="grid grid-cols-7 gap-1 rounded-full border border-gray-200 p-1">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <div key={day} className="text-center text-sm text-gray-500 py-1">
                {day}
              </div>
            ))}
            {weekDays.map((date, index) => (
              <button
                key={index}
                className={`py-2 rounded-full text-center ${
                  format(selectedDate, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
                    ? 'bg-orange-100 text-orange-500'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedDate(date)}
              >
                {format(date, 'd')}
              </button>
            ))}
          </div>
        </div>

        {/* Time Slot */}
        <div className="mb-6">
          <label className="block text-gray-600 mb-2">Time Slot Available</label>
          <p className="text-red-500">Time Slot not designed yet by the figma designer</p>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            className="flex-1 py-3 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={onContinue}
            className="flex-1 py-3 bg-[#FF5C00] text-white rounded-lg hover:bg-orange-600"
          >
            Continue
          </button>
        </div>
      </div>
    </div>

    <GuestDetailsModal
      isOpen={isGuestDetailsModalOpen}
      onClose={() => setIsGuestDetailsModalOpen(false)}
      onSubmit={() => {}}
    />
    </>
  );
};

export default NewReservationModal;


