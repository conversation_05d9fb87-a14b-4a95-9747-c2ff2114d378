import React, { useState, useEffect } from "react";
import CustomModal from "../../CustomModal";
import {type Customer } from "../../../store/api/customerApi";

interface PointsEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer | null;
  onUpdatePoints: (customerId: string, newPoints: number) => void;
}

const PointsEditModal: React.FC<PointsEditModalProps> = ({
  isOpen,
  onClose,
  customer,
  onUpdatePoints,
}) => {
  const [addPoints, setAddPoints] = useState<string>("");
  const [subtractPoints, setSubtractPoints] = useState<string>("");
  const [newPoints, setNewPoints] = useState<number>(0);

  useEffect(() => {
    if (customer) {
      const currentPoints = customer.CustomerLoyalty?.Points || 0;
      setNewPoints(currentPoints);
      setAddPoints("");
      setSubtractPoints("");
    }
  }, [customer]);

  const handleAddPointsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "" || /^\d*\.?\d*$/.test(value)) {
      setAddPoints(value);
      const addValue = value ? parseFloat(value) : 0;
      const currentPoints = customer?.CustomerLoyalty?.Points || 0;
      setNewPoints(currentPoints + addValue);
      setSubtractPoints("");
    }
  };

  const handleSubtractPointsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "" || /^\d*\.?\d*$/.test(value)) {
      setSubtractPoints(value);
      const subtractValue = value ? parseFloat(value) : 0;
      const currentPoints = customer?.CustomerLoyalty?.Points || 0;
      setNewPoints(Math.max(0, currentPoints - subtractValue));
      setAddPoints("");
    }
  };

  const handleUpdatePoints = () => {
    if (customer && customer._id) {
      onUpdatePoints(customer._id, newPoints);
      onClose();
    }
  };

  if (!customer) return null;

  const currentPoints = customer.CustomerLoyalty?.Points || 0;
  const formattedCurrentPoints = currentPoints.toFixed(2);
  const formattedNewPoints = newPoints.toFixed(2);

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Points Adjustment"
      width="max-w-xl"
    >
      <div className="p-6">
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-4">Customer Details</h2>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-gray-600">Customer Name</p>
              <p className="font-semibold">{`${customer.FirstName} ${customer.LastName}`}</p>
            </div>
            <div>
              <p className="text-gray-600">Card Number</p>
              <p className="font-semibold">{customer.CustomerLoyalty?.CardNo || "N/A"}</p>
            </div>
          </div>
          
          <div className="mb-4">
            <p className="text-gray-600">Old Points</p>
            <div className="bg-gray-200 p-3 rounded-md text-center">
              {formattedCurrentPoints}
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-gray-600 mb-2">Add points</p>
              <input
                type="text"
                value={addPoints}
                onChange={handleAddPointsChange}
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                placeholder="0.00"
              />
            </div>
            <div>
              <p className="text-gray-600 mb-2">Subtract points</p>
              <input
                type="text"
                value={subtractPoints}
                onChange={handleSubtractPointsChange}
                className="w-full p-3 bg-gray-100 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                placeholder="0.00"
              />
            </div>
          </div>
          
          <div className="mb-6">
            <p className="text-gray-600">New Points</p>
            <p className="text-2xl font-bold">{formattedNewPoints}</p>
          </div>
          
          <div className="flex justify-between">
            <button
              onClick={onClose}
              className="px-8 py-2 border border-orange-500 text-orange-500 rounded-full font-medium hover:bg-orange-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleUpdatePoints}
              className="px-8 py-2 bg-orange-500 text-white rounded-full font-medium hover:bg-orange-600 transition-colors"
            >
              Update Points
            </button>
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default PointsEditModal;
