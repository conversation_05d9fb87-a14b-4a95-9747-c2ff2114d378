import { useState, useRef, useEffect } from 'react';
import { Calendar, ChevronLeft, ChevronRight, Download, FileText, Trash2, Edit2, <PERSON><PERSON>he<PERSON>, Check } from 'lucide-react';

export default function TaxPaid() {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const datePickerRef = useRef<HTMLDivElement>(null);

  // Sample tax data
  const taxData = [
    { 
      id: 1, 
      no: '01',
      date: '01 Nov 2024', 
      recipientDept: 'FBR', 
      referenceNo: '4834FBR343', 
      taxDetails: ['Sales Tax', 'Income Tax'], 
      paymentSchedule: 'Annual', 
      amount: 3200.00 
    },
  ];

  const toggleDatePicker = () => {
    setIsDatePickerOpen(!isDatePickerOpen);
  };

  const applyDates = () => {
    // Handle applying the selected dates
    setIsDatePickerOpen(false);
  };

  // Close datepicker when clicking outside
  useEffect(() => {
    function handleClickOutside(event: { target: any; }) {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target)) {
        setIsDatePickerOpen(false);
      }
    }
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [datePickerRef]);

  return (
    <div className="w-full p-2">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <h1 className="text-2xl font-bold text-gray-800 mr-2">Tax Paid</h1>
          <div className="flex items-center bg-orange-500 text-white rounded-full h-6 w-6 justify-center text-xs">
            1
          </div>
          <p className="ml-2 text-gray-600">These taxes are paid to the Government</p>
        </div>
        <div className="flex gap-4">
          <div className="flex relative">
            <button 
              onClick={toggleDatePicker}
              className="flex items-center gap-2 px-4 py-2 bg-gray-100 rounded-lg border border-gray-300 text-gray-600"
            >
              {startDate ? startDate : 'Start date'} → {endDate ? endDate : 'End date'}
              <Calendar size={18} />
            </button>
            
            {/* Date Picker Dropdown */}
            {isDatePickerOpen && (
              <div 
                ref={datePickerRef}
                className="absolute top-12 right-0 bg-white p-4 rounded-lg shadow-lg border border-gray-200 z-10 w-96"
              >
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Start Date</label>
                    <div className="relative">
                      <input 
                        type="text" 
                        placeholder="mm/dd/yyyy" 
                        className="w-full p-2 border border-gray-300 rounded-md"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                      />
                      <button className="absolute right-3 top-2.5 text-gray-500">
                        <Calendar size={20} />
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">End Date</label>
                    <div className="relative">
                      <input 
                        type="text" 
                        placeholder="mm/dd/yyyy" 
                        className="w-full p-2 border border-gray-300 rounded-md"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                      />
                      <button className="absolute right-3 top-2.5 text-gray-500">
                        <Calendar size={20} />
                      </button>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-4 mt-4">
                  <button 
                    onClick={applyDates}
                    className="bg-orange-500 hover:bg-orange-500 text-white py-2 px-4 rounded-md flex-1"
                  >
                    Apply
                  </button>
                  <button 
                    onClick={() => setIsDatePickerOpen(false)}
                    className="border border-orange-300 text-orange-500 py-2 px-4 rounded-md flex-1"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </div>
          
          <button className="border border-orange-500 text-orange-500 hover:bg-orange-50 px-4 py-2 rounded-lg flex items-center gap-2">
            <span>Download CSV</span>
            <Download size={18} />
          </button>
          
          <button className="bg-orange-500 hover:bg-orange-500 text-white px-4 py-2 rounded-lg flex items-center gap-2">
            <span>Tax Submission Form</span>
            <FileText size={18} />
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-lg overflow-hidden">
        {/* Table Header */}
        <div className="grid grid-cols-7 p-4 bg-orange-50 font-medium text-gray-700 border-b border-orange-100">
          <div>No</div>
          <div>Date</div>
          <div>Receipient Department</div>
          <div>Reference No</div>
          <div>Tax Details</div>
          <div>Payment Schedule</div>
          <div>Amount</div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-gray-200">
          {taxData.map((item) => (
            <div key={item.id} className="grid grid-cols-7 p-4 items-center hover:bg-orange-50">
              <div className="text-gray-500">{item.no}</div>
              <div className="text-gray-700">{item.date}</div>
              <div className="text-gray-700">{item.recipientDept}</div>
              <div className="text-gray-700">{item.referenceNo}</div>
              <div className="flex flex-wrap gap-1">
                {item.taxDetails.map((tax, index) => (
                  <span key={index} className="bg-orange-100 text-orange-500 border border-orange-300 rounded-full px-3 py-1 text-xs">
                    {tax}
                  </span>
                ))}
              </div>
              <div className="text-gray-700">{item.paymentSchedule}</div>
              <div className="flex justify-between items-center">
                <span className="text-gray-800 font-medium">${item.amount.toFixed(2)}</span>
                <div className="flex gap-2">
                  <button className="text-green-500 hover:text-green-700">
                    <FileCheck size={18} />
                  </button>
                  <button className="text-blue-500 hover:text-blue-700">
                    <Edit2 size={18} />
                  </button>
                  <button className="text-red-500 hover:text-red-700">
                    <Trash2 size={18} />
                  </button>
                  <button className="text-green-500 hover:text-green-700">
                    <Check size={18} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center mt-8 gap-2">
        <button 
          className="p-2 border border-gray-300 rounded hover:bg-gray-100"
          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
          disabled={currentPage === 1}
        >
          <ChevronLeft size={16} />
        </button>
        <button className="h-8 w-8 bg-gray-200 flex items-center justify-center rounded">
          1
        </button>
        <button 
          className="p-2 border border-gray-300 rounded hover:bg-gray-100"
          onClick={() => setCurrentPage(prev => prev + 1)}
        >
          <ChevronRight size={16} />
        </button>
      </div>
    </div>
  );
}