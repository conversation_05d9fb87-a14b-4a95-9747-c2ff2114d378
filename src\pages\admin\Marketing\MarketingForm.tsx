import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronDown, X } from 'lucide-react';
import { useGetCustomersQuery } from "../../../store/api/customerApi";
import { useCreateEmailMarketingMutation } from "../../../store/api/emailMarketingApi";
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';

// Define TypeScript interfaces
interface Customer {
  _id: string;
  FirstName?: string;
  LastName?: string;
  Email: string;
}

interface Recipient {
  id: string;
  name: string;
  email: string;
  selected: boolean;
}

interface EmailMarketingData {
  userId: string;
  subject: string;
  message: string;
  email: string[];
}

export default function AddEmailCampaign() {
  const [emailSubject, setEmailSubject] = useState<string>('');
  const [customRecipientInput, setCustomRecipientInput] = useState<string>('');
  const [customRecipients, setCustomRecipients] = useState<string[]>([]);
  const [emailContent, setEmailContent] = useState<string>('');
  const [showRecipientDropdown, setShowRecipientDropdown] = useState<boolean>(false);
  const [selectedCustomers, setSelectedCustomers] = useState<Recipient[]>([]);
  
  // RTK Query hooks
  const { data: customersData, isLoading: isLoadingCustomers } = useGetCustomersQuery();
  const [createEmailMarketing, { isLoading: isSubmitting }] = useCreateEmailMarketingMutation();

  // Convert customers data to recipients format
  const [recipients, setRecipients] = useState<Recipient[]>([]);

  useEffect(() => {
    if (customersData) {
      setRecipients(customersData.map((customer: Customer) => ({
        id: customer._id,
        name: `${customer.FirstName || ''} ${customer.LastName || ''}`.trim(),
        email: customer.Email,
        selected: false
      })));
    }
  }, [customersData]);

  const handleToggleRecipient = (id: string): void => {
    const updatedRecipients = recipients.map(recipient => 
      recipient.id === id ? { ...recipient, selected: !recipient.selected } : recipient
    );
    
    setRecipients(updatedRecipients);
    
    // Update selected customers list
    const selected = updatedRecipients.filter(r => r.selected);
    setSelectedCustomers(selected);
  };

  const handleAddCustomRecipient = (e: React.KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === 'Enter' && customRecipientInput.trim()) {
      e.preventDefault();
      
      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(customRecipientInput)) {
        toast.error('Please enter a valid email address');
        return;
      }
      
      // Check if email already exists
      if (customRecipients.includes(customRecipientInput)) {
        toast.error('This email is already added');
        return;
      }
      
      // Check if email already exists in selected customers
      if (selectedCustomers.some(customer => customer.email === customRecipientInput)) {
        toast.error('This email is already selected from customers list');
        return;
      }
      
      setCustomRecipients([...customRecipients, customRecipientInput]);
      setCustomRecipientInput('');
    }
  };

  const removeCustomRecipient = (email: string): void => {
    setCustomRecipients(customRecipients.filter(recipient => recipient !== email));
  };

  const removeSelectedCustomer = (id: string): void => {
    handleToggleRecipient(id);
  };

  const navigate =useNavigate()

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    
    // Get all selected customer emails
    const customerEmails = selectedCustomers.map(customer => customer.email);
    
    // Combine with custom emails
    const allEmails = [...customerEmails, ...customRecipients];
    
    if (allEmails.length === 0) {
      toast.error('Please select at least one recipient');
      return;
    }
    
    const userId = localStorage.getItem('userId');
    if (!userId) {
      toast.error('User authentication required');
      return;
    }
    
    try {
      const emailData: EmailMarketingData = {
        userId,
        subject: emailSubject,
        message: emailContent,
        email: allEmails
      };
      
      await createEmailMarketing(emailData).unwrap();
      toast.success('Email campaign created successfully');
      
      // Reset form
      setEmailSubject('');
      setCustomRecipients([]);
      setCustomRecipientInput('');
      setEmailContent('');
      setRecipients(recipients.map(r => ({ ...r, selected: false })));
      setSelectedCustomers([]);
      navigate(-1)
    } catch (error: any) {
      toast.error(error?.data?.message || 'Failed to create email campaign');
    }
  };

  const handleCancel = (): void => {
    // Navigation logic here
    // history.push('/email-campaigns'); // If using react-router
  };

  return (
    <div className="bg-white min-h-screen p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-6 text-gray-800">
          <ChevronLeft className="w-5 h-5 mr-2" />
          <h1 className="text-xl font-medium">Add Email Campaign</h1>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Email Campaign Details Section */}
          <div className="mb-6">
            <div className="bg-orange-50 py-3 px-4 text-gray-800 font-medium rounded-t-lg border-b border-orange-100">
              Email Campaign Details
            </div>
            <div className="bg-white p-6 rounded-b-lg border border-gray-200 border-t-0 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Email Subject */}
                <div>
                  <label className="block text-sm mb-2">
                    Email Subject <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    placeholder="Enter Email Subject"
                    value={emailSubject}
                    onChange={(e) => setEmailSubject(e.target.value)}
                    className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                    required
                  />
                </div>

                {/* Email Recipients */}
                <div>
                  <label className="block text-sm mb-2">
                    Email Recipients <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <button
                      type="button"
                      className="w-full bg-white border border-gray-300 rounded-md p-2 flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-orange-500"
                      onClick={() => setShowRecipientDropdown(!showRecipientDropdown)}
                    >
                      <span className="text-gray-500">
                        {selectedCustomers.length > 0 
                          ? `${selectedCustomers.length} recipients selected` 
                          : 'Select Email Recipients'}
                      </span>
                      <ChevronDown className="w-4 h-4 text-gray-500" />
                    </button>
                    
                    {showRecipientDropdown && (
                      <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg py-1 max-h-60 overflow-y-auto">
                        {isLoadingCustomers ? (
                          <div className="px-3 py-2 text-center text-gray-500">Loading customers...</div>
                        ) : recipients.length === 0 ? (
                          <div className="px-3 py-2 text-center text-gray-500">No customers found</div>
                        ) : (
                          recipients.map((recipient) => (
                            <div key={recipient.id} className="px-3 py-2 hover:bg-gray-50">
                              <label className="flex items-center space-x-3 cursor-pointer">
                                <input
                                  type="checkbox"
                                  className="w-4 h-4 text-white bg-red-500 border-gray-300 rounded focus:ring-red-500"
                                  checked={recipient.selected}
                                  onChange={() => handleToggleRecipient(recipient.id)}
                                />
                                <span>{recipient.name} {recipient.email ? `(${recipient.email})` : ''}</span>
                              </label>
                            </div>
                          ))
                        )}
                      </div>
                    )}
                  </div>
                  
                  {/* Selected Recipients Tags */}
                  {selectedCustomers.length > 0 && (
                    <div className="flex flex-wrap mt-2 gap-2">
                      {selectedCustomers.map(customer => (
                        <div 
                          key={customer.id}
                          className="bg-orange-500 text-white text-sm px-2 py-1 rounded-md flex items-center"
                        >
                          <span>{customer.email}</span>
                          <button 
                            type="button" 
                            onClick={() => removeSelectedCustomer(customer.id)}
                            className="ml-1 text-white"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Custom Email Recipients */}
              <div>
                <label className="block text-sm mb-2">
                  Custom Email Recipients
                </label>
                <input
                  type="email"
                  placeholder="Enter custom email recipients and press Enter to add"
                  value={customRecipientInput}
                  onChange={(e) => setCustomRecipientInput(e.target.value)}
                  onKeyDown={handleAddCustomRecipient}
                  className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                />
                
                {/* Custom Recipients Tags */}
                {customRecipients.length > 0 && (
                  <div className="flex flex-wrap mt-2 gap-2">
                    {customRecipients.map(email => (
                      <div 
                        key={email}
                        className="bg-orange-500 text-white text-sm px-2 py-1 rounded-md flex items-center"
                      >
                        <span>{email}</span>
                        <button 
                          type="button" 
                          onClick={() => removeCustomRecipient(email)}
                          className="ml-1 text-white"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Compose Email */}
              <div>
                <label className="block text-sm mb-2">
                  Compose Email <span className="text-red-500">*</span>
                </label>
                <textarea
                  placeholder="Compose Email"
                  value={emailContent}
                  onChange={(e) => setEmailContent(e.target.value)}
                  className="w-full border border-gray-300 rounded-md p-2 h-40 focus:outline-none focus:ring-2 focus:ring-orange-500"
                  required
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 flex items-center"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Add Email Campaign'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}