import React, { useState, useEffect } from "react";
import CustomModal from "../../CustomModal";
import { IoSearchOutline } from "react-icons/io5";
import { IoIosArrowDown, IoIosArrowUp } from "react-icons/io";
import { useGetCustomersQuery } from "../../../store/api/customerApi";

interface CustomerBalanceProps {
  isOpen: boolean;
  onClose: () => void;
}

interface CustomerDetails {
  id: string; // Adding unique ID for better tracking
  customerName: string;
  cardNumber: string;
  cardType: string;
  customerType: string;
  phoneNumber: string;
  emailAddress: string;
  balance: string;
  totalPoints: number;
  visits: number;
  lastVisit: string;
  address: string;
  city: string;
  state: string;
}

const CustomerBalance: React.FC<CustomerBalanceProps> = ({
  isOpen,
  onClose,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedCustomerId, setExpandedCustomerId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredCustomers, setFilteredCustomers] = useState<CustomerDetails[]>([]);
  const itemsPerPage = 5; // Show exactly 5 customers per page

  // const userId = localStorage.getItem("userId);
  const { data, isLoading, error } = useGetCustomersQuery();

  // Transform API data to match the CustomerDetails interface
  useEffect(() => {
    if (data) {
      const transformedData: CustomerDetails[] = Object.values(data).map((customer: any) => {
        const fullName = `${customer.FirstName || customer.firstName || ""} ${customer.LastName || customer.lastName || ""}`.trim();

        return {
          id: customer._id || customer.id || `${fullName}-${customer.Phone || customer.phone || ""}`,
          customerName: fullName,
          cardNumber: customer.CustomerLoyalty?.CardNo || "N/A",
          cardType: "N/A", // Not provided in API data
          customerType: "N/A", // Not provided in API data
          phoneNumber: customer.Phone || customer.phone || "",
          emailAddress: customer.Email || customer.email || "",
          balance: customer.CustomerLoyalty?.creditLimits ? customer.CustomerLoyalty.creditLimits.toFixed(2) : "0.00",
          totalPoints: customer.CustomerLoyalty?.Points || 0,
          visits: customer.visits || 0,
          lastVisit: customer.CustomerLoyalty?.LastVisit
            ? new Date(customer.CustomerLoyalty.LastVisit).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
            : "",
          address: customer.Address || customer.address || "",
          city: customer.City || customer.city || "",
          state: customer.State || customer.state || "",
        };
      });

      setFilteredCustomers(transformedData);
    }
  }, [data]);

  // Filter customers based on search term
  useEffect(() => {
    if (data) {
      const transformed = Object.values(data).map((customer: any) => {
        const fullName = `${customer.FirstName || customer.firstName || ""} ${customer.LastName || customer.lastName || ""}`.trim();

        return {
          id: customer._id || customer.id || `${fullName}-${customer.Phone || customer.phone || ""}`,
          customerName: fullName,
          cardNumber: customer.CustomerLoyalty?.CardNo || "N/A",
          cardType: "N/A",
          customerType: "N/A",
          phoneNumber: customer.Phone || customer.phone || "",
          emailAddress: customer.Email || customer.email || "",
          balance: customer.CustomerLoyalty?.creditLimits ? customer.CustomerLoyalty.creditLimits.toFixed(2) : "0.00",
          totalPoints: customer.CustomerLoyalty?.Points || 0,
          visits: customer.visits || 0,
          lastVisit: customer.CustomerLoyalty?.LastVisit
            ? new Date(customer.CustomerLoyalty.LastVisit).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
            : "",
          address: customer.Address || customer.address || "",
          city: customer.City || customer.city || "",
          state: customer.State || customer.state || "",
        };
      });

      const filtered = transformed.filter((customer) =>
        customer.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.phoneNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.emailAddress.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.cardNumber.toLowerCase().includes(searchTerm.toLowerCase())
      );

      setFilteredCustomers(filtered);
      setCurrentPage(1); // Reset to first page when search changes
      setExpandedCustomerId(null); // Close any expanded customer when search changes
    }
  }, [searchTerm, data]);

  const toggleExpand = (customerId: string) => {
    setExpandedCustomerId(expandedCustomerId === customerId ? null : customerId);
  };

  // Pagination logic - ensure we always show exactly 5 customers
  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);


  const getCurrentPageCustomers = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredCustomers.slice(startIndex, startIndex + itemsPerPage);
  };

  const currentCustomers = getCurrentPageCustomers();

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
      setExpandedCustomerId(null); // Close any expanded customer when changing pages
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
      setExpandedCustomerId(null); // Close any expanded customer when changing pages
    }
  };

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border border-gray-300 rounded-lg">
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage > 1 ? 'text-black cursor-pointer' : 'text-[#9C9C9C] cursor-not-allowed'}`}
          onClick={goToPreviousPage}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage < totalPages ? 'text-black cursor-pointer' : 'text-[#9C9C9C] cursor-not-allowed'}`}
          onClick={goToNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          Next →
        </button>
      </div>
      <div className="text-sm text-gray-500">
        Showing {Math.min(filteredCustomers.length, 1 + (currentPage - 1) * itemsPerPage)}-
        {Math.min(currentPage * itemsPerPage, filteredCustomers.length)} of {filteredCustomers.length} customers
      </div>
      <button
        onClick={onClose}
        className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
      >
        Cancel
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Customer Balance"
      width="max-w-7xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search Bar */}
        <div className="relative mb-6">
          <IoSearchOutline
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={20}
          />
          <input
            type="text"
            placeholder="Search Customer"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-orange-500"
          />
        </div>

        {/* Customer Table */}
        <div className="mb-6">
          <div className="grid grid-cols-6 gap-4 px-4 py-2 bg-gray-50 rounded-t-lg">
            <div className="text-gray-600 font-medium">Customer Name</div>
            <div className="text-gray-600 font-medium">Card Number</div>
            <div className="text-gray-600 font-medium">Card Type</div>
            <div className="text-gray-600 font-medium">Customer Type</div>
            <div className="text-gray-600 font-medium">Phone Number</div>
            <div className="text-gray-600 font-medium">Email Address</div>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">Error loading customers. Please try again.</div>
          ) : currentCustomers.length === 0 ? (
            <div className="text-center py-8">No customers found.</div>
          ) : (
            <div className="divide-y divide-gray-100">
              {/* Fixed height container to ensure consistent display */}
              <div className="overflow-y-auto" style={{ maxHeight: expandedCustomerId ? 'auto' : '350px' }}>
                {currentCustomers.map((customer) => (
                  <div key={customer.id}>
                    <div
                      className="grid grid-cols-6 gap-4 px-4 py-3 hover:bg-gray-50 cursor-pointer"
                      onClick={() => toggleExpand(customer.id)}
                    >
                      <div className="flex items-center gap-2">
                        {customer.customerName}
                        {expandedCustomerId === customer.id ? (
                          <IoIosArrowUp className="text-gray-500" />
                        ) : (
                          <IoIosArrowDown className="text-gray-500" />
                        )}
                      </div>
                      <div>{customer.cardNumber}</div>
                      <div>{customer.cardType}</div>
                      <div>{customer.customerType}</div>
                      <div>{customer.phoneNumber}</div>
                      <div>{customer.emailAddress}</div>
                    </div>

                    {expandedCustomerId === customer.id && (
                      <div className="px-4 py-3 bg-gray-50">
                        <div className="grid grid-cols-4 gap-4 mb-4">
                          <div>
                            <div className="text-sm text-gray-500">Balance</div>
                            <div>{customer.balance}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">
                              Total Points
                            </div>
                            <div>{customer.totalPoints.toFixed(2)}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">Visits</div>
                            <div>{customer.visits}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">Last Visit</div>
                            <div>{customer.lastVisit || "N/A"}</div>
                          </div>
                        </div>
                        <div className="grid grid-cols-3 gap-4">
                          <div>
                            <div className="text-sm text-gray-500">Address</div>
                            <div>{customer.address || "N/A"}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">City</div>
                            <div>{customer.city || "N/A"}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">State</div>
                            <div>{customer.state || "N/A"}</div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </CustomModal>
  );
};

export default CustomerBalance;