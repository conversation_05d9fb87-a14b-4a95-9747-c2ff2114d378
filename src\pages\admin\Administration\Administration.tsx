import React, { useState } from "react";

import { Download } from 'lucide-react';

// Types
interface Business {
  name: string;
  phone: string;
  address: string;
  status: boolean;
}

interface PaymentType {
  id: number;
  name: string;
  icon: string;
  status: string;
}

interface Tax {
  id: number;
  name: string;
  value: string;
  status: boolean;
}

interface TaxCollected {
  id: string;
  date: string;
  taxesApplied: string;
  totalOrders: number;
  amount: string;
}

interface TaxPaid {
  id: string;
  date: string;
  department: string;
  reference: string;
  taxDetails: string[];
  schedule: string;
  amount: string;
}

interface CustomizationItem {
  id: number;
  name: string;
  status: boolean;
}

// interface Transaction {
//   id: string;
//   name: string;
//   email: string;
//   plan: string;
//   payments: string;
//   count: number;
//   startDate: string;
// }

interface SubscriptionInfo {
  name: string;
  price: number;
  trialDays: number;
  cardType: string;
  cardNumber: string;
  nextPayment: string;
  status: string;
}

// Main component
const Administration: React.FC = () => {
  const [expandedRow, setExpandedRow] = useState(null);

  const toggleRow = (index: any) => {
    setExpandedRow(expandedRow === index ? null : index);
  };

  const [activeTab, setActiveTab] = useState("Business Name");
  const [searchCustomization, setSearchCustomization] = useState("");
  const [customizationItems, setCustomizationItems] = useState<
    CustomizationItem[]
  >([
    { id: 1, name: "Parent categories", status: false },
    { id: 2, name: "Completed Orders", status: true },
    { id: 3, name: "Ingredients", status: true },
    { id: 4, name: "showModifiers", status: true },
    { id: 5, name: "All Orders", status: true },
  ]);

  // Sample data
  const business: Business = {
    name: "Saad Foods",
    phone: "2345234523535345",
    address: "Florida Swabi",
    status: true,
  };

  const paymentTypes: PaymentType[] = [
    { id: 1, name: "Stripe Checkout", icon: "S", status: "Active" },
    { id: 2, name: "Terminal Pay", icon: "terminal", status: "Active" },
    { id: 3, name: "Cash", icon: "cash", status: "Active" },
  ];

  const taxes: Tax[] = [
    { id: 1, name: "GST", value: "5%", status: true },
    { id: 2, name: "Income Tax", value: "8%", status: true },
  ];

  const taxesCollected: TaxCollected[] = [
    {
      id: "01",
      date: "24th October 2024",
      taxesApplied: "GST (5%) VAT (3%)",
      totalOrders: 12,
      amount: "$250.00",
    },
    {
      id: "02",
      date: "25th October 2024",
      taxesApplied: "GST (5%)",
      totalOrders: 8,
      amount: "$180.00",
    },
    {
      id: "03",
      date: "26th October 2024",
      taxesApplied: "VAT (3%)",
      totalOrders: 5,
      amount: "$120.00",
    },
    {
      id: "04",
      date: "27th October 2024",
      taxesApplied: "GST (5%) VAT (3%)",
      totalOrders: 15,
      amount: "$300.00",
    },
  ];

  const taxesPaid: TaxPaid[] = [
    {
      id: "01",
      date: "01 Nov 2024",
      department: "FBR",
      reference: "4834FBR343",
      taxDetails: ["Sales Tax", "Income Tax"],
      schedule: "Annual",
      amount: "$3,200.00",
    },
    {
      id: "02",
      date: "15 Nov 2024",
      department: "IRS",
      reference: "7856IRS123",
      taxDetails: ["Property Tax"],
      schedule: "Quarterly",
      amount: "$1,500.00",
    },
  ];

  const subscription: SubscriptionInfo = {
    name: "Best Plan",
    price: 5,
    trialDays: 7,
    cardType: "Mastercard",
    cardNumber: "3456",
    nextPayment: "23 Nov, 2024",
    status: "Currently Active",
  };


  const toggleCustomizationStatus = (id: number) => {
    setCustomizationItems(
      customizationItems.map((item) =>
        item.id === id ? { ...item, status: !item.status } : item
      )
    );
  };

  const filteredCustomizations = customizationItems.filter((item) =>
    item.name.toLowerCase().includes(searchCustomization.toLowerCase())
  );

  return (
    <div className="p-4">
      <div className="p-4 border bg-white mb-4 rounded-2xl border-gray-200">
        <h1 className="text-xl font-bold text-gray-800">Administration</h1>
      </div>

      <div className="px-4 pt-4">
        <div className="flex flex-wrap gap-2 mb-6">
          {[
            "Business Name",
            "Payment Types",
            "Tax",
            "Tax Collected",
            "Tax Paid",
            "Customization",
            "Subscription Plan",
          ].map((tab) => (
            <button
              key={tab}
              className={`px-4 py-2 rounded-full text-sm ${
                activeTab === tab
                  ? "bg-orange-500 text-white"
                  : "bg-white text-gray-700 border border-gray-300"
              }`}
              onClick={() => setActiveTab(tab)}
            >
              {tab}
            </button>
          ))}
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-100">
        {/* Dynamic Content */}
        <div className="p-4">
          {/* Business Name Tab */}
          {activeTab === "Business Name" && (
            <div>
              <h2 className="text-lg font-medium text-gray-800 mb-4">
                Business Info
              </h2>
              <div className="bg-white rounded-2xl border border-gray-200 overflow-x-auto">
  <table className="w-full">
    <thead>
      <tr className="bg-orange-50">
        <th className="p-4 text-left font-medium">Business Name</th>
        <th className="p-4 text-left font-medium">Phone No</th>
        <th className="p-4 text-left font-medium">Address</th>
        <th className="p-4 text-left font-medium">Status</th>
        <th className="p-4 text-right font-medium">Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr className="border-t border-gray-200">
        <td className="p-4">{business.name}</td>
        <td className="p-4">{business.phone}</td>
        <td className="p-4">{business.address}</td>
        <td className="p-4">
          <span className="px-4 py-1 bg-green-100 text-green-600 rounded-full text-sm">
            {business.status ? "Active" : "Inactive"}
          </span>
        </td>
        <td className="p-4 text-right">
          <div className="flex justify-end space-x-2">
            <button className="text-blue-500 hover:text-blue-700">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
            </button>
            <button className="text-red-500 hover:text-red-700">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
            </div>
          )}

          {/* Payment Types Tab */}
          {activeTab === "Payment Types" && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-medium text-gray-800">
                  Payment Type
                </h2>
                <button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center">
                  Add Payment Type
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 ml-1"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>

              <div className="grid md:grid-cols-3 grid-col-1 gap-4">
                {paymentTypes.map((type) => (
                  <div
                    key={type.id}
                    className="bg-white p-4 rounded-xl shadow-sm flex items-center border border-gray-200 justify-between"
                  >
                    {/* Icon Column */}
                    <div className="h-16 w-16 rounded-lg flex items-center justify-center bg-gray-100">
                      {type.name === "Stripe Checkout" && (
                        <div className="bg-indigo-500 h-12 w-12 rounded-lg flex items-center justify-center text-white text-2xl font-bold">
                          S
                        </div>
                      )}
                      {type.name === "Terminal Pay" && (
                        <div className="h-12 w-12 flex items-center justify-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-8 w-8 text-gray-600"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" />
                          </svg>
                        </div>
                      )}
                      {type.name === "Cash" && (
                        <div className="h-12 w-12 flex items-center justify-center text-2xl">
                          💰
                        </div>
                      )}
                    </div>

                    {/* Info + Actions */}
                    <div className="ml-4 flex-1">
                      <div className="font-semibold text-gray-800">
                        {type.name}
                      </div>
                      <div className="text-green-600 text-sm">
                        {type.status}
                      </div>

                      <div className="flex items-center mt-1 space-x-2">
                        <button className="text-blue-500 text-sm flex items-center">
                          Edit
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 ml-1"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M17.414 2.586a2 2 0 010 2.828L8.828 14H6v-2.828l8.586-8.586a2 2 0 012.828 0z" />
                          </svg>
                        </button>
                        <button className="text-red-500 text-sm flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Tax Tab */}
          {activeTab === "Tax" && (
            <div>
              <div className="flex md:flex-row flex-col justify-between">
                <h2 className="text-3xl md:text-start text-center font-medium text-gray-800 mb-4">Tax</h2>
                <div className="flex md:flex-row flex-col space-y-2 md:space-y-0 justify-between mb-4">
                  <div className="relative mx-3">
                    <input
                      type="text"
                      placeholder="Search Tax"
                      className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg w-full"
                    />
                    <div className="absolute right-3 top-2.5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                  <button className="bg-orange-500 text-white px-4 py-2 w-full rounded-lg flex items-center">
                    Add Tax
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 ml-1"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="bg-white rounded-2xl border border-gray-200 overflow-x-auto">
  <table className="w-full">
    <thead>
      <tr className="bg-orange-50">
        <th className="p-4 text-left font-medium">No</th>
        <th className="p-4 text-left font-medium">Name</th>
        <th className="p-4 text-left font-medium col-span-2">Tax Value</th>
        <th className="p-4 text-left font-medium">Status</th>
        <th className="p-4 text-right font-medium">Actions</th>
      </tr>
    </thead>
    <tbody>
      {taxes.map((tax) => (
        <tr 
          key={tax.id}
          className="border-t border-gray-200 hover:bg-orange-50"
        >
          <td className="p-4">{tax.id}</td>
          <td className="p-4">{tax.name}</td>
          <td className="p-4 col-span-2">{tax.value}</td>
          <td className="p-4">
            <span className={`px-4 py-1 rounded-full text-sm ${
              tax.status ? "bg-green-100 text-green-600" : "bg-red-100 text-red-600"
            }`}>
              {tax.status ? "Active" : "Inactive"}
            </span>
          </td>
          <td className="p-4 text-right">
            <div className="flex justify-end space-x-2">
              <button className="text-blue-500 hover:text-blue-700">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
              </button>
              <button className="text-red-500 hover:text-red-700">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </td>
        </tr>
      ))}
    </tbody>
  </table>
</div>
              <div className="flex justify-start space-x-2 mt-4">
                <button className="p-2 border border-gray-300 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                <button className="py-2 px-3 border border-gray-300 rounded-lg bg-orange-500 text-white">
                  1
                </button>
                <button className="p-2 border border-gray-300 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Tax Collected Tab */}
          {activeTab === "Tax Collected" && (
            <div>
              <div className="flex justify-between md:flex-row flex-col">
                <h2 className="text-3xl md:text-start text-center font-medium text-gray-800 mb-4">
                  Tax Collected
                </h2>
                <div className="flex justify-end  md:flex-row flex-col mb-4 md:space-y-0 space-y-2">
                  <div className="mr-2">
                    <input
                      type="text"
                      placeholder="Start date — End date"
                      className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg w-full"
                    />
                  </div>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded-lg flex items-center">
                    Download CSV
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 ml-1"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 overflow-x-auto">
  <table className="w-full">
    <thead>
      <tr className="bg-orange-50">
        <th className="p-4 text-left font-medium">No</th>
        <th className="p-4 text-left font-medium">Date</th>
        <th className="p-4 text-left font-medium">Taxes Applied</th>
        <th className="p-4 text-left font-medium">Total Orders</th>
        <th className="p-4 text-right font-medium">Amount</th>
      </tr>
    </thead>
    <tbody>
      {taxesCollected.map((tax, index) => (
        <tr 
          key={index}
          className="border-t border-gray-200 hover:bg-orange-50"
        >
          <td className="p-4">{tax.id}</td>
          <td className="p-4">{tax.date}</td>
          <td className="p-4">{tax.taxesApplied}</td>
          <td className="p-4">{tax.totalOrders}</td>
          <td className="p-4 text-right">
            <div className="flex items-center justify-end space-x-4">
              <span>{tax.amount}</span>
              <button className="text-red-500 hover:text-red-700 ml-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </td>
        </tr>
      ))}
    </tbody>
  </table>
</div>
              <div className="flex justify-start space-x-2 mt-4">
                <button className="p-2 border border-gray-300 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                <button className="py-2 px-3 border border-gray-300 rounded-lg bg-orange-500 text-white">
                  1
                </button>
                <button className="p-2 border border-gray-300 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Tax Paid Tab */}
          {activeTab === "Tax Paid" && (
            <div>
              <div className="flex justify-between md:flex-row flex-col">
                <div>
                  <h2 className="text-lg font-medium text-gray-800 mb-4">
                    Tax Paid
                  </h2>
                  <div className="flex items-center mb-2">
                    <div className="bg-orange-500 text-white rounded-full w-6 h-6 flex items-center justify-center mr-2">
                      <span className="text-xs">i</span>
                    </div>
                    <span className="text-sm text-gray-600">
                      These taxes are paid to the Government
                    </span>
                  </div>
                </div>

                <div className="flex justify-end  items-center md:flex-row  flex-col md:space-y-0 space-y-2 mb-4">
                  <div className="mr-2">
                    <input
                      type="text"
                      placeholder="Start date — End date"
                      className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg"
                    />
                  </div>

                  <div>
                    <button className="bg-white p-2 text-orange-500 border border-orange-500  rounded-lg flex items-center mr-2">
                      Download CSV
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 ml-1"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                  <div>
                    <button className="bg-orange-500 p-2 text-white rounded-lg flex items-center">
                      Tax Submission Form
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 ml-1"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              <div className="bg-white border border-gray-200 rounded-2xl overflow-x-auto">
  <table className="w-full">
    <thead>
      <tr className="bg-orange-100">
        <th className="p-4 text-left font-medium">No</th>
        <th className="p-4 text-left font-medium">Date</th>
        <th className="p-4 text-left font-medium">Recipient Department</th>
        <th className="p-4 text-left font-medium">Reference No</th>
        <th className="p-4 text-left font-medium">Tax Details</th>
        <th className="p-4 text-left font-medium">Payment Schedule</th>
        <th className="p-4 text-left font-medium">Amount</th>
        <th className="p-4 text-right font-medium">Action</th>
      </tr>
    </thead>
    <tbody>
      {taxesPaid.map((tax, index) => (
        <React.Fragment key={index}>
          <tr className="border-t border-gray-100 hover:bg-orange-50">
            <td className="p-4 align-middle">{tax.id}</td>
            <td className="p-4 align-middle">{tax.date}</td>
            <td className="p-4 align-middle">{tax.department}</td>
            <td className="p-4 align-middle">{tax.reference}</td>
            <td className="p-4 align-middle">
              <div className="flex flex-wrap gap-1">
                {tax.taxDetails.map((detail, idx) => (
                  <span
                    key={idx}
                    className="px-2 py-1 bg-orange-100 text-orange-600 rounded-full text-xs"
                  >
                    {detail}
                  </span>
                ))}
              </div>
            </td>
            <td className="p-4 align-middle">{tax.schedule}</td>
            <td className="p-4 align-middle">{tax.amount}</td>
            <td className="p-4 align-middle text-right">
              <div className="flex items-center justify-end space-x-2">
                <div className="flex space-x-2">
                  <button className="text-blue-500 hover:text-blue-700">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                  <button className="text-blue-500 hover:text-blue-700">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                  </button>
                  <button className="text-red-500 hover:text-red-700">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
                <button
                  onClick={() => toggleRow(index)}
                  className="text-gray-600 hover:text-gray-800 ml-2"
                >
                  {expandedRow === index ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </button>
              </div>
            </td>
          </tr>
          {expandedRow === index && (
            <tr className="bg-gray-50">
              <td colSpan={8} className="p-4 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-8">
                  <div>
                    <div className="mb-2">
                      <span className="font-medium">Sales Tax</span>
                      <span className="ml-2">15%</span>
                    </div>
                    <div>
                      <span className="font-medium">Income Tax</span>
                      <span className="ml-2">18%</span>
                    </div>
                  </div>
                  <div>
                    <div className="mb-2">
                      <span className="font-medium">$1,800.00</span>
                    </div>
                    <div>
                      <span className="font-medium">$5.00</span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          )}
        </React.Fragment>
      ))}
    </tbody>
  </table>
</div>
              <div className="flex justify-start space-x-2 mt-4">
                <button className="p-2 border border-gray-300 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                <button className="py-2 px-3 border border-gray-300 rounded-lg bg-orange-500 text-white">
                  1
                </button>
                <button className="p-2 border border-gray-300 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Customization Tab */}
          {activeTab === "Customization" && (
            <div>
                <div className="flex justify-between items-center md:flex-row flex-col  mb-4">

                <h2 className="text-2xl font-medium text-gray-800 mb-4">
                Customization
              </h2>
              <div className="flex justify-between md:flex-row flex-col md:space-y-0 space-y-2 space-x-2 mb-4">
                <div className="relative w-1/2">
                  <input
                    type="text"
                    placeholder="Search Customization"
                    className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg w-[200px] focus:outline-none focus:ring-2 focus:ring-orange-500"
                    value={searchCustomization}
                    onChange={(e) => setSearchCustomization(e.target.value)}
                  />
                  <div className="absolute right-3 top-2.5">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
                <button className="bg-orange-500 text-white px-4 py-2 rounded-lg flex items-center">
                  Add Customize
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 ml-1"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
                </div>
              
                <div className="bg-white rounded-lg border border-gray-200 overflow-x-auto">
  <table className="w-full">
    <thead>
      <tr className="bg-orange-50">
        <th className="p-4 text-left font-medium">No</th>
        <th className="p-4 text-left font-medium">Name</th>
        <th className="p-4 text-left font-medium">Status</th>
        <th className="p-4 text-right font-medium">Action</th>
      </tr>
    </thead>
    <tbody>
      {filteredCustomizations.map((item) => (
        <tr key={item.id} className="border-t border-gray-100 hover:bg-orange-50">
          <td className="p-4 align-middle">{item.id}</td>
          <td className="p-4 align-middle">{item.name}</td>
          <td className="p-4 align-middle">
            <label className="inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={item.status}
                onChange={() => toggleCustomizationStatus(item.id)}
              />
              <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"></div>
              <span className="ms-3 text-sm font-medium text-gray-700">
                {item.status ? "Active" : "Inactive"}
              </span>
            </label>
          </td>
          <td className="p-4 align-middle text-right">
            <div className="flex justify-end space-x-2">
              <button className="text-blue-500 hover:text-blue-700">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
              </button>
              <button className="text-red-500 hover:text-red-700">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </td>
        </tr>
      ))}
    </tbody>
  </table>
</div>
              <div className="flex justify-start space-x-2 mt-4">
                <button className="p-2 border border-gray-300 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                <button className="py-2 px-3 border border-gray-300 rounded-lg bg-orange-500 text-white">
                  1
                </button>
                <button className="p-2 border border-gray-300 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>

            </div>
          )}

          {/* Subscription Plan Tab */}
        
{activeTab === "Subscription Plan" && (
  <div>
    <h2 className="text-xl font-medium mb-6">Subscription</h2>

    {/* Subscription Info Card */}
    <div className="bg-white rounded-lg p-6 border border-gray-200 mb-8 shadow-sm">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="font-bold text-xl">
            Best Plan
            <span className="font-bold text-xl">${subscription.price}/</span>
            <span className="text-sm font-normal text-gray-600">Per Month</span>
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            Trial Period {subscription.trialDays} Days Remaining
          </p>
        </div>
        <button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center">
          Upgrade Subscription
        </button>
      </div>

      <div className="flex justify-between mb-4">
        <div>
          <p className="text-sm text-gray-500">Payment Method</p>
          <div className="flex items-center">
            <div className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center mr-2">
              <span className="text-xs">M</span>
            </div>
            <p className="font-medium">
              Mastercard ············{subscription.cardNumber}
            </p>
          </div>
        </div>
        <div>
          <p className="text-sm text-gray-500">Next Payment Due</p>
          <p className="font-medium">{subscription.nextPayment}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">&nbsp;</p>
          <div className="flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            <span className="text-sm text-orange-500">{subscription.status}</span>
          </div>
        </div>
      </div>
    </div>

    {/* Transaction History */}
    <div className="flex justify-between items-center mb-4">
      <h3 className="text-lg font-medium">Transaction History</h3>
      <div className="flex items-center">
        <div className="mr-2">
          <input
            type="text"
            placeholder="Start date → End date"
            className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg"
          />
        </div>
        <button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center">
          Download CSV
          <Download className="h-5 w-5 ml-1" />
        </button>
      </div>
    </div>
    
    <div className="bg-white rounded-lg overflow-hidden border border-gray-200">
      <table className="w-full">
        <thead>
          <tr className="bg-orange-50">
            <th className="text-left p-4 font-medium text-gray-700">No</th>
            <th className="text-left p-4 font-medium text-gray-700">Name</th>
            <th className="text-left p-4 font-medium text-gray-700">Email</th>
            <th className="text-left p-4 font-medium text-gray-700">Plan</th>
            <th className="text-left p-4 font-medium text-gray-700">Received Payments</th>
            <th className="text-left p-4 font-medium text-gray-700">Total Count</th>
            <th className="text-left p-4 font-medium text-gray-700">Start Date</th>
          </tr>
        </thead>
        <tbody>
          {[
            { id: '01', name: 'Aamir Shehzad', email: '<EMAIL>', plan: 'Basic Plan', payments: '$20.00', totalCount: '05', startDate: '25 Dec 2023' },
            { id: '02', name: 'Saad Khan', email: '<EMAIL>', plan: 'Popular Plan', payments: '$35.00', totalCount: '03', startDate: '01 March 2024' },
            { id: '03', name: 'Ihtizaz Ahmad', email: '<EMAIL>', plan: 'Best Plan', payments: '$60.00', totalCount: '06', startDate: '01 June 2024' }
          ].map((transaction) => (
            <tr key={transaction.id} className="border-t border-gray-100 hover:bg-orange-50">
              <td className="p-4">{transaction.id}</td>
              <td className="p-4">{transaction.name}</td>
              <td className="p-4">{transaction.email}</td>
              <td className="p-4">{transaction.plan}</td>
              <td className="p-4">{transaction.payments}</td>
              <td className="p-4">{transaction.totalCount}</td>
              <td className="p-4">{transaction.startDate}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>

    <div className="flex justify-center mt-6">
      <button className="mx-1 p-2 rounded-md border border-gray-300">
        <span className="sr-only">Previous page</span>
        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      </button>
      <button className="mx-1 px-3 py-2 rounded-md bg-orange-500 text-white">1</button>
      <button className="mx-1 p-2 rounded-md border border-gray-300">
        <span className="sr-only">Next page</span>
        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
        </svg>
      </button>
    </div>
  </div>
)}
        </div>
      </div>
    </div>
  );
};

export default Administration;
