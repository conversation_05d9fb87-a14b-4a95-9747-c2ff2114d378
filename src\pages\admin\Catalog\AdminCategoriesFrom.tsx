import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  usePostCategoryMutation,
  usePutCategoryMutation,
  useGetCategoriesQuery
} from '../../../store/api/CategoryApi';
import { useGetParentCategoriesQuery } from '../../../store/api/parentCategoryApi';
import { Image as ImageIcon, AlertCircle } from 'lucide-react';
import Swal from 'sweetalert2';
import { ClipLoader } from 'react-spinners';

interface CategoryFormData {
  categoryName: string;
  parentCategory: string;
  hasPicture: boolean;
  isActive: boolean;
  imageUrl: string;
  categoryImage?: File;
}

interface ParentCategoryOption {
  id: string;
  name: string;
}

interface CompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  outputFormat?: string;
}

// Image compression utility
const compressImage = (
  file: File,
  options: CompressionOptions = {}
): Promise<File> => {
  return new Promise((resolve, reject) => {
    const {
      maxWidth = 800,
      maxHeight = 800,
      quality = 0.8,
      outputFormat = "image/jpeg",
    } = options;

    // Check if file is an image
    if (!file.type.startsWith("image/")) {
      reject(new Error("File is not an image"));
      return;
    }

    // For SVG files, just return as-is since they're vector-based
    if (file.type === "image/svg+xml") {
      resolve(file);
      return;
    }

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      reject(new Error("Canvas context not available"));
      return;
    }

    const img = new Image();

    // Set a timeout to prevent hanging
    const timeoutId = setTimeout(() => {
      reject(new Error("Image loading timeout"));
    }, 10000);

    img.onload = function () {
      clearTimeout(timeoutId);

      try {
        let { width, height } = img;

        if (width > maxWidth || height > maxHeight) {
          if (width > height) {
            if (width > maxWidth) {
              height = (height * maxWidth) / width;
              width = maxWidth;
            }
          } else {
            if (height > maxHeight) {
              width = (width * maxHeight) / height;
              height = maxHeight;
            }
          }
        }

        canvas.width = Math.floor(width);
        canvas.height = Math.floor(height);

        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";

        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        let finalFormat = outputFormat;
        if (
          file.type === "image/png" &&
          !file.name.toLowerCase().includes("photo")
        ) {
          finalFormat = "image/png"; 
        }

        // Convert canvas to blob with error handling
        canvas.toBlob(
          (blob) => {
            if (blob && blob.size > 0) {
              // Create a new File object with the compressed data
              const compressedFile = new File([blob], file.name, {
                type: finalFormat,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              reject(
                new Error("Canvas to Blob conversion failed - empty blob")
              );
            }
          },
          finalFormat,
          quality
        );
      } catch (error) {
        reject(
          new Error(`Canvas processing failed: ${(error as Error).message}`)
        );
      }
    };

    img.onerror = function () {
      clearTimeout(timeoutId);
      reject(new Error("Failed to load image - image may be corrupted"));
    };

    // Create object URL with error handling
    try {
      const objectUrl = URL.createObjectURL(file);
      img.src = objectUrl;

      // Clean up object URL after image loads or fails
      const originalOnLoad = img.onload;
      img.onload = function () {
        URL.revokeObjectURL(objectUrl);
        if (originalOnLoad) {
          return originalOnLoad.apply(this, arguments as any);
        }
      };

      const originalOnError = img.onerror;
      img.onerror = function () {
        URL.revokeObjectURL(objectUrl);
        if (originalOnError) {
          return originalOnError.apply(this, arguments as any);
        }
      };
    } catch (error) {
      reject(new Error("Failed to create object URL"));
    }
  });
};

// Progressive compression with multiple attempts
const progressiveCompress = async (file: File): Promise<File> => {
  const strategies = [
    { maxWidth: 800, maxHeight: 800, quality: 0.8 },
    { maxWidth: 600, maxHeight: 600, quality: 0.7 },
    { maxWidth: 400, maxHeight: 400, quality: 0.6 },
  ];

  let lastError: Error | null = null;

  for (const strategy of strategies) {
    try {
      const compressed = await compressImage(file, strategy);
      return compressed;
    } catch (error) {
      lastError = error as Error;
      console.warn(`Compression strategy failed:`, strategy, error);
    }
  }

  throw lastError || new Error("All compression strategies failed");
};

// Utility to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const CreateCategoryForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;
  const navigate = useNavigate();
  const userId = localStorage.getItem('userId') ?? '';
  
  const { data: parentCategories = [], isLoading: loadingParents } = useGetParentCategoriesQuery(userId);
  
  // Fix: Make sure userId is always provided to useGetCategoriesQuery
  const { data: categories = [], isLoading: loadingCategories } = useGetCategoriesQuery(
    userId, // Always provide userId as first argument
    {
      skip: !isEditMode || !userId // Skip if not in edit mode or no userId
    }
  );
  
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const categoryData = isEditMode ? categories.find(category => category.id === id) : null;
  const loadingCategory = isEditMode && loadingCategories;
  
  const [postCategory, { isLoading: isCreating }] = usePostCategoryMutation();
  const [putCategory, { isLoading: isUpdating }] = usePutCategoryMutation();
  
  const isLoading = isCreating || isUpdating;

  const [formData, setFormData] = useState<CategoryFormData>({
    categoryName: '',
    parentCategory: '',
    hasPicture: false, 
    isActive: false,   
    imageUrl: '',
  });
  
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [compressedFile, setCompressedFile] = useState<File | null>(null);
  const [selectedParentName, setSelectedParentName] = useState<string>('Select Parent Category');
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isCompressing, setIsCompressing] = useState(false);
  const [compressionError, setCompressionError] = useState<string>("");

  useEffect(() => {
    if (isEditMode && categoryData) {
      setFormData({
        categoryName: categoryData.name || '',
        parentCategory: '',
        hasPicture: !!categoryData.pictureUrl,
        isActive: categoryData.status || false,
        imageUrl: categoryData.pictureUrl || '',
      });

      if (categoryData.pictureUrl) {
        setImagePreview(categoryData.pictureUrl);
      }

      if (categoryData.parentCategory) {
        setSelectedParentName(categoryData.parentCategory);
        
        const parent = parentCategories.find(
          (p: ParentCategoryOption) => p.name === categoryData.parentCategory
        );
        
        if (parent) {
          setFormData(prev => ({
            ...prev,
            parentCategory: parent.id
          }));
        }
      }
    }
  }, [isEditMode, categoryData, parentCategories]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    if (name === 'imageUrl' && value === '') {
      setImagePreview(null);
    }
  };

  const handleToggleChange = (name: keyof CategoryFormData) => {
    setFormData((prev) => ({ ...prev, [name]: !prev[name] }));
  };

  const handleSelectParentCategory = (category: ParentCategoryOption) => {
    setFormData({ ...formData, parentCategory: category.id });
    setSelectedParentName(category.name);
    setDropdownOpen(false);
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setCompressionError("");
    setIsCompressing(true);
    setSelectedFile(file);

    try {
      const originalSize = file.size;

      // Check if file is too large (e.g., > 50MB)
      const maxFileSize = 50 * 1024 * 1024; // 50MB
      if (originalSize > maxFileSize) {
        const errorMessage = `File size (${formatFileSize(
          originalSize
        )}) exceeds 50MB limit`;
        setCompressionError(errorMessage);
        setIsCompressing(false);

        Swal.fire({
          icon: "error",
          title: "File Too Large",
          text: errorMessage,
          confirmButtonColor: "#6366f1",
        });
        return;
      }

      // Validate file type
      const validTypes = [
        "image/jpeg",
        "image/jpg", 
        "image/png",
        "image/gif",
        "image/webp",
        "image/svg+xml",
      ];
      if (!validTypes.includes(file.type)) {
        const errorMessage =
          "Unsupported file type. Please use JPEG, PNG, GIF, WebP, or SVG.";
        setCompressionError(errorMessage);
        setIsCompressing(false);

        Swal.fire({
          icon: "error",
          title: "Invalid File Type",
          text: errorMessage,
          confirmButtonColor: "#6366f1",
        });
        return;
      }

      let processedFile: File;

      if (file.type === "image/svg+xml") {
        processedFile = file;
        Swal.fire({
          icon: "info",
          title: "SVG Uploaded",
          text: "SVG file uploaded successfully (no compression needed)",
          confirmButtonColor: "#6366f1",
        });
      } else {
        try {
          processedFile = await progressiveCompress(file);
        } catch (compressionError) {
          console.error("Progressive compression failed:", compressionError);

          if (originalSize <= 5 * 1024 * 1024) {
            processedFile = file;
          } else {
            const errorMessage = `File too large (${formatFileSize(
              originalSize
            )}) and compression failed. Please use a smaller image or try a different format.`;

            Swal.fire({
              icon: "error",
              title: "Compression Failed",
              text: errorMessage,
              confirmButtonColor: "#6366f1",
            });

            throw new Error(errorMessage);
          }
        }
      }

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(processedFile);

      // Store processed file and clear URL field
      setCompressedFile(processedFile);
      setFormData(prev => ({ ...prev, imageUrl: '', categoryImage: processedFile }));
      
    } catch (error) {
      console.error("Image processing failed:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to process image. Please try a different image or format.";
      setCompressionError(errorMessage);

      Swal.fire({
        icon: "error",
        title: "Image Processing Failed", 
        text: errorMessage,
        confirmButtonColor: "#6366f1",
      });

      setImagePreview(null);
      setCompressedFile(null);
    } finally {
      setIsCompressing(false);
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (!formData.categoryName.trim()) {
      errors.categoryName = 'Category name is required';
    }
    
    if (!formData.parentCategory) {
      errors.parentCategory = 'Parent category is required';
    }
    
    if (formData.hasPicture && !compressedFile && !selectedFile && !formData.imageUrl) {
      errors.image = 'Image is required when "Has Picture" is enabled';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setValidationErrors({});
    
    if (!validateForm()) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        html: `
          <div class="text-center">
            Please fix the following issues:
            <ul class="list-disc pl-5 mt-2 text-left">
              ${Object.values(validationErrors).map(error => `<li>${error}</li>`).join('')}
            </ul>
          </div>
        `,
        confirmButtonText: 'OK',
        confirmButtonColor: '#6366f1',
      });
      return;
    }

    const categoryFormData = new FormData();
    categoryFormData.append('name', formData.categoryName);
    categoryFormData.append('active', formData.isActive.toString());
    categoryFormData.append('hasPicture', formData.hasPicture.toString());
    
    // Use compressed file if available, otherwise use the original file or URL
    if (compressedFile) {
      categoryFormData.append('category_pic', compressedFile);
    } else if (selectedFile) {
      categoryFormData.append('category_pic', selectedFile);
    } else if (formData.imageUrl) {
      categoryFormData.append('category_pic', formData.imageUrl);
    }
    
    categoryFormData.append('parentId', formData.parentCategory);

    try {
      if (isEditMode && id) {
        await putCategory({ 
          id, 
          formData: categoryFormData 
        }).unwrap();
        
        await Swal.fire({
          title: 'Success!',
          text: 'Category has been updated successfully',
          icon: 'success',
          confirmButtonText: 'OK'
        });
      } else {
        await postCategory(categoryFormData).unwrap();
        
        await Swal.fire({
          title: 'Success!',
          text: 'Category has been created successfully',
          icon: 'success',
          confirmButtonText: 'OK'
        });
      }
      navigate('/admin/catalog/categories');
    } catch (err) {
      console.error(`Category ${isEditMode ? 'update' : 'creation'} failed`, err);
      
      Swal.fire({
        title: 'Error!',
        text: `Failed to ${isEditMode ? 'update' : 'create'} category. Please try again.`,
        icon: 'error',
        confirmButtonText: 'OK'
      });
    }
  };

  const handleCancel = () => {
    navigate('/admin/catalog/categories');
  };

  // Show loading if we don't have userId or if we're in edit mode and still loading
  if (!userId || (isEditMode && loadingCategory)) {
    return (
      <div className="rounded shadow w-full p-3 bg-gray-50">
        <div className="p-8 text-center">
          <ClipLoader size={30} color="#F97316" />
          <p className="text-gray-500 mt-2">
            {!userId ? 'Loading user data...' : 'Loading category data...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded shadow w-full p-3 bg-gray-50">
      <div className="p-4 flex items-center border border-gray-100 rounded-2xl bg-white mb-4">
        <button 
          className="flex items-center text-gray-800 font-medium"
          onClick={handleCancel}
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          <h1 className='text-xl'>{isEditMode ? 'Update Category' : 'Create Category'}</h1>
        </button>
      </div>

      <form onSubmit={handleSubmit} className='border border-gray-200 rounded-2xl bg-white'>
        {/* Category Details Section */}
        <div className="bg-opacity-30">
          <h2 className="text-base bg-orange-50 p-3 font-medium text-gray-800 mb-4 rounded-t-2xl">Category Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
            <div className=''>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category Name
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                name="categoryName"
                placeholder="Enter Category Name"
                value={formData.categoryName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Parent Category Name
                <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <button
                  type="button"
                  className="w-full px-3 py-2 border border-gray-300 rounded bg-white text-left flex justify-between items-center focus:outline-none focus:ring-1 focus:ring-orange-500"
                  onClick={() => setDropdownOpen(!dropdownOpen)}
                >
                  <span className='text-gray-500'>{selectedParentName}</span>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                
                {dropdownOpen && (
                  <div className="absolute w-full z-10 mt-1 bg-white shadow-lg rounded overflow-auto max-h-60">
                    {loadingParents ? (
                      <div className="p-2 text-gray-500">Loading...</div>
                    ) : (
                      parentCategories.map((category: ParentCategoryOption) => (
                        <div
                          key={category.id}
                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                          onClick={() => handleSelectParentCategory(category)}
                        >
                          {category.name}
                        </div>
                      ))
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Permissions Section */}
        <div className="border border-gray-100 mt-2 rounded-2xl">
          <h2 className="text-base bg-orange-50 p-3 font-medium text-gray-800 mb-4">Permissions</h2>
          
          <div className="space-y-3">
            <div className="flex items-center p-2">
              <label className="inline-flex relative items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={formData.hasPicture}
                  onChange={() => handleToggleChange('hasPicture')} 
                />
                <div className={`
                  w-11 h-6 bg-gray-200 rounded-full peer 
                  peer-checked:after:translate-x-full 
                  peer-checked:after:border-white 
                  after:content-[''] after:absolute after:top-0.5 after:left-[2px] 
                  after:bg-white after:border-gray-300 after:border after:rounded-full 
                  after:h-5 after:w-5 after:transition-all 
                  ${formData.hasPicture ? 'bg-orange-500' : ''}
                `}></div>
              </label>
              <span className="ml-3 text-sm font-medium text-gray-700">Has Picture</span>
            </div>
            
            <div className="flex items-center p-2">
              <label className="inline-flex relative items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={formData.isActive}
                  onChange={() => handleToggleChange('isActive')}
                />
                <div className={`w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all ${formData.isActive ? 'bg-orange-500' : ''}`}></div>
              </label>
              <span className="ml-3 text-sm font-medium text-gray-700">Active</span>
            </div>
          </div>
        </div>
        
        {/* Category Image Section */}
        <div className="mt-2 border border-gray-100">
          <h2 className="bg-orange-50 p-3 font-medium text-gray-800 mb-4">Category Image</h2>
          
          <div className="flex items-start space-x-4 p-3">
            <div className="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center overflow-hidden">
              {isCompressing ? (
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
              ) : imagePreview ? (
                <img src={imagePreview} alt="Preview" className="w-full h-full object-cover" />
              ) : formData.imageUrl ? (
                <img src={formData.imageUrl} alt="Preview" className="w-full h-full object-cover" />
              ) : (
              <ImageIcon size={24} className="text-orange-500" />
              )}
            </div>
            <div className="flex-1">
              <div className="flex items-center">
                <label className="cursor-pointer">
                  <span className="text-sm font-medium text-blue-500">Upload</span>
                  <input 
                    type="file"
                    className="hidden"
                    accept="image/*"
                    disabled={isCompressing}
                    onChange={handleFileUpload}
                  />
                </label>
                <span className="ml-2 text-sm text-gray-500">Category Image</span>
              </div>
              <p className="text-xs text-gray-500">
                SVG, PNG, JPG, GIF, WebP (max. 50MB, auto-compressed to 800x800px)
              </p>

              {/* Compression Status */}
              {isCompressing && (
                <div className="mt-2 flex items-center text-sm text-blue-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  Compressing image...
                </div>
              )}

              {/* Compression Error */}
              {compressionError && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs flex items-start">
                  <AlertCircle
                    size={16}
                    className="text-red-500 mr-1 mt-0.5 flex-shrink-0"
                  />
                  <p className="text-red-700">{compressionError}</p>
                </div>
              )}

              {selectedFile && !compressionError && (
                <p className="mt-1 text-xs text-gray-700">Selected: {selectedFile.name}</p>
              )}
            </div>
          </div>
          
          <div className="mt-4 p-3">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Or Enter Image URL
            </label>
            <input
              type="url"
              name="imageUrl"
              placeholder="Add Image URL"
              value={formData.imageUrl}
              onChange={handleInputChange}
              disabled={!!selectedFile || isCompressing}
              className={`w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500 ${
                selectedFile || isCompressing ? 'bg-gray-100' : ''
              }`}
            />
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="p-4 border-t border-gray-100 flex justify-end space-x-2">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 border border-gray-300 rounded text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none"
            disabled={isLoading || isCompressing}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading || isCompressing}
            className={`px-6 py-2 rounded text-sm font-medium text-white focus:outline-none flex items-center justify-center ${
              isLoading || isCompressing ? 'bg-orange-300' : 'bg-orange-500 hover:bg-orange-600'
            }`}
          >
            {isCompressing ? (
              <>
                <ClipLoader size={18} color="#ffffff" className="mr-2" />
                Compressing...
              </>
            ) : isLoading ? (
              <>
                <ClipLoader size={18} color="#ffffff" className="mr-2" />
                {isEditMode ? 'Update Category...' : 'Add Category...'}
              </>
            ) : isEditMode ? 'Update Category' : 'Add Category'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateCategoryForm;