import { useState, useEffect } from "react";
import { Search, Edit, Trash2, ChevronLeft, ChevronRight, Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { 
  useGetCouponsByUserQuery, 
  useDeleteCouponMutation 
} from "../../../store/api/couponsApi";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

interface Coupon {
  id?: string;
  _id?: string;
  series: string;
  description: string;
  discount: string | number;
  start: number;
  end: number;
  startDate: string;
  endDate: string;
  discountType: string;
  publish: boolean;
  userId?: string;
  message?: string;
}

export default function Coupons() {
  // Get userId from localStorage with auth state check
  const [userId, setUserId] = useState("");
  const [isReady, setIsReady] = useState(false);
  
  // Setup auth check on mount
  useEffect(() => {
    const storedUserId = localStorage.getItem("userId");
    if (storedUserId) {
      setUserId(storedUserId);
      setIsReady(true);
    } else {
      // Handle case where user is not authenticated
      console.warn("User ID not found in localStorage");
      setIsReady(true); // Still set ready to prevent endless loading
    }
  }, []);
  
  // Use RTK Query to fetch coupons with proper skip for auth
  const { 
    data: couponsData = [], 
    isLoading, 
    isError, 
    error,
    refetch 
  } = useGetCouponsByUserQuery(userId, {
    skip: !isReady || !userId,
    refetchOnMountOrArgChange: true
  });

  // Use RTK Query for delete mutation
  const [deleteCoupon, { isLoading: isDeleting }] = useDeleteCouponMutation();

  // Local state for search and pagination
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const navigate = useNavigate();

  // State for delete confirmation modal
  const [showModal, setShowModal] = useState(false);
  const [couponToDelete, setCouponToDelete] = useState<string | null>(null);

  // Format error message for better display
  const getErrorMessage = (error: any) => {
    if (!error) return "Unknown error occurred";
    
    if (typeof error === 'string') return error;
    
    if (error.data && error.data.message) return error.data.message;
    
    if (error.message) return error.message;
    
    return "Failed to load coupons. Please try again.";
  };

  // Handle search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  // Handle coupon deletion request
  const handleDelete = (id: string) => {
    if (!id) {
      toast.error("Invalid coupon ID");
      return;
    }
    setCouponToDelete(id);
    setShowModal(true);
  };

  // Confirm and process deletion
  const confirmDeleteCoupon = () => {
    if (couponToDelete) {
      deleteCoupon(couponToDelete)
        .unwrap()
        .then(() => {
          toast.success("Coupon deleted successfully!");
          setShowModal(false);
          refetch(); // Refresh the coupons list
        })
        .catch((err) => {
          const errorMsg = err.data?.message || err.message || "Unknown error";
          toast.error("Failed to delete coupon: " + errorMsg);
          setShowModal(false);
        });
    }
  };

  // Navigate to add coupon form
  const handleAddCoupon = () => {
    navigate('/admin/marketing/coupons/coupons-form');
  };

  // Navigate to edit coupon form
  const handleEdit = (id: string) => {
    if (!id) {
      toast.error("Invalid coupon ID");
      return;
    }
    navigate(`/admin/marketing/coupons/coupons-form/${id}`);
  };

  // Filter coupons based on search term, with null/undefined checks
  const filteredCoupons = Array.isArray(couponsData) ? couponsData.filter(
    (coupon: Coupon) =>
      (coupon?.series?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (coupon?.description?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  ) : [];

  // Format date to display as "Month Day, Year"
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', { 
        month: 'long', 
        day: 'numeric', 
        year: 'numeric' 
      });
    } catch {
      return dateString || '-';
    }
  };

  // Calculate pagination
  const itemsPerPage = 4;
  const totalPages = Math.ceil((filteredCoupons.length || 0) / itemsPerPage);
  const paginatedCoupons = filteredCoupons.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Loading state
  if (!isReady || isLoading) {
    return (
      <div className="p-4 bg-gray-50 flex justify-center items-center h-64">
        <div className="text-xl text-gray-600">Loading coupons...</div>
      </div>
    );
  }

  // Error state with improved error handling
  if (isError) {
    return (
      <div className="p-4 bg-gray-50 flex justify-center items-center h-64">
        <div className="text-xl text-red-600">
          Error loading coupons: {getErrorMessage(error)}
          <button 
            onClick={() => refetch()} 
            className="ml-4 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // No user ID state
  if (!userId) {
    return (
      <div className="p-4 bg-gray-50 flex justify-center items-center h-64">
        <div className="text-xl text-gray-600">
          Please log in to view your coupons
          <button 
            onClick={() => navigate('/login')} 
            className="ml-4 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600"
          >
            Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 bg-gray-50">
      {/* Confirmation Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-50">
          <div className="bg-white p-8 rounded-xl shadow-xl w-full max-w-md text-center relative">
            <div className="flex justify-center mb-4">
              <div className="bg-orange-100 text-orange-500 p-4 rounded-full">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01M12 5c.512 0 1.023.195 1.414.586C13.805 6.977 14 7.488 14 8s-.195 1.023-.586 1.414A1.993 1.993 0 0112 10a1.993 1.993 0 01-1.414-.586A1.993 1.993 0 0110 8c0-.512.195-1.023.586-1.414A1.993 1.993 0 0112 5z"
                  />
                </svg>
              </div>
            </div>
            <h3 className="text-2xl font-semibold text-gray-800">
              Are you sure?
            </h3>
            <p className="mt-2 text-gray-600">
              Do you want to delete this coupon?
            </p>
            <div className="mt-6 flex justify-center gap-4">
              <button
                onClick={confirmDeleteCoupon}
                className={`bg-orange-600 hover:bg-orange-700 text-white px-5 py-2 rounded-md font-medium ${
                  isDeleting ? "opacity-50 cursor-not-allowed" : ""
                }`}
                disabled={isDeleting}
              >
                Yes, delete it!
              </button>
              <button
                onClick={() => setShowModal(false)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-5 py-2 rounded-md font-medium"
              >
                No, cancel!
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center md:flex-row flex-col p-2 mb-2 rounded-2xl border border-gray-200 bg-white">
        <h1 className="text-3xl p-3 font-bold text-gray-800">Coupons</h1>
        <div className="flex md:flex-row flex-col-reverse gap-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Coupons"
              className="pl-3 pr-10 py-2 border border-gray-200 rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchTerm}
              onChange={handleSearch}
            />
            <button className="absolute right-3 top-2.5 text-gray-400">
              <Search size={20} />
            </button>
          </div>
          <button
            onClick={handleAddCoupon}
            className="bg-orange-500 hover:bg-orange-600 text-white font-medium px-4 py-2 rounded-lg flex items-center gap-1"
          >
            Add Coupons
            <Plus size={20} />
          </button>
        </div>
      </div>

      <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
        {paginatedCoupons.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            No coupons found. {searchTerm ? "Try a different search term." : "Add your first coupon!"}
          </div>
        ) : (
          <div className="overflow-x-auto rounded-t-2xl">
            <table className="w-full">
              <thead>
                <tr className="border-gray-100 bg-orange-50">
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Coupon Code
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Discount
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Start No
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    End No
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Start Date
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    End Date
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100 bg-white-50">
                {paginatedCoupons.map((coupon: Coupon) => (
                  <tr key={coupon._id || coupon.id} className="hover:bg-orange-50">
                    <td className="px-6 py-4 text-sm text-gray-800">
                      {coupon?.series || '-'}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-800">
                      {coupon?.description || '-'}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-800">
                      {coupon?.discount || 0}{coupon?.discountType || ''}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-800">
                      {coupon?.start || 0}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-800">
                      {coupon?.end || 0}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-800">
                      {formatDate(coupon?.startDate)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-800">
                      {formatDate(coupon?.endDate)}
                    </td>
                    <td className="px-6 py-4 text-sm">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        coupon?.publish === true
                          ? "bg-green-100 text-green-600" 
                          : "bg-orange-100 text-orange-600"
                      }`}>
                        {coupon?.publish === true ? "Active" : "In_active"}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm flex gap-2">
                      <button
                        onClick={() => handleEdit(coupon?._id || coupon?.id || '')}
                        className="text-blue-500 hover:text-blue-700"
                      >
                        <Edit size={18} />
                      </button>
                      <button
                        onClick={() => handleDelete(coupon?._id || coupon?.id || '')}
                        className={`text-red-500 hover:text-red-700 ${
                          isDeleting ? "opacity-50 cursor-not-allowed" : ""
                        }`}
                        disabled={isDeleting}
                      >
                        <Trash2 size={18} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination controls */}
      {totalPages > 0 && (
        <div className="flex justify-start items-start space-x-2 p-4">
          <button
            className={`p-1 rounded-md border border-gray-200 ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-orange-200'}`}
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft size={18} className="text-gray-600" />
          </button>

          {/* Generate page numbers */}
          {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
            const pageNum = i + 1;
            return (
              <button
                key={pageNum}
                className={`w-8 h-8 rounded-md flex items-center justify-center ${
                  pageNum === currentPage
                    ? "bg-orange-500 text-white"
                    : "border border-gray-200 text-gray-600 hover:bg-orange-100"
                }`}
                onClick={() => setCurrentPage(pageNum)}
              >
                {pageNum}
              </button>
            );
          })}

          {/* If there are more pages than shown */}
          {totalPages > 5 && (
            <>
              <span className="px-2">...</span>
              <button
                className={`w-8 h-8 rounded-md border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-orange-100`}
                onClick={() => setCurrentPage(totalPages)}
              >
                {totalPages}
              </button>
            </>
          )}

          <button
            className={`p-1 rounded-md border border-gray-200 ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-orange-200'}`}
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            <ChevronRight size={18} className="text-gray-600" />
          </button>
        </div>
      )}
    </div>
  );
}