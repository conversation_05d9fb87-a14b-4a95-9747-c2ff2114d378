import React from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";

const AllOrdersLayout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

const getTabStyles = (isActive: boolean): string => {
  const baseStyles = "flex items-center justify-center text-center leading-none px-4 py-1 text-sm font-medium pt-[0.4rem]";
  return isActive
    ? `${baseStyles} bg-orange-50 text-orange-500 border border-orange-500 rounded-full`
    : `${baseStyles} text-gray-700 hover:text-orange-500 cursor-pointer`;
};


  return (
    <div className="w-full bg-white p-2 pt-4 rounded-b-xl shadow-md">
    
      <div className="overflow-x-auto pb-2 mb-4 custom-scrollbar">
        <div className="flex justify-center items-center space-x-4 w-max">
          <button
              className={`${getTabStyles(location.pathname.includes("pos-order") || location.pathname.endsWith("all-orders"))}`}
            onClick={() => navigate("pos-order")}
          >
            POS Orders
          </button>
          <button
            className={getTabStyles(location.pathname.includes("online-order"))}
            onClick={() => navigate("online-order")}
            
          >
            Online Orders
          </button>
          <button
            className={getTabStyles(location.pathname.includes("drive-thru"))}
            onClick={() => navigate("drive-thru")}
             disabled
          >
            Drive Thru Orders
          </button>
          <button
            className={getTabStyles(location.pathname.includes("kiosk"))}
            onClick={() => navigate("kiosk")}
             disabled
          >
            Kiosk Orders
          </button>
        </div>
      </div>

      {/* Content area - this will render the nested route components */}
      <Outlet />
    </div>
  );
};

export default AllOrdersLayout;