import { useState, useEffect } from 'react';
import { Search, Plus, Edit, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  useGetEmployeesListQuery,
  useDeleteEmployeeListMutation,
  type Employee,
} from "../../../store/api/EmployeeListApi";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import Swal from "sweetalert2";


export default function SalaryStructure() {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(6);
  
  const navigate = useNavigate();
  const userId = localStorage.getItem('userId') || '';

  // Fetch employees data using the API
  const { data: employees = [], isLoading, error, refetch } = useGetEmployeesListQuery(userId);
  const [deleteEmployee, { isLoading: isDeleting }] = useDeleteEmployeeListMutation();

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleDeleteEmployee = async (id: string) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this employee's salary structure?",
      icon: "warning",
      cancelButtonText: "No, cancel!",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#6b7280",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        await deleteEmployee(id).unwrap();
        await Swal.fire(
          "Deleted!",
          "Employee salary structure has been deleted successfully.",
          "success"
        );
        refetch();
      } catch (error) {
        console.error('Failed to delete employee salary structure:', error);
        Swal.fire("Error!", "Failed to delete employee salary structure.", "error");
      }
    }
  };

  const handleAddEmployee = () => {
    navigate('/admin/employees/salary-structure/salary-structure-form');
  };

  const handleEdit = (id: string) => {
    navigate(`/admin/employees/salary-structure/salary-structure-form/${id}`);
  };

  // Filter employees based on search query
  const filteredEmployees = employees.filter((employee) =>
    employee.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    employee.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    employee.employeeId.includes(searchQuery)
  );
  
  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentEmployees = filteredEmployees.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredEmployees.length / itemsPerPage);

  useEffect(() => {
    if (error) {
      Swal.fire("Error!", "Failed to fetch employees.", "error");
      console.error('Error fetching employees:', error);
    }
  }, [error]);

  // Format name by combining first and last name
  const formatName = (firstName: string, lastName: string) => {
    return `${firstName} ${lastName}`;
  };

  return (
    <div className="p-4 bg-gray-50">
      <div className="flex justify-between items-center md:flex-row flex-col rounded-2xl border border-gray-200 bg-white p-4 mb-6">
        <h1 className="text-2xl font-bold text-gray-800 md:mb-0 mb-2">Salary Structure</h1>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Employee"
              className="pl-3 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchQuery}
              onChange={handleSearchChange}
            />
            <Search className="absolute right-3 top-2.5 text-gray-400" size={18} />
          </div>
          <button
            onClick={handleAddEmployee}
            className="flex items-center gap-1 cursor-pointer bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Add Employee's Salary
            <Plus size={18} />
          </button>
        </div>
      </div>

      {isLoading ? (
      <div className="flex justify-center items-start bg-white h-screen pt-[32vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading Employee list...</p>
        </div>
      </div>
      ) : error ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-red-500">Failed to load employees. Please try again later.</p>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto bg-white rounded-2xl border border-gray-200">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 bg-orange-50 bg-opacity-50">
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">ID</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Employee Name</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Rate/Hourly</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Shift Hours</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Overtime Rate/Hourly</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Bonuses</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Allowances</th>
                  <th className="text-right py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentEmployees.length > 0 ? (
                  currentEmployees.map((employee) => (
                    <tr key={employee.id} className="border-b border-gray-200 hover:bg-orange-50">
                      <td className="py-4 px-4 text-gray-600">{employee.employeeId}</td>
                      <td className="py-4 px-4 text-gray-600">{formatName(employee.firstName, employee.lastName)}</td>
                      <td className="py-4 px-4 text-gray-600">${employee.hourlyRate.toFixed(2)}</td>
                      <td className="py-4 px-4 text-gray-600">{employee.totalHours || 8}</td>
                      <td className="py-4 px-4 text-gray-600">${employee.overTimeRate.toFixed(2)}</td>
                      <td className="py-4 px-4 text-gray-600">${getBonusesTotal(employee).toFixed(2)}</td>
                      <td className="py-4 px-4 text-gray-600">${getAllowancesTotal(employee).toFixed(2)}</td>
                      <td className="py-4 px-4 flex justify-end gap-2">
                        <button 
                          className="text-blue-500 hover:text-blue-700"
                          onClick={() => handleEdit(employee.id)}
                          disabled={isDeleting}
                        >
                      <Edit
                          id="edit-icon"
                          data-tooltip-id="edit-tooltip"
                          data-tooltip-content="Edit"
                          size={20}
                          className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                        />
                        <Tooltip
                          id="edit-tooltip"
                          place="bottom"
                          className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                        />

                        </button>
                        <button 
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleDeleteEmployee(employee.id)}
                          disabled={isDeleting}
                        >
                          <Trash2
                          id="delete-icon"
                          data-tooltip-id="delete-tooltip"
                          data-tooltip-content="Delete"
                          size={20}
                          className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                        />
                        <Tooltip
                          id="delete-tooltip"
                          place="bottom"
                          className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                        />

                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={8} className="py-4 px-4 text-center text-gray-500">
                      No employees found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {filteredEmployees.length > 0 && (
            <div className="flex justify-start items-center gap-2 mt-6">
              <button 
                className={`p-2 rounded-md border border-gray-200 ${
                  currentPage === 1 ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "hover:bg-gray-100"
                }`}
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft size={18} />
              </button>
              
              {Array.from({ length: Math.min(totalPages, 4) }, (_, i) => {
                // Show pagination numbers based on current page position
                let pageNum = i + 1;
                if (totalPages > 4 && currentPage > 2) {
                  pageNum = Math.min(currentPage - 1 + i, totalPages);
                  if (i === 0 && currentPage > 2) pageNum = 1;
                  if (i === 3 && pageNum < totalPages) pageNum = totalPages;
                }
                
                return (
                  <button 
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={`w-8 h-8 flex items-center justify-center rounded-md ${
                      currentPage === pageNum 
                        ? 'bg-orange-500 text-white' 
                        : 'border border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
              
              <button 
                className={`p-2 rounded-md border border-gray-200 ${
                  currentPage === totalPages ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "hover:bg-gray-100"
                }`}
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                <ChevronRight size={18} />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}


const getBonusesTotal = (employee: Employee) => {
  if (!employee.deductions || !Array.isArray(employee.deductions)) {
    return 0;
  }
  
  return employee.deductions
    .filter(deduction => deduction.type === 'bonus')
    .reduce((total, item) => total + (item.amount || 0), 0);
};

const getAllowancesTotal = (employee: Employee) => {
  if (!employee.deductions || !Array.isArray(employee.deductions)) {
    return 0;
  }
  
  return employee.deductions
    .filter(deduction => deduction.type === 'allowance')
    .reduce((total, item) => total + (item.amount || 0), 0);
};