import { useState, useEffect, useRef, useMemo } from 'react';
import { X, Check, CreditCard, Printer, Mail, ChevronDown, LockIcon, Banknote } from 'lucide-react';
import { loadStripe } from '@stripe/stripe-js';
import {
    Elements,
    CardNumberElement,
    CardExpiryElement,
    CardCvcElement,
    useStripe,
    useElements
} from '@stripe/react-stripe-js';
import TipAmountModal from './TipAmountModal';
import { useCreateOrderItemMutation, useGetPaymentsQuery } from '../../../store/api/pos/orderapi';
import { useDispatch, useSelector } from 'react-redux';
import { generateNewInvoiceNumber, selectTipAmount, setTipAmount, clearCart, selectCartTotal, selectLoyaltyAmount, selectFinalTotal, selectSelectedCoupon, selectCouponOfferAmount } from '../../../store/slices/cartSlice';
import type { RootState } from '../../../store/store';
import { selectSelectedCustomer } from '../../../store/slices/selectedcustomer';
import { selectTaxes } from '../../../store/slices/taxSlice';
import { useGetProductsQuery } from '../../../store/api/menuitemApi';
import { selectSelectedTables } from '../../../store/slices/selectedTablesSlice';
import { useGetTaxsQuery } from '../../../store/api/taxApi';
import Swal from 'sweetalert2';
import { printReceipt } from '../../../utils/printUtils';

// Define the interface locally to avoid import issues
interface PrintReceiptData {
    orderNo: string;
    table?: string;
    customer: string;
    date: string;
    items: Array<{
        name: string;
        quantity: number;
        price: number;
        total: number;
    }>;
    subtotal: number;
    tax: number;
    surcharge: number;
    total: number;
    payment: string;
    received: number;
    due: number;
    restaurantName?: string;
    restaurantAddress?: string;
    restaurantPhone?: string;
}

import { selectModifiers } from '../../../store/slices/modifiersSlice';

// List of all countries in the world
const countries = [
    "Afghanistan", "Albania", "Algeria", "Andorra", "Angola", "Antigua and Barbuda", "Argentina", "Armenia", "Australia", "Austria",
    "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium", "Belize", "Benin", "Bhutan",
    "Bolivia", "Bosnia and Herzegovina", "Botswana", "Brazil", "Brunei", "Bulgaria", "Burkina Faso", "Burundi", "Cabo Verde", "Cambodia",
    "Cameroon", "Canada", "Central African Republic", "Chad", "Chile", "China", "Colombia", "Comoros", "Congo", "Costa Rica",
    "Croatia", "Cuba", "Cyprus", "Czech Republic", "Denmark", "Djibouti", "Dominica", "Dominican Republic", "East Timor", "Ecuador",
    "Egypt", "El Salvador", "Equatorial Guinea", "Eritrea", "Estonia", "Eswatini", "Ethiopia", "Fiji", "Finland", "France",
    "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Greece", "Grenada", "Guatemala", "Guinea", "Guinea-Bissau",
    "Guyana", "Haiti", "Honduras", "Hungary", "Iceland", "India", "Indonesia", "Iran", "Iraq", "Ireland",
    "Israel", "Italy", "Jamaica", "Japan", "Jordan", "Kazakhstan", "Kenya", "Kiribati", "Korea, North", "Korea, South",
    "Kosovo", "Kuwait", "Kyrgyzstan", "Laos", "Latvia", "Lebanon", "Lesotho", "Liberia", "Libya", "Liechtenstein",
    "Lithuania", "Luxembourg", "Madagascar", "Malawi", "Malaysia", "Maldives", "Mali", "Malta", "Marshall Islands", "Mauritania",
    "Mauritius", "Mexico", "Micronesia", "Moldova", "Monaco", "Mongolia", "Montenegro", "Morocco", "Mozambique", "Myanmar",
    "Namibia", "Nauru", "Nepal", "Netherlands", "New Zealand", "Nicaragua", "Niger", "Nigeria", "North Macedonia", "Norway",
    "Oman", "Pakistan", "Palau", "Palestine", "Panama", "Papua New Guinea", "Paraguay", "Peru", "Philippines", "Poland",
    "Portugal", "Qatar", "Romania", "Russia", "Rwanda", "Saint Kitts and Nevis", "Saint Lucia", "Saint Vincent and the Grenadines", "Samoa", "San Marino",
    "Sao Tome and Principe", "Saudi Arabia", "Senegal", "Serbia", "Seychelles", "Sierra Leone", "Singapore", "Slovakia", "Slovenia", "Solomon Islands",
    "Somalia", "South Africa", "South Sudan", "Spain", "Sri Lanka", "Sudan", "Suriname", "Sweden", "Switzerland", "Syria",
    "Taiwan", "Tajikistan", "Tanzania", "Thailand", "Togo", "Tonga", "Trinidad and Tobago", "Tunisia", "Turkey", "Turkmenistan",
    "Tuvalu", "Uganda", "Ukraine", "United Arab Emirates", "United Kingdom", "United States", "Uruguay", "Uzbekistan", "Vanuatu", "Vatican City",
    "Venezuela", "Vietnam", "Yemen", "Zambia", "Zimbabwe"
];

// Initialize Stripe with your publishable key
// Replace with your actual publishable key
const stripePromise = loadStripe('pk_test_51OxXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX');

interface PaymentModalProps {
    isOpen: boolean;
    onClose: () => void;
    totalAmount?: number;
}

// Stripe Card Form Component with separate fields
const StripeCardForm = ({ onPaymentSuccess }: { onPaymentSuccess: () => void }) => {
    const stripe = useStripe();
    const elements = useElements();
    const [error, setError] = useState<string | null>(null);
    const [processing, setProcessing] = useState(false);
    const [succeeded, setSucceeded] = useState(false);
    const [cardComplete, setCardComplete] = useState({
        cardNumber: false,
        cardExpiry: false,
        cardCvc: false
    });

    const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();

        if (!stripe || !elements) {
            // Stripe.js has not loaded yet. Make sure to disable form submission until Stripe.js has loaded.
            return;
        }

        if (!cardComplete.cardNumber || !cardComplete.cardExpiry || !cardComplete.cardCvc) {
            setError('Please fill in all card details');
            return;
        }

        setProcessing(true);

        // Get references to the mounted Elements
        const cardNumber = elements.getElement(CardNumberElement);

        if (!cardNumber) {
            setProcessing(false);
            setError('Card element not found');
            return;
        }

        // Use your card Element with other Stripe.js APIs
        const { error, paymentMethod } = await stripe.createPaymentMethod({
            type: 'card',
            card: cardNumber,
        });

        if (error) {
            setError(error.message || 'An error occurred');
            setProcessing(false);
        } else {
            setError(null);
            setSucceeded(true);
            setProcessing(false);
            console.log('Payment method created:', paymentMethod);
            onPaymentSuccess();
        }
    };

    const elementStyle = {
        base: {
            fontSize: '16px',
            color: '#424770',
            '::placeholder': {
                color: '#aab7c4',
            },
        },
        invalid: {
            color: '#9e2146',
            iconColor: '#9e2146',
        },
    };

    const handleChange = (element: string) => (event: any) => {
        setCardComplete({
            ...cardComplete,
            [element]: event.complete
        });

        if (event.error) {
            setError(event.error.message);
        } else {
            setError(null);
        }
    };

    return (
        <form onSubmit={handleSubmit} className="mt-4 space-y-4">
            {/* Secure checkout with Link */}
            <div className="flex items-center text-blue-500 mb-2">
                <LockIcon size={16} className="mr-2" />
                <span className="text-sm font-medium">Secure checkout with Stripe</span>
                <ChevronDown size={16} className="ml-1" />
            </div>

            {/* Card details */}
            <div className="space-y-4">
                <div>
                    <label className="block text-gray-700 text-sm mb-1">Card number</label>
                    <div className="p-3 border border-gray-200 rounded-lg">
                        <CardNumberElement
                            options={{ style: elementStyle }}
                            onChange={handleChange('cardNumber')}
                        />
                    </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <label className="block text-gray-700 text-sm mb-1">Expiry date</label>
                        <div className="p-3 border border-gray-200 rounded-lg">
                            <CardExpiryElement
                                options={{ style: elementStyle }}
                                onChange={handleChange('cardExpiry')}
                            />
                        </div>
                    </div>
                    <div>
                        <label className="block text-gray-700 text-sm mb-1">CVC</label>
                        <div className="p-3 border border-gray-200 rounded-lg">
                            <CardCvcElement
                                options={{ style: elementStyle }}
                                onChange={handleChange('cardCvc')}
                            />
                        </div>
                    </div>
                </div>

                <div>
                    <label className="block text-gray-700 text-sm mb-1">Country</label>
                    <div className="relative">
                        <select
                            className="w-full p-3 border border-gray-200 rounded-lg appearance-none"
                            defaultValue="Pakistan"
                        >
                            {countries.map(country => (
                                <option key={country} value={country}>{country}</option>
                            ))}
                        </select>
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <ChevronDown size={20} className="text-gray-500" />
                        </div>
                    </div>
                </div>

                {error && <div className="text-red-500 text-sm">{error}</div>}

                <button
                    type="submit"
                    disabled={!stripe || processing || !cardComplete.cardNumber || !cardComplete.cardExpiry || !cardComplete.cardCvc}
                    className={`w-full py-3 rounded-lg font-medium ${processing || !stripe || !cardComplete.cardNumber || !cardComplete.cardExpiry || !cardComplete.cardCvc
                        ? 'bg-indigo-300 text-white cursor-not-allowed'
                        : 'bg-indigo-600 text-white cursor-pointer'
                        }`}
                >
                    {processing ? 'Processing...' : succeeded ? 'Payment Successful' : 'Pay now'}
                </button>
            </div>
        </form>
    );
};

export default function PaymentModal({ isOpen, onClose, totalAmount = 0 }: PaymentModalProps) {
    const [inputValue, setInputValue] = useState('');
    const dispatch = useDispatch();
    const userId = localStorage.getItem("userId") || ""
    const tipamount = useSelector(selectTipAmount)
    const cart = useSelector((state: RootState) => state.cart)
    const selectedcustomer = useSelector(selectSelectedCustomer)
    const selectedTables = useSelector(selectSelectedTables)
    const cartTotal = useSelector(selectCartTotal)
    const loyaltyAmount = useSelector(selectLoyaltyAmount)
    const finalTotal = useSelector(selectFinalTotal)
    const modifiers = useSelector(selectModifiers)
    const selectedCoupon = useSelector(selectSelectedCoupon)
    const couponOfferAmount = useSelector(selectCouponOfferAmount)
    const { data: prouducts } = useGetProductsQuery(userId)

    // Fetch taxes from API - this will populate the Redux state
    const { data: taxesFromApi, isLoading: taxesLoading, error: taxesError } = useGetTaxsQuery(userId)
    const texes = useSelector(selectTaxes)

    const [createOrderItem, { isLoading: createOrderLoading, error: _createOrderError }] = useCreateOrderItemMutation();

    const { data: paymentmethod } = useGetPaymentsQuery(userId)
    console.log(paymentmethod)

    // Debug taxes
    console.log("Taxes from API:", taxesFromApi)
    console.log("Taxes from Redux state:", texes)
    console.log("Taxes loading:", taxesLoading)
    console.log("Taxes error:", taxesError)

    // Filter payment methods: exclude GiftCard and only show active ones
    const activePaymentMethods = useMemo(() => {
        return paymentmethod?.filter(
            (method: any) => method.isActive && method.name !== "GiftCard"
        ) || [];
    }, [paymentmethod]);

    const [selectedPayment, setSelectedPayment] = useState('cash'); // Start with 'cash' as default
    const [selectedStripeOption, setSelectedStripeOption] = useState('card'); // 'card', 'amazon', 'cashapp'
    const [show, setShow] = useState(false);
    const [paymentSucceeded, setPaymentSucceeded] = useState(false);

    const [isTipModalOpen, setIsTipModalOpen] = useState(false);

    // Track if we've already set the initial payment method
    const hasSetInitialPayment = useRef(false);

    // Update selected payment when payment methods data changes (only on initial load)
    useEffect(() => {
        if (activePaymentMethods.length > 0 && !hasSetInitialPayment.current) {
            const newDefaultPayment = activePaymentMethods.find((method: any) => method.defaultPayment)?.name.toLowerCase() ||
                activePaymentMethods[0].name.toLowerCase();
            console.log('Setting initial default payment to:', newDefaultPayment);
            setSelectedPayment(newDefaultPayment);
            hasSetInitialPayment.current = true;
        }
    }, [activePaymentMethods]);

    // Debug: Track selectedPayment changes
    useEffect(() => {
        console.log('selectedPayment changed to:', selectedPayment);
    }, [selectedPayment]);

    useEffect(() => {
        if (isOpen) {
            setShow(true);
            // Reset tip modal state when payment modal is opened
            setIsTipModalOpen(false);
        } else {
            // Delay unmount for exit animation
            const timeout = setTimeout(() => setShow(false), 300);
            return () => clearTimeout(timeout);
        }
    }, [isOpen]);

    // Handle number input
    const handleNumberInput = (value: string) => {
        setInputValue(prev => {
            if (value === 'C') return '';
            if (value === '.') {
                if (prev.includes('.')) return prev;
                return prev + '.';
            }
            return prev + value;
        });
    };

    // Handle backspace
    const handleBackspace = () => {
        setInputValue(prev => prev.slice(0, -1));
    };

    const handlePaymentSuccess = () => {
        setPaymentSucceeded(true);
        // You might want to show a success message or redirect
    };

    // Handle adding tip
    const handleAddTip = (amount: string) => {
        dispatch(setTipAmount(amount))
    };

    // Open tip modal
    const openTipModal = () => {
        setIsTipModalOpen(true);
    };

    // Function to transform cart data into API payload
    // Function to transform cart data into API payload
    const createOrderPayload = () => {
        // Calculate tax amount
        const taxAmount = (cartTotal * cart.taxPercentage) / 100;
        const lineValueExclTax = cartTotal - Number(cart.discountAmount) - loyaltyAmount;

        // Transform cart items to productWithQty format
        const productWithQty = cart.items.map(item => ({
            productId: item.id,
            qty: item.quantity,
            price: item.price,
            discount: Number(item.discountAmount) || 0,
            reason: "",
            oldAmount: item.price * item.quantity,
            newAmount: item.price * item.quantity - (Number(item.discountAmount) || 0),
            discountTypePr: false,
            userDate: new Date().toISOString().split('T')[0],
            userstartTime: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true }),
            userendTime: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true })
        }));

        // Collect all selected modifiers from all cart items for root level
        const allSelectedModifiers = cart.items.reduce((acc: any[], item) => {
            if (item.selectedModifiers && Array.isArray(item.selectedModifiers)) {
                const transformedModifiers = item.selectedModifiers.map(modifier => ({
                    category: modifier.name || "",
                    name: modifier.selectedProperty?.name || "",
                    totalQuantity: modifier.selectedProperty?.totalQuantity || 0,
                    price: modifier.selectedProperty?.price || 0,
                    _id: modifier.selectedProperty?._id || modifier._id || ""
                }));
                return [...acc, ...transformedModifiers];
            }
            return acc;
        }, []);

        // Transform products with proper structure including modifiers
        const products = cart.items.map(cartItem => {
            const product = prouducts?.find(p => p.id === cartItem.id);
            if (!product) return null;

            // Build the product object with all required fields
            return {
                _id: product.id,
                ProductId: product?.ProductId || `PR${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
                Product_pic: product.Product_pic || product.pictureUrl,
                active: product.active ? "true" : "false",
                barCode: product.barCode || "",
                categoryId: product.categoryId || [],
                categoryParents: product.categoryParents || [],
                hasPicture: product.hasPicture || false,
                ingredient: product.ingredient || [],
                isLock: false,
                modifiers: {
                    data: cartItem.selectedModifiers?.map(modifier => ({
                        category: modifier.name || "",
                        name: modifier.selectedProperty?.name || "",
                        totalQuantity: modifier.selectedProperty?.totalQuantity || 0,
                        price: modifier.selectedProperty?.price || 0,
                        _id: modifier.selectedProperty?._id || modifier._id || ""
                    })) || []
                },
                name: product.name,
                note: "",
                offerQuantity: 0,
                originalPrice: product.price,
                price: cartItem.price, // This should include modifiers price
                quantity: cartItem.quantity,
                retailPrice: product.retailPrice || 0,
                reviewId: [],
                selectedDiscount: null,
                totalQuantity: product.totalQuantity || 0,
                unit: product.unit || null,
                userId: product.userId || { _id: userId }
            };
        }).filter(Boolean);

        // Transform taxes to required format
        const taxArray = texes?.map(tax => ({
            name: tax.name,
            addtax: (cartTotal * Number(tax.taxValue)) / 100
        })) || [];

        // Calculate total units
        const totalUnits = cart.items.reduce((total, item) => total + item.quantity, 0);

        // Generate order number
        const orderNumber = `OR${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

        // Create coupon offer structure if coupon is selected
        const couponOffer = selectedCoupon ? [{
            discount: selectedCoupon.discount,
            amount: couponOfferAmount
        }] : [];

        // Calculate actual discount amount based on type
        let actualDiscountAmount = 0;
        if (cart.discountType === 'percentage') {
            actualDiscountAmount = (cartTotal * Number(cart.discountAmount)) / 100;
        } else {
            actualDiscountAmount = Number(cart.discountAmount);
        }

        // Helper function to determine discount type based on reason
        function determineDiscountType(reason: string): string {
            if (!reason) return "General Discount";

            const lowerReason = reason.toLowerCase();
            if (lowerReason.includes("manager")) return "Manager Discount";
            if (lowerReason.includes("receipt")) return "Receipt Discount";
            if (lowerReason.includes("customer complaint")) return "Manager Discount";
            if (lowerReason.includes("loyalty program")) return "Manager Discount";
            if (lowerReason.includes("promotion")) return "Manager Discount";
            if (lowerReason.includes("product issue")) return "Manager Discount";

            return "General Discount";
        }

        // Create discount type object if discount is applied
        const discountTypeObj = (cart.discountAmount && Number(cart.discountAmount) > 0) ? {
            discount: cart.discountAmount,
            reason: cart.discountReason || "No reason provided",
            discountType: determineDiscountType(cart.discountReason)
        } : null;

        const payload = {
            OrderNumber: orderNumber,
            orderNo: cart.invoiceNumber,
            table: selectedTables.map(table => table.id),
            recieveamount: Number(inputValue) || 0,
            orderStatus: "pos",
            Status: "in progress",
            couponOffer: couponOffer,
            customerId: selectedcustomer?._id || null,
            customername: selectedcustomer ? `${selectedcustomer.FirstName} ${selectedcustomer.LastName}` : "Guest",
            discountAmount: actualDiscountAmount,
            discountType: discountTypeObj,
            grandTotal: finalTotal,
            kitchenStatus: "new order",
            lineValue: cartTotal,
            lineValueExclTax: lineValueExclTax,
            lineValueTax: taxAmount,
            loyalityOffer: [],
            priceExclTax: lineValueExclTax,
            product: products,
            productWithQty: productWithQty,
            selectedModifiers: allSelectedModifiers,
            surCharge: 0,
            tax: taxArray,
            timestamp: new Date().toISOString(),
            units: totalUnits,
            userId: userId,
            displayStatus: [],
            refundData: [],
            split: {}
        };

        return payload;
    };

    // Handle Done button click - create order if cash is selected
    const handleDoneClick = async () => {
        console.log("the cart is ", cart)
        console.log("the selected customer is ", selectedcustomer)
        console.log("the proudcts is ", prouducts)
        console.log("the texes is ", texes)
        console.log("the modifer is", modifiers)

        // Only call API if cash payment is selected
        if (selectedPayment === 'cash') {
            // Validate that we have items in cart
            if (!cart.items || cart.items.length === 0) {
                alert('No items in cart to process.');
                return;
            }

            try {
                const payload = createOrderPayload();
                console.log("Order payload:", payload);
                console.log("the taxes" + texes)

                // Debug: Check if selectedModifiers is being added somewhere
                console.log("Cart items with modifiers:", cart.items.map(item => ({
                    id: item.id,
                    name: item.name,
                    selectedModifiers: item.selectedModifiers
                })))
                console.log("All selected modifiers for root level:", payload.selectedModifiers)
                console.log("Selected tables:", selectedTables)
                const orderResult = await createOrderItem({ body: payload });

                if (orderResult.data) {
                    console.log("Order created successfully:", orderResult.data);
                    // Clear cart after successful order creation
                    dispatch(clearCart());
                    // Generate new invoice number for next order
                    dispatch(generateNewInvoiceNumber());
                    console.log('Order created and cart cleared');

                    // Prepare receipt data
                    const receiptData: PrintReceiptData = {
                        orderNo: payload.orderNo,
                        table: selectedTables.length > 0 ? selectedTables.map(t => t.name).join(', ') : undefined,
                        customer: payload.customername,
                        date: new Date().toLocaleString('en-US', {
                            month: '2-digit',
                            day: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            hour12: true
                        }),
                        items: cart.items.map(item => ({
                            name: item.name,
                            quantity: item.quantity,
                            price: item.price,
                            total: item.price * item.quantity
                        })),
                        subtotal: payload.lineValueExclTax,
                        tax: payload.lineValueTax,
                        surcharge: payload.surCharge,
                        total: payload.grandTotal,
                        payment: selectedPayment,
                        received: Number(inputValue) || payload.grandTotal,
                        due: Math.max(0, payload.grandTotal - (Number(inputValue) || payload.grandTotal)),
                        restaurantName: "Restaurant Name", // You can make this configurable
                        restaurantAddress: "123 Main Street, City",
                        restaurantPhone: "(*************"
                    };

                    // Show success SweetAlert with print option
                    const swalResult = await Swal.fire({
                        icon: 'success',
                        title: 'Order Created Successfully!',
                        text: `Order ${payload.orderNo} has been created successfully.`,
                        showCancelButton: true,
                        confirmButtonText: 'Print Receipt',
                        cancelButtonText: 'Close',
                        confirmButtonColor: '#FF5C00',
                        cancelButtonColor: '#6c757d',
                        showClass: {
                            popup: 'animate__animated animate__fadeInDown'
                        },
                        hideClass: {
                            popup: 'animate__animated animate__fadeOutUp'
                        }
                    });

                    // If user clicked "Print Receipt", print the receipt
                    if (swalResult.isConfirmed) {
                        printReceipt(receiptData);
                    }

                    onClose();
                } else if (orderResult.error) {
                    console.error("Error creating order:", orderResult.error);

                    // Show error SweetAlert
                    await Swal.fire({
                        icon: 'error',
                        title: 'Order Creation Failed',
                        text: 'Failed to create order. Please try again.',
                        confirmButtonText: 'Try Again',
                        confirmButtonColor: '#FF5C00',
                        showClass: {
                            popup: 'animate__animated animate__fadeInDown'
                        },
                        hideClass: {
                            popup: 'animate__animated animate__fadeOutUp'
                        }
                    });
                }
            } catch (error) {
                console.error("Failed to create order:", error);

                // Show error SweetAlert for catch block
                await Swal.fire({
                    icon: 'error',
                    title: 'Unexpected Error',
                    text: 'An error occurred while creating the order. Please try again.',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#FF5C00',
                    showClass: {
                        popup: 'animate__animated animate__fadeInDown'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOutUp'
                    }
                });
            }
        } else {
            // For non-cash payments, just generate new invoice and close
            dispatch(generateNewInvoiceNumber());
            console.log('New invoice number generated');
            onClose();
        }
    };

    if (!isOpen && !show) return null;

    // Debug: Log the current selected payment
    console.log('Current selectedPayment:', selectedPayment);
    console.log('Active payment methods:', activePaymentMethods);

    // Render different payment interfaces based on selected payment method
    const renderPaymentInterface = () => {
        console.log('Rendering interface for:', selectedPayment);

        // Force re-render by using a key or ensuring state changes are detected
        switch (selectedPayment) {
            case 'stripe checkout':
                return (
                    <div className="mt-4 space-y-4" key="stripe-checkout">
                        <h3 className="text-lg font-bold text-green-600">STRIPE CHECKOUT INTERFACE</h3>
                        {/* Payment options */}
                        <div className="grid grid-cols-3 gap-3 mb-4">
                            <div
                                className={`border rounded-lg p-3 flex flex-col items-center cursor-pointer transition-all duration-300 ease-in-out transform hover:scale-105 ${selectedStripeOption === 'card' ? 'border-blue-500 shadow-md' : 'border-gray-200'}`}
                                onClick={() => setSelectedStripeOption('card')}
                            >
                                <CreditCard size={20} className={`${selectedStripeOption === 'card' ? 'text-blue-500' : 'text-gray-500'} mb-1 transition-colors duration-300`} />
                                <span className={`${selectedStripeOption === 'card' ? 'text-blue-500' : 'text-gray-600'} text-sm font-medium transition-colors duration-300`}>Card</span>
                            </div>
                            <div
                                className={`border rounded-lg p-3 flex flex-col items-center cursor-pointer transition-all duration-300 ease-in-out transform hover:scale-105 ${selectedStripeOption === 'amazon' ? 'border-orange-500 shadow-md' : 'border-gray-200'}`}
                                onClick={() => setSelectedStripeOption('amazon')}
                            >
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/29/Amazon_Pay_logo.svg/1200px-Amazon_Pay_logo.svg.png" alt="Amazon Pay" className="w-5 h-5 mb-1" />
                                <span className={`${selectedStripeOption === 'amazon' ? 'text-orange-500' : 'text-gray-600'} text-sm transition-colors duration-300`}>Amazon Pay</span>
                            </div>
                            <div
                                className={`border rounded-lg p-3 flex flex-col items-center cursor-pointer transition-all duration-300 ease-in-out transform hover:scale-105 ${selectedStripeOption === 'cashapp' ? 'border-green-500 shadow-md' : 'border-gray-200'}`}
                                onClick={() => setSelectedStripeOption('cashapp')}
                            >
                                <span className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center mb-1">$</span>
                                <span className={`${selectedStripeOption === 'cashapp' ? 'text-green-500' : 'text-gray-600'} text-sm transition-colors duration-300`}>Cash App Pay</span>
                            </div>
                        </div>

                        {/* Show different UI based on selected stripe option with animations */}
                        <div className="relative h-auto overflow-hidden">
                            {/* Card payment option */}
                            <div
                                className={`${selectedStripeOption === 'card'
                                    ? 'block opacity-100'
                                    : 'hidden opacity-0'
                                    }`}
                            >
                                <Elements stripe={stripePromise}>
                                    <StripeCardForm onPaymentSuccess={handlePaymentSuccess} />
                                </Elements>
                            </div>

                            {/* Amazon Pay option */}
                            <div
                                className={`${selectedStripeOption === 'amazon'
                                    ? 'block opacity-100'
                                    : 'hidden opacity-0'
                                    }`}
                            >
                                <div className="space-y-4">
                                    {/* Amazon Pay logo and message */}
                                    <div className="p-4 border border-gray-200 rounded-lg shadow-sm">
                                        <div className="flex items-center mb-3">
                                            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/29/Amazon_Pay_logo.svg/1200px-Amazon_Pay_logo.svg.png" alt="Amazon Pay" className="h-8" />
                                        </div>
                                        <p className="text-gray-700 mb-4">Amazon Pay selected.</p>
                                        <div className="flex items-center text-gray-600 text-sm">
                                            <img src="https://cdn-icons-png.flaticon.com/512/1077/1077976.png" alt="Info" className="w-5 h-5 mr-2" />
                                            <p>After submission, you will be redirected to securely complete next steps.</p>
                                        </div>
                                    </div>

                                    {/* Pay now button */}
                                    <button className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-300 cursor-pointer">
                                        Pay now
                                    </button>
                                </div>
                            </div>

                            {/* Cash App Pay option */}
                            <div
                                className={`${selectedStripeOption === 'cashapp'
                                    ? 'block opacity-100'
                                    : 'hidden opacity-0'
                                    }`}
                            >
                                <div className="space-y-4">
                                    {/* Cash App Pay logo and message */}
                                    <div className="p-4 border border-gray-200 rounded-lg shadow-sm">
                                        <div className="flex items-center mb-3">
                                            <div className="bg-green-500 text-white rounded-lg w-10 h-10 flex items-center justify-center">
                                                <span className="text-xl font-bold">$</span>
                                            </div>
                                        </div>
                                        <p className="text-gray-700 mb-4">Cash App Pay selected.</p>
                                        <div className="flex items-center text-gray-600 text-sm">
                                            <img src="https://cdn-icons-png.flaticon.com/512/1077/1077976.png" alt="Info" className="w-5 h-5 mr-2" />
                                            <p>You will be shown a QR code to scan using Cash App.</p>
                                        </div>
                                    </div>

                                    {/* Pay now button */}
                                    <button className="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition-colors duration-300 cursor-pointer">
                                        Pay now
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                );

            case 'cash':
                return (
                    <div key="cash" className="mb-6">
                        <h3 className="text-lg font-bold text-green-600">CASH INTERFACE</h3>
                        <p className="font-medium text-gray-800 mb-3">Input amount</p>
                        <div className="relative mb-4">
                            <input
                                type="text"
                                className="w-full p-4 border border-gray-200 rounded-lg text-center text-gray-400"
                                value={inputValue}
                                readOnly
                            />
                        </div>

                        {/* Number pad */}
                        <div className="grid grid-cols-4 gap-2">
                            <button onClick={() => handleNumberInput('1')} className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">1</button>
                            <button onClick={() => handleNumberInput('2')} className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">2</button>
                            <button onClick={() => handleNumberInput('3')} className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">3</button>
                            <button onClick={() => handleNumberInput('10')} className="bg-blue-100 rounded-lg py-4 text-lg font-medium text-blue-600 cursor-pointer">10</button>

                            <button onClick={() => handleNumberInput('4')} className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">4</button>
                            <button onClick={() => handleNumberInput('5')} className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">5</button>
                            <button onClick={() => handleNumberInput('6')} className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">6</button>
                            <button onClick={() => handleNumberInput('20')} className="bg-blue-100 rounded-lg py-4 text-lg font-medium text-blue-600 cursor-pointer">20</button>

                            <button onClick={() => handleNumberInput('7')} className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">7</button>
                            <button onClick={() => handleNumberInput('8')} className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">8</button>
                            <button onClick={() => handleNumberInput('9')} className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">9</button>
                            <button onClick={handleBackspace} className="bg-red-100 rounded-lg py-4 flex items-center justify-center text-red-500 cursor-pointer">
                                <X size={24} />
                            </button>

                            <button onClick={() => handleNumberInput('C')} className="bg-red-50 rounded-lg py-4 text-lg font-medium text-red-500 cursor-pointer">C</button>
                            <button onClick={() => handleNumberInput('0')} className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">0</button>
                            <button onClick={() => handleNumberInput('.')} className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">.</button>
                            <button className="bg-gray-50 rounded-lg py-4 text-lg font-medium text-gray-800 cursor-pointer">Add</button>
                        </div>
                    </div>
                );

            case 'check':
                return (
                    <div className="mt-4 space-y-4" key="check">
                        <h3 className="text-lg font-bold text-green-600">CHECK INTERFACE</h3>
                        <p className="font-medium text-gray-800 mb-3">Check Payment</p>

                        {/* Check details form */}
                        <div className="space-y-4">
                            <div>
                                <label className="block text-gray-700 text-sm mb-1">Check Number</label>
                                <input
                                    type="text"
                                    className="w-full p-3 border border-gray-200 rounded-lg"
                                    placeholder="Enter check number"
                                />
                            </div>

                            <div>
                                <label className="block text-gray-700 text-sm mb-1">Bank Name</label>
                                <input
                                    type="text"
                                    className="w-full p-3 border border-gray-200 rounded-lg"
                                    placeholder="Enter bank name"
                                />
                            </div>

                            <div>
                                <label className="block text-gray-700 text-sm mb-1">Amount</label>
                                <input
                                    type="text"
                                    className="w-full p-3 border border-gray-200 rounded-lg"
                                    value={`$${totalAmount.toFixed(2)}`}
                                    readOnly
                                />
                            </div>

                            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                <p className="text-blue-700 text-sm mb-2">Check Payment Instructions</p>
                                <p className="text-gray-600 text-xs">Please verify all check details before processing the payment.</p>
                            </div>

                            <button className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-300 cursor-pointer">
                                Process Check Payment
                            </button>
                        </div>
                    </div>
                );

            case 'terminal pay':
                return (
                    <div className="mt-4 space-y-4" key="terminal-pay">
                        <h3 className="text-lg font-bold text-green-600">TERMINAL PAY INTERFACE</h3>
                        <p className="font-medium text-gray-800 mb-3">Terminal Payment</p>

                        <div className="relative mb-4">
                            <input
                                type="text"
                                className="w-full p-4 border border-gray-200 rounded-lg text-center text-gray-400 text-xl"
                                value={`$${totalAmount.toFixed(2)}`}
                                readOnly
                            />
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                            <p className="text-green-700 text-sm mb-2">Terminal Payment</p>
                            <p className="text-gray-600 text-xs">Please process payment through the terminal device.</p>
                            <div className="mt-3 flex items-center">
                                <div className="animate-pulse w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                <span className="text-green-600 text-sm">Waiting for terminal...</span>
                            </div>
                        </div>

                        <button className="w-full bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition-colors duration-300 cursor-pointer">
                            Complete Terminal Payment
                        </button>
                    </div>
                );

            default:
                return (
                    <div className="mt-4 space-y-4" key="default">
                        <h3 className="text-lg font-bold text-red-600">DEFAULT INTERFACE</h3>
                        <p className="font-medium text-gray-800 mb-3">Payment Method: {selectedPayment || 'Unknown'}</p>
                        <div className="relative mb-4">
                            <input
                                type="text"
                                className="w-full p-4 border border-gray-200 rounded-lg text-center text-gray-400 text-xl"
                                value={`$${inputValue || totalAmount.toFixed(2)}`}
                                readOnly
                            />
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <p className="text-gray-700 text-sm mb-2">Generic Payment Method</p>
                            <p className="text-gray-600 text-xs">Processing payment for: {selectedPayment}</p>
                        </div>

                        <button className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-300 cursor-pointer">
                            Process Payment
                        </button>
                    </div>
                );
        }
    };

    return (
        <>
            {/* Tip Amount Modal - Rendered at the root level */}
            <TipAmountModal
                isOpen={isTipModalOpen}
                onClose={() => setIsTipModalOpen(false)}
                onAddTip={handleAddTip}
            />

            <div className="p-4 flex flex-col items-center">
                {/* Modal overlay */}
                <div
                    style={{ zIndex: 993 }}
                    className={`fixed inset-0  bg-opacity-50 transition-opacity duration-300 ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
                    onClick={onClose}
                />

                {/* Side modal */}
                <div
                    style={{ zIndex: 999999 }}
                    className={`fixed z-auto top-14 right-0 bottom-0 w-auto bg-white shadow-lg transform transition-transform duration-300 ease-in-out ${isOpen ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'} ${show ? '' : 'pointer-events-none'} overflow-y-auto`}
                >
                    {/* Modal header */}
                    <div className="flex justify-between items-center p-6 border-b">
                        <div>
                            <h2 className="text-2xl font-bold text-gray-800">Order Payment</h2>
                            <p className="text-gray-500">Order #102</p>
                        </div>
                        <button onClick={onClose} className="p-1 text-gray-500 cursor-pointer">
                            <X size={24} />
                        </button>
                    </div>

                    {/* Modal content */}
                    <div className="p-6">
                        {/* Payment info */}
                        <div className="bg-gray-50 p-4 rounded mb-6">
                            <div className="flex justify-between mb-2">
                                <span className="text-gray-700">Tip Amount</span>
                                <span className="font-medium">${(Number(tipamount) || 0).toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between font-bold">
                                <span>Total Amount</span>
                                <span className='text-red-400' >${totalAmount.toFixed(2)}</span>
                            </div>
                        </div>

                        {/* Payment methods */}
                        <div className="mb-4">
                            <p className="font-medium text-gray-800 mb-3">Payment Method</p>

                            <div className={`grid gap-3 ${activePaymentMethods.length === 1 ? 'grid-cols-1' : activePaymentMethods.length === 2 ? 'grid-cols-2' : activePaymentMethods.length === 3 ? 'grid-cols-3' : 'grid-cols-4'}`}>
                                {activePaymentMethods.map((method: any) => {
                                    const methodName = method.name.toLowerCase();
                                    const isSelected = selectedPayment === methodName;

                                    const getPaymentIcon = (name: string) => {
                                        switch (name) {
                                            case 'cash':
                                                return <img src="https://cdn-icons-png.flaticon.com/512/2489/2489756.png" alt="Cash" className="w-10 h-6" />;
                                            case 'stripe checkout':
                                                return (
                                                    <div className="bg-indigo-500 text-white p-1 rounded-md w-10 h-10 flex items-center justify-center">
                                                        <span className="text-xl font-bold">S</span>
                                                    </div>
                                                );
                                            case 'terminal pay':
                                                return (
                                                    <div className="bg-green-500 text-white p-1 rounded-md w-10 h-10 flex items-center justify-center">
                                                        <span className="text-xl font-bold">T</span>
                                                    </div>
                                                );
                                            case 'check':
                                                return (
                                                    <div className="bg-blue-500 text-white p-1 rounded-md w-10 h-10 flex items-center justify-center">
                                                        <span className="text-xl font-bold">C</span>
                                                    </div>
                                                );
                                            default:
                                                return (
                                                    <div className="bg-gray-500 text-white p-1 rounded-md w-10 h-10 flex items-center justify-center">
                                                        <span className="text-xl font-bold">{method.name.charAt(0).toUpperCase()}</span>
                                                    </div>
                                                );
                                        }
                                    };

                                    return (
                                        <div
                                            key={method._id}
                                            className={`flex flex-col justify-center items-center py-4 border rounded-lg cursor-pointer transition-all duration-300 ease-in-out transform hover:scale-105 ${isSelected ? 'border-orange-500 shadow-md' : 'border-gray-200'}`}
                                            onClick={() => {
                                                console.log('Clicking payment method:', methodName);
                                                console.log('Current selectedPayment before click:', selectedPayment);
                                                setSelectedPayment(methodName);
                                                console.log('Setting selectedPayment to:', methodName);
                                            }}
                                        >
                                            <div className="flex items-center mb-2">
                                                {getPaymentIcon(methodName)}
                                            </div>
                                            <span className={`text-xs font-medium ${isSelected ? 'text-orange-500' : 'text-gray-600'}`}>
                                                {method.name}
                                            </span>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Render payment interface based on selected method */}
                        {renderPaymentInterface()}

                        {/* Bottom buttons - only show for non-stripe payment methods */}
                        {selectedPayment !== 'stripe checkout' && (
                            <div className="grid grid-cols-4 gap-2 mt-6">
                                <button
                                    onClick={openTipModal}
                                    className="bg-gray-900 cursor-pointer text-white rounded-lg p-4 flex flex-col items-center justify-center hover:bg-gray-800 transition-colors"
                                >
                                    <span className="mb-1">
                                        <Banknote className='size-6' />
                                    </span>
                                    <span className="text-xs">Tip Amount</span>
                                </button>
                                <button className="bg-gray-900 cursor-pointer text-white rounded-lg p-4 flex flex-col items-center justify-center hover:bg-gray-800 transition-colors">
                                    <span className="mb-1">
                                        <Printer size={24} />
                                    </span>
                                    <span className="text-xs">Print Receipt</span>
                                </button>
                                <button className="bg-gray-900 cursor-pointer text-white rounded-lg p-4 flex flex-col items-center justify-center">
                                    <span className="mb-1">
                                        <Mail size={24} />
                                    </span>
                                    <span className="text-xs">Email</span>
                                </button>
                                <button
                                    onClick={handleDoneClick}
                                    disabled={createOrderLoading}
                                    className={`bg-gradient-to-r cursor-pointer from-orange-400 to-red-500 text-white rounded-lg p-4 flex flex-col items-center justify-center ${createOrderLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                >
                                    <span className="mb-1">
                                        {createOrderLoading ? (
                                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                                        ) : (
                                            <Check size={24} />
                                        )}
                                    </span>
                                    <span className="text-xs">{createOrderLoading ? 'Processing...' : 'Done'}</span>
                                </button>
                            </div>
                        )}

                        {/* Only show these buttons when stripe is selected */}
                        {selectedPayment === 'stripe checkout' && (
                            <div className="grid grid-cols-4 gap-2 mt-6 pt-4 border-t">
                                <button
                                    onClick={openTipModal}
                                    className="bg-gray-900  cursor-pointer  text-white rounded-lg p-4 flex flex-col items-center justify-center hover:bg-gray-800 transition-colors"
                                >
                                    <span className="mb-1">
                                        <Banknote className='size-6' />
                                    </span>
                                    <span className="text-xs">Tip Amount</span>
                                </button>
                                <button className="bg-gray-900 cursor-pointer  text-white rounded-lg p-4 flex flex-col items-center justify-center hover:bg-gray-800 transition-colors">
                                    <span className="mb-1">
                                        <Printer size={24} />
                                    </span>
                                    <span className="text-xs">Print Receipt</span>
                                </button>
                                <button className="bg-gray-900 cursor-pointer text-white rounded-lg p-4 flex flex-col items-center justify-center">
                                    <span className="mb-1">
                                        <Mail size={24} />
                                    </span>
                                    <span className="text-xs">Email</span>
                                </button>
                                <button
                                    onClick={handleDoneClick}
                                    disabled={createOrderLoading}
                                    className={`${paymentSucceeded ? 'bg-gradient-to-r from-green-400 to-green-500' : 'bg-gradient-to-r from-orange-400 to-red-500'} text-white rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer ${createOrderLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                >
                                    <span className="mb-1">
                                        {createOrderLoading ? (
                                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                                        ) : (
                                            <Check size={24} />
                                        )}
                                    </span>
                                    <span className="text-xs">{createOrderLoading ? 'Processing...' : 'Done'}</span>
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div >
        </>
    );
}