import { useState, useEffect } from 'react';
import { 
  useGetOnlinekitchendisplayQuery,
  useUpdateOnlineOrderStatusMutation
} from "../../../store/api/kitchenAllorder";
import Swal from 'sweetalert2';

type OrderStatus = 'new order' | 'preparing' | 'ready' | 'done';

interface ModifierItem {
  category: string;
  name: string;
  totalQuantity?: number;
  price?: number;
  _id: string;
}

interface MenuItemModifiers {
  size?: string;
  topping?: string;
  notes?: string;
  data?: ModifierItem[];
}

interface MenuItem {
  _id: string;
  name: string;
  quantity: number;
  modifiers?: MenuItemModifiers;
}

interface Order {
  _id: string;
  orderNo?: string;
  createdAt?: string;
  orderStatus?: OrderStatus;
  product?: MenuItem[];
  customername?: string;
}

export default function AllOrder() {
  const { data: onlineData, isLoading: onlineIsLoading, error: onlineError } = useGetOnlinekitchendisplayQuery();
  const [updateOnlineOrderStatus] = useUpdateOnlineOrderStatusMutation();
  const [orders, setOrders] = useState<Order[]>([]);
  
  // Track expanded state by column type
  const [expandedNewOrder, setExpandedNewOrder] = useState<string | null>(null);
  const [expandedPreparingOrder, setExpandedPreparingOrder] = useState<string | null>(null);
  const [expandedReadyOrder, setExpandedReadyOrder] = useState<string | null>(null);
  
  const [dragOrder, setDragOrder] = useState<string | null>(null);
  const userId = localStorage.getItem('userId') || '';

  // Update orders from API data when it loads
 useEffect(() => {
  if (onlineData) {
    const sortedOrders = [...onlineData].sort((a: any, b: any) => {
      // Status sorting
      if (a.orderStatus === 'new order' && b.orderStatus !== 'new order') return -1;
      if (a.orderStatus !== 'new order' && b.orderStatus === 'new order') return 1;
      
      // Date sorting with error handling
      try {
        const aTime = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const bTime = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return bTime - aTime;
      } catch (e) {
        console.error('Error parsing dates:', e);
        return 0;
      }
    });
    
    setOrders(sortedOrders);
      
      // Set first order in each column as expanded by default
      const newOrders = sortedOrders.filter(order => order.orderStatus === 'new order');
      const preparingOrders = sortedOrders.filter(order => order.orderStatus === 'preparing');
      const readyOrders = sortedOrders.filter(order => order.orderStatus === 'ready');
      
      if (newOrders.length > 0 && !expandedNewOrder) {
        setExpandedNewOrder(newOrders[0]._id);
      }
      
      if (preparingOrders.length > 0 && !expandedPreparingOrder) {
        setExpandedPreparingOrder(preparingOrders[0]._id);
      }
      
      if (readyOrders.length > 0 && !expandedReadyOrder) {
        setExpandedReadyOrder(readyOrders[0]._id);
      }
    }
  }, [onlineData]);
  
  const toggleOrder = (orderId: string, status: OrderStatus) => {
    switch (status) {
      case 'new order':
        setExpandedNewOrder(prev => prev === orderId ? null : orderId);
        break;
      case 'preparing':
        setExpandedPreparingOrder(prev => prev === orderId ? null : orderId);
        break;
      case 'ready':
        setExpandedReadyOrder(prev => prev === orderId ? null : orderId);
        break;
      default:
        break;
    }
  };

  const updateOrderStatus = async (order: Order, newStatus: OrderStatus) => {
    try {
      const orderId = order._id;
      const currentStatus = order.orderStatus;
      
      if (currentStatus === newStatus) return;
      
      // Close the current order in its column before moving
      if (currentStatus === 'new order' && expandedNewOrder === orderId) {
        setExpandedNewOrder(null);
      } else if (currentStatus === 'preparing' && expandedPreparingOrder === orderId) {
        setExpandedPreparingOrder(null);
      } else if (currentStatus === 'ready' && expandedReadyOrder === orderId) {
        setExpandedReadyOrder(null);
      }
      
      // Update online order status
      if (newStatus === 'done') {
        await updateOnlineOrderStatus({
          orderId,
          status: {
            _id: orderId,
            orderStatus: newStatus,
            userId
          }
        }).unwrap();
      } else {
        await updateOnlineOrderStatus({
          orderId,
          status: {
            _id: orderId,
            orderStatus: newStatus
          }
        }).unwrap();
      }
      
      // Update local state
      setOrders(prevOrders => 
        prevOrders.map(order => {
          if (order._id === orderId) {
            return {
              ...order,
              orderStatus: newStatus
            };
          }
          return order;
        })
      );
      
      // If status changed to 'done', remove it from displayed orders after a delay
      if (newStatus === 'done') {
        setTimeout(() => {
          setOrders(prevOrders => prevOrders.filter(o => o._id !== orderId));
        }, 500);
      }
      
      // Show success message
      Swal.fire({
       
        icon: 'success',
        title: 'Order updated successfully',
       
          confirmButtonColor: '#3085d6',
        confirmButtonText: 'OK',
       
      });
    } catch (error) {
      console.error('Error updating order status:', error);
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Failed to update order status!',
      });
    }
  };

  const getOrdersByStatus = (status: OrderStatus) => {
    return orders.filter(order => {
      if (order.orderStatus === 'done') return false;
      return order.orderStatus === status;
    });
  };

  const newOrders = getOrdersByStatus('new order');
  const preparingOrders = getOrdersByStatus('preparing');
  const readyOrders = getOrdersByStatus('ready');

  // Drag and drop functionality
  const handleDragStart = (e: React.DragEvent, order: Order) => {
    setDragOrder(order._id);
    e.dataTransfer.setData('orderData', JSON.stringify(order));
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = '#f9fafb';
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = '';
    }
  };

  const handleDrop = (e: React.DragEvent, newStatus: OrderStatus) => {
    e.preventDefault();
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = '';
    }
    
    const orderData = e.dataTransfer.getData('orderData');
    if (!orderData) return;
    
    const order: Order = JSON.parse(orderData);
    if (order) {
      const currentStatus = order.orderStatus;
      if (currentStatus !== newStatus) {
        updateOrderStatus(order, newStatus);
      }
    }
    
    setDragOrder(null);
  };

  const handleDragEnd = () => {
    setDragOrder(null);
  };

  if (onlineIsLoading) {
    return (
   <div className="flex justify-center items-start bg-white h-screen pt-[20vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading kitchen Online Orders...</p>
        </div>
      </div>
    );
  }

  if (onlineError) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50">
        <div className="p-6 bg-white rounded-lg shadow-md border border-red-200 max-w-md">
          <div className="flex items-center mb-4">
            <svg className="w-6 h-6 text-red-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h2 className="text-lg font-bold text-red-700">Error Loading Orders</h2>
          </div>
          <p className="text-gray-700 mb-4">There was a problem loading the kitchen orders. Please try again later or contact support if the problem persists.</p>
          <button 
            className="w-full py-2 bg-orange-500 text-white font-medium rounded hover:bg-orange-600 transition-colors"
            onClick={() => window.location.reload()}
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col bg-gray-50 p-2 h-screen">
      <div className="flex flex-1 gap-4 overflow-hidden">
        {/* New Orders Column */}
        <div
          className="flex-1 flex flex-col border border-gray-300 rounded-2xl overflow-hidden h-[68vh]"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, 'new order')}
        >
          <div className="bg-orange-50 p-3 font-bold text-lg">
            New Orders ({newOrders.length})
          </div>
          <div className="flex-1 overflow-y-auto p-2 space-y-3 scrollbar-hide" style={{ maxHeight: "calc(100% - 56px)" }}>
            {newOrders.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <svg className="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p>No new orders</p>
              </div>
            ) : (
              newOrders.map(order => (
                <OrderCard
                  key={order._id}
                  order={order}
                  expanded={expandedNewOrder === order._id}
                  onToggle={() => toggleOrder(order._id, 'new order')}
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
                  dragActive={dragOrder === order._id}
                >
                  <button
                    className="w-full py-2 bg-orange-500 text-white font-medium mt-2 rounded hover:bg-orange-600 transition-colors"
                    onClick={() => updateOrderStatus(order, 'preparing')}
                  >
                    Start Preparing
                  </button>
                </OrderCard>
              ))
            )}
          </div>
        </div>

        {/* Preparing Orders Column */}
        <div
          className="flex-1 flex flex-col border border-gray-300 rounded-2xl overflow-hidden h-[68vh]" 
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, 'preparing')}
        >
          <div className="bg-orange-50 p-3 font-bold text-lg">
            Preparing ({preparingOrders.length})
          </div>
          <div className="flex-1 overflow-y-auto p-2 space-y-3 scrollbar-hide" style={{ maxHeight: "calc(100% - 56px)" }}>
            {preparingOrders.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <svg className="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                </svg>
                <p>No orders in preparation</p>
              </div>
            ) : (
              preparingOrders.map(order => (
                <OrderCard
                  key={order._id}
                  order={order}
                  expanded={expandedPreparingOrder === order._id}
                  onToggle={() => toggleOrder(order._id, 'preparing')}
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
                  dragActive={dragOrder === order._id}
                >
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      className="py-2 bg-gray-300 text-gray-700 font-medium rounded hover:bg-gray-400 transition-colors"
                      onClick={() => updateOrderStatus(order, 'new order')}
                    >
                      Move Back
                    </button>
                    <button
                      className="py-2 bg-orange-500 text-white font-medium rounded hover:bg-orange-600 transition-colors"
                      onClick={() => updateOrderStatus(order, 'ready')}
                    >
                      Mark as Ready
                    </button>
                  </div>
                </OrderCard>
              ))
            )}
          </div>
        </div>

        {/* Ready Orders Column */}
        <div
          className="flex-1 flex flex-col border border-gray-300 rounded-2xl overflow-hidden h-[68vh]"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, 'ready')}
        >
          <div className="bg-orange-50 p-3 font-bold text-lg">
            Ready ({readyOrders.length})
          </div>
          <div className="flex-1 overflow-y-auto p-2 space-y-3 scrollbar-hide" style={{ maxHeight: "calc(100% - 56px)" }}>
            {readyOrders.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <svg className="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p>No orders ready for pickup</p>
              </div>
            ) : (
              readyOrders.map(order => (
                <OrderCard
                  key={order._id}
                  order={order}
                  expanded={expandedReadyOrder === order._id}
                  onToggle={() => toggleOrder(order._id, 'ready')}
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
                  dragActive={dragOrder === order._id}
                >
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      className="py-2 bg-gray-300 text-gray-700 font-medium rounded hover:bg-gray-400 transition-colors"
                      onClick={() => updateOrderStatus(order, 'preparing')}
                    >
                      Move Back
                    </button>
                    <button
                      className="py-2 bg-orange-500 text-white font-medium rounded hover:bg-orange-600 transition-colors"
                      onClick={() => updateOrderStatus(order, 'done')}
                    >
                      Mark as Done
                    </button>
                  </div>
                </OrderCard>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function OrderCard({ 
  order, 
  expanded, 
  onToggle, 
  onDragStart, 
  onDragEnd,
  dragActive,
  children 
}: {
  order: Order;
  expanded: boolean;
  onToggle: () => void;
  onDragStart: (e: React.DragEvent, order: Order) => void;
  onDragEnd: () => void;
  dragActive: boolean;
  children: React.ReactNode;
}) {
  const displayOrderNo = order.orderNo || 'No Order #';
  const orderDate = order.createdAt ? new Date(order.createdAt).toLocaleString() : '';

  return (
    <div 
      className={`bg-white rounded-md shadow border border-gray-300 overflow-hidden cursor-move transition-opacity ${dragActive ? 'opacity-50' : ''}`}
      draggable="true"
      onDragStart={(e) => onDragStart(e, order)}
      onDragEnd={onDragEnd}
    >
      <div 
        className="flex justify-between items-center p-3 border-b border-gray-200 cursor-pointer bg-blue-50"
        onClick={onToggle}
      >
        <div className="flex items-center">
          <div className="text-orange-500 mr-2">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <span className="font-medium">{displayOrderNo}</span>
        </div>
        
        <div className='flex items-center space-x-2'>
          <span className="text-md font-medium bg-blue-100 text-blue-800 px-2 py-0.5 rounded">Online</span>
          <button className="text-gray-500">
            <svg 
              className={`w-5 h-5 transition-transform duration-200 ${expanded ? 'rotate-180' : ''}`}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
        </div>
      </div>
      
      {expanded && (
        <div className="p-3">
          {orderDate && (
            <div className="flex justify-between mb-2">
              <span className="text-md font-medium">{order.customername || 'Anonymous'}</span>
              <span className="text-md font-medium">{orderDate}</span>
            </div>
          )}
          
          <div className="mb-2 text-sm">
            <span className="font-semibold">Order Status: </span>
            <span className="text-orange-600">{order.orderStatus}</span>
          </div>
        
          {order.product && order.product.map((item, idx) => (
            <div key={idx} className="mb-3 pl-2 border-l-2 border-orange-100">
              <div className="flex justify-between">
                <span className="font-medium">{item.name}</span>
                <span className="text-gray-600">{item.quantity}x</span>
              </div>

              {item.modifiers && item.modifiers.data && item.modifiers.data.length > 0 && (
                <div className="mt-2 space-y-1">
                  {item.modifiers.data.map((modifier, modIdx) => (
                    <div key={modIdx} className="flex justify-between ml-4 text-sm">
                      <span className="text-gray-600">{modifier.category}:</span>
                      <span className="font-medium">{modifier.name}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}

          {children}
        </div>
      )}
    </div>
  );
}