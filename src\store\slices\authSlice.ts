import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import { userApi } from '../api/userApi.ts';

export interface User {
  _id: string;
  id?: string;
  name: string;
  email: string;
  role: string;
  userId?: string;
  appFee?: number;
  surChargeThreshold?: number;
  isActive?: boolean;
  stripe_account_id?: string;
  stripe_acess_token?: string;
  stripe_refresh_token?: string;
  google?: boolean;
  monthlySurchargePayments?: Array<{
    month: number;
    year: number;
    paid: boolean;
    amount: number;
    _id: string;
  }>;
  createdDate?: string;
  createdAt?: string;
  updatedAt?: string;
  loginDate?: string;
  password?: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: !!localStorage.getItem('token'),
  isLoading: false,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (
      state,
      action: PayloadAction<{ token: string }>
    ) => {
      const { token } = action.payload;
      state.token = token;
      state.isAuthenticated = true;
      localStorage.setItem('token', token);
    },
    setUser: (state, action: PayloadAction<User>) => {
      // Create a new object with id set as an alias for _id for compatibility
      if (action.payload) {
        state.user = {
          ...action.payload,
          id: action.payload._id
        };
      } else {
        state.user = null;
      }
    },
    clearCredentials: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      localStorage.removeItem('token');
      localStorage.removeItem('userId');
    },
  },
  extraReducers: (builder) => {
    builder

      // Handle login success from userApi
      .addMatcher(
        userApi.endpoints.login.matchFulfilled,
        (state, { payload }) => {
          state.token = payload.token;
          state.isAuthenticated = true;
          localStorage.setItem('token', payload.token);

          // Create a user object from the login response
          if (payload.name) {
            const userData = {
              _id: payload.userId._id,
              name: payload.name.name,
              email: payload.email,
              role: payload.role,
              loginDate: payload.loginDate,
              appFee: payload.appFee,
              userId: payload.name.userId,
              isActive: payload.name.isActive,
              createdDate: payload.name.createdDate,
              createdAt: payload.name.createdAt,
              updatedAt: payload.name.updatedAt,
              surChargeThreshold: payload.name.surChargeThreshold,
              stripe_account_id: payload.name.stripe_account_id,
              stripe_acess_token: payload.name.stripe_acess_token,
              stripe_refresh_token: payload.name.stripe_refresh_token,
              google: payload.name.google,
              monthlySurchargePayments: payload.name.monthlySurchargePayments,
              password: payload.name.password
            };

            state.user = {
              ...userData,
              id: userData._id
            };
             // ✅ Store userId in localStorage
                   localStorage.setItem('userId', userData._id);
          }
        }
      )


  },
});

export const { setCredentials, setUser, clearCredentials } = authSlice.actions;

export const selectCurrentUser = (state: RootState) => state.auth.user;
export const selectIsAuthenticated = (state: RootState) => state.auth.isAuthenticated;
export const selectAuthToken = (state: RootState) => state.auth.token;
export const selectIsLoading = (state: RootState) => state.auth.isLoading;

export default authSlice.reducer;