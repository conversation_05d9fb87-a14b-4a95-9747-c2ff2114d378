import { baseApi } from "./baseApi";

export interface Supplier {
  id: string;
  SupplierID: string;
  SupplierName: string;
  EmailAddress: string;
  ContactNumber: string;
  BussinessAddress: string;
  TaxIdentification: string;
  OtherInformation: string;
  ProductOffered: string;
}

export const supplierApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getSuppliers: builder.query<Supplier[], string>({
      query: (userId) => `/Supplier?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          SupplierID: item.SupplierID,
          SupplierName: item.SupplierName,
          EmailAddress: item.EmailAddress,
          ContactNumber: item.ContactNumber,
          BussinessAddress: item.BussinessAddress,
          TaxIdentification: item.TaxIdentification,
          OtherInformation: item.OtherInformation,
          ProductOffered: item.ProductOffered,
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "Supplier" as const, id })),
              { type: "Supplier", id: "LIST" },
            ]
          : [{ type: "Supplier", id: "LIST" }],
    }),

    getSupplier: builder.query<Supplier, string>({
      query: (id) => `/Supplier/${id}`,
      transformResponse: (response: any) => ({
        id: response._id,
        SupplierID: response.SupplierID,
        SupplierName: response.SupplierName,
        EmailAddress: response.EmailAddress,
        ContactNumber: response.ContactNumber,
        BussinessAddress: response.BussinessAddress,
        TaxIdentification: response.TaxIdentification,
        OtherInformation: response.OtherInformation,
        ProductOffered: response.ProductOffered,
      }),
      providesTags: (_result, _error, id) => [{ type: "Supplier", id }],
    }),

    deleteSupplier: builder.mutation<void, string>({
      query: (id) => ({
        url: `/Supplier/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            supplierApi.util.invalidateTags([
              { type: "Supplier", id },
              { type: "Supplier", id: "LIST" }
            ])
          );
        } catch (error) {
          console.error("Failed to delete supplier", error);
        }
      },
    }),

    postSupplier: builder.mutation<any, any>({
      query: (data) => {
        console.log("Sending POST request with data:", data);
        return {
          url: "/Supplier",
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: [{ type: "Supplier", id: "LIST" }],
    }),

    putSupplier: builder.mutation<
      any,
      { id: string; formData: any }
    >({
      query: ({ id, formData }) => ({
        url: `/Supplier/${id}`,
        method: "PUT",
        body: {
          ...formData,
        },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "Supplier", id },
        { type: "Supplier", id: "LIST" },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetSuppliersQuery,
  useGetSupplierQuery,
  useDeleteSupplierMutation,
  usePostSupplierMutation,
  usePutSupplierMutation,
} = supplierApi;