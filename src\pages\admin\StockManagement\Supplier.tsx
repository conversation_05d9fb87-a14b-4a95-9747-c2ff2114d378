import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  Plus,
} from "lucide-react";
import {
  useGetSuppliersQuery,
  useDeleteSupplierMutation,
} from "../../../store/api/supplierApi";
import Swal from 'sweetalert2';
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";

export default function Supplier() {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const navigate = useNavigate();
  const userId = localStorage.getItem('userId') || '';

  const { data: suppliersData, isLoading, error, refetch } = useGetSuppliersQuery(userId);
  const [deleteSupplier, { isLoading: isDeleting }] = useDeleteSupplierMutation();

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleDelete = (id: string, supplierName: string) => {
    Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to delete "${supplierName}" supplier?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!'
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          await deleteSupplier(id).unwrap();
          
          Swal.fire({
            title: 'Deleted!',
            text: 'Supplier has been deleted successfully.',
            icon: 'success',
            confirmButtonColor: '#4f46e5'
          });
          
          refetch();
        } catch (error) {
          console.error('Failed to delete supplier:', error);
          
          Swal.fire({
            title: 'Error!',
            text: 'Failed to delete supplier.',
            icon: 'error',
            confirmButtonColor: '#4f46e5'
          });
        }
      }
    });
  };

  const handleAddSupplier = () => {
    navigate('/admin/stock-management/supplier/supplier-form');
  };

  const handleEdit = (id: string) => {
    navigate(`/admin/stock-management/supplier/supplier-form/${id}`);
  };

  // Filter suppliers based on search term
  const filteredSuppliers = suppliersData?.filter(supplier =>
    supplier.SupplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.SupplierID.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.ContactNumber.includes(searchTerm)
  ) || [];

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentSuppliers = filteredSuppliers.slice(indexOfFirstItem, indexOfLastItem);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  useEffect(() => {
    if (error) {
      Swal.fire({
        title: 'Error!',
        text: 'Failed to fetch suppliers',
        icon: 'error',
        confirmButtonColor: '#4f46e5'
      });
      console.error('Error fetching suppliers:', error);
    }
  }, [error]);

  return (
    <div className="p-4 bg-gray-50">
      <div className="flex justify-between md:flex-row flex-col items-center p-2 border border-gray-50 bg-white mb-2 rounded-2xl">
        <h1 className="text-3xl p-3 font-bold text-gray-800">Supplier Details</h1>
        <div className="flex gap-2 md:flex-row flex-col-reverse">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Supplier"
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchTerm}
              onChange={handleSearch}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400 h-5 w-5" />
          </div>
          <button
            onClick={handleAddSupplier}
            className="bg-orange-500 hover:bg-orange-600 text-white font-medium px-4 py-2 rounded-lg flex items-center gap-1"
          >
            Add New Supplier
            <Plus size={20} />
          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-start bg-white h-screen pt-[30vh]">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
            <p className="text-gray-600 font-medium">Loading Suppliers...</p>
          </div>
        </div>
      ) : error ? (
        <p className="text-center text-red-500 py-8">Failed to load suppliers.</p>
      ) : (
        <>
          <div className="bg-white shadow-sm border border-gray-200 rounded-2xl">
            <div className="overflow-x-auto rounded-2xl">
              <table className="w-full">
                <thead className="bg-orange-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">
                      Supplier ID
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">
                      Supplier Name
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {currentSuppliers.length > 0 ? (
                    currentSuppliers.map((supplier) => (
                      <tr key={supplier.id} className="hover:bg-orange-50">
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {supplier.SupplierID}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {supplier.SupplierName}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {supplier.ContactNumber}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {supplier.EmailAddress}
                        </td>
                        <td className="px-6 py-4 text-sm flex gap-4">
                          <button
                            onClick={() => handleEdit(supplier.id)}
                            className="text-blue-500 hover:text-blue-700"
                            disabled={isDeleting}
                          >
                            <Edit
                              id="edit-icon"
                              data-tooltip-id="edit-tooltip"
                              data-tooltip-content="Edit"
                              size={20}
                              className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                            />
                            <Tooltip
                              id="edit-tooltip"
                              place="bottom"
                              className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                            />
                          </button>
                          <button
                            onClick={() => handleDelete(supplier.id, supplier.SupplierName)}
                            className={`text-red-500 hover:text-red-700 ${isDeleting ? "cursor-not-allowed opacity-50" : ""}`}
                            disabled={isDeleting}
                          >
                            <Trash2
                              id="delete-icon"
                              data-tooltip-id="delete-tooltip"
                              data-tooltip-content="Delete"
                              size={20}
                              className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                            />
                            <Tooltip
                              id="delete-tooltip"
                              place="bottom"
                              className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                            />
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                        No suppliers found.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Pagination */}
          {filteredSuppliers.length > 0 && (
            <div className="flex justify-start items-center space-x-2 p-4">
              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  currentPage === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft size={18} className="text-gray-600" />
              </button>

              {Array.from(
                { length: Math.ceil(filteredSuppliers.length / itemsPerPage) },
                (_, i) => i + 1
              ).map((number) => (
                <button
                  key={number}
                  onClick={() => paginate(number)}
                  className={`w-8 h-8 rounded-md flex items-center justify-center border ${
                    currentPage === number
                      ? "bg-orange-500 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  {number}
                </button>
              ))}

              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  indexOfLastItem >= filteredSuppliers.length
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(currentPage + 1)}
                disabled={indexOfLastItem >= filteredSuppliers.length}
              >
                <ChevronRight size={18} className="text-gray-600" />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}