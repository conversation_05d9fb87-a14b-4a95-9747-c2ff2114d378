// Define types for product modifiers
export interface ModifierProperty {
  name: string;
  totalQuantity: number;
  price: number;
  _id: string;
}

export interface ModifierOption {
  name: string;
  properties?: ModifierProperty[];
  _id?: string;
  selectedProperty?: ModifierProperty;
}

export interface ProductModifier {
  _id: string;
  Modifier: ModifierOption[];
  isActive: boolean;
  productId: string;
}
