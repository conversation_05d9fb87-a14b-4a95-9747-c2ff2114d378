import React, { useState, useEffect } from "react";
import CustomModal from "../../CustomModal";
import { usePutCustomerMutation } from "../../../store/api/customerApi";
import Swal from 'sweetalert2';

interface CustomerBalanceEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: {
    _id: string;
    FirstName: string;
    LastName: string;
    CustomerLoyalty?: {
      CardNo: string;
      creditLimits: number;
    };
  } | null;
  onUpdateSuccess?: () => void; // Add callback for successful update
}

const CustomerBalanceEditModal: React.FC<CustomerBalanceEditModalProps> = ({
  isOpen,
  onClose,
  customer,
  onUpdateSuccess,
}) => {
  const [addBalance, setAddBalance] = useState<string>("");
  const [subtractBalance, setSubtractBalance] = useState<string>("");
  const [newBalance, setNewBalance] = useState<number>(0);
  const [putCustomer] = usePutCustomerMutation();

  // Set initial balance when customer changes
  useEffect(() => {
    if (customer && customer.CustomerLoyalty) {
      setNewBalance(customer.CustomerLoyalty.creditLimits || 0);
    } else {
      setNewBalance(0);
    }
    // Reset input fields
    setAddBalance("");
    setSubtractBalance("");
  }, [customer]);

  // Calculate new balance when add or subtract values change
  useEffect(() => {
    const oldBalance = customer?.CustomerLoyalty?.creditLimits || 0;
    const addValue = parseFloat(addBalance) || 0;
    const subtractValue = parseFloat(subtractBalance) || 0;

    setNewBalance(oldBalance + addValue - subtractValue);
  }, [addBalance, subtractBalance, customer]);

  const handleAddBalanceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "" || /^\d*\.?\d*$/.test(value)) {
      setAddBalance(value);
      // Clear subtract field if adding
      if (value !== "") {
        setSubtractBalance("");
      }
    }
  };

  const handleSubtractBalanceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "" || /^\d*\.?\d*$/.test(value)) {
      setSubtractBalance(value);
      // Clear add field if subtracting
      if (value !== "") {
        setAddBalance("");
      }
    }
  };

  const handleUpdateBalance = async () => {
    if (!customer) return;

    try {
      // Prepare the updated customer data
      const updatedCustomerData = {
        _id: customer._id,
        CustomerLoyalty: {
          ...(customer.CustomerLoyalty || {}),
          creditLimits: newBalance,
        },
      };

      // Call the update mutation
      await putCustomer(updatedCustomerData).unwrap();

      // Show success message
      Swal.fire({
        title: "Success!",
        text: "Customer balance updated successfully",
        icon: "success",
        timer: 2000,
        showConfirmButton: false,
      });

      // Call the onUpdateSuccess callback if provided
      if (onUpdateSuccess) {
        onUpdateSuccess();
      }

      // Close the modal
      onClose();
    } catch (error) {
      console.error("Error updating customer balance:", error);
      Swal.fire({
        title: "Error!",
        text: "Failed to update customer balance",
        icon: "error",
      });
    }
  };

  const footer = (
    <div className="flex justify-between items-center">
      <button
        onClick={onClose}
        className="px-10 py-2 border border-orange text-orange rounded-full hover:bg-gray-50 transition-colors"
      >
        Cancel
      </button>
      <button
        onClick={handleUpdateBalance}
        className="px-10 py-2 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-colors"
      >
        Update Balance
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Balance Adjustment"
      width="max-w-xl"
      footer={footer}
    >
      <div className="p-6">
        {customer && (
          <>
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4">Customer Details</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-gray-500">Customer Name</p>
                  <p className="font-medium">{`${customer.FirstName} ${customer.LastName}`}</p>
                </div>
                <div>
                  <p className="text-gray-500">Card Number</p>
                  <p className="font-medium">{customer.CustomerLoyalty?.CardNo || "N/A"}</p>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-gray-500">Old Balance</p>
              <div className="bg-gray-100 p-4 rounded-md text-center">
                <span className="text-xl font-semibold">${(customer.CustomerLoyalty?.creditLimits || 0).toFixed(2)}</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-gray-500 mb-2">Add balance</label>
                <input
                  type="text"
                  value={addBalance}
                  onChange={handleAddBalanceChange}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:border-orange-500"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-gray-500 mb-2">Subtract balance</label>
                <input
                  type="text"
                  value={subtractBalance}
                  onChange={handleSubtractBalanceChange}
                  className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:border-orange-500"
                  placeholder="0.00"
                />
              </div>
            </div>

            <div>
              <p className="text-gray-500 mb-2">New Balance</p>
              <div className="bg-gray-100 p-4 rounded-md text-center">
                <span className="text-2xl font-bold">${newBalance.toFixed(2)}</span>
              </div>
            </div>
          </>
        )}
      </div>
    </CustomModal>
  );
};

export default CustomerBalanceEditModal;
