import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { Employee } from '../api/EmployeeListApi';
import { employeeListApi } from '../api/EmployeeListApi';

interface EmployeeState {
  employees: Employee[];
  isLoading: boolean;
  error: string | null;
}

const initialState: EmployeeState = {
  employees: [],
  isLoading: false,
  error: null,
};

const employeeListSlice = createSlice({
  name: 'employeeList',
  initialState,
  reducers: {
    setEmployees: (state, action: PayloadAction<Employee[]>) => {
      state.employees = action.payload;
    },
    clearEmployees: (state) => {
      state.employees = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET Employees List
      .addMatcher(
        employeeListApi.endpoints.getEmployeesList.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        employeeListApi.endpoints.getEmployeesList.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.employees = payload;
        }
      )
      .addMatcher(
        employeeListApi.endpoints.getEmployeesList.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch employees';
        }
      )

      // DELETE Employee
      .addMatcher(
        employeeListApi.endpoints.deleteEmployeeList.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        employeeListApi.endpoints.deleteEmployeeList.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const employeeId = action.meta.arg.originalArgs as string;
          state.employees = state.employees.filter(
            (employee) => employee.id !== employeeId
          );
        }
      )
      .addMatcher(
        employeeListApi.endpoints.deleteEmployeeList.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete employee';
        }
      )

      // POST Employee
      .addMatcher(
        employeeListApi.endpoints.postEmployeeList.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        employeeListApi.endpoints.postEmployeeList.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.employees.push({
            id: payload._id,
            userName: payload.userName,
            firstName: payload.firstName,
            lastName: payload.lastName,
            email: payload.email,
            employeeId: payload.employeeId,
            userId: payload.userId,
            role: payload.role,
            hourlyRate: payload.hourlyRate,
            employeeType: payload.employeeType,
            overTimeRate: payload.overTimeRate,
            totalHours: payload.totalHours,
            phoneNo: payload.phoneNo,
            address: payload.address,
            employeeStartTime: payload.employeeStartTime,
            employeeEndTime: payload.employeeEndTime,
            deductions: payload.deductions || [],
          });
        }
      )
      .addMatcher(
        employeeListApi.endpoints.postEmployeeList.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create employee';
        }
      )

      // PUT Employee
      .addMatcher(
        employeeListApi.endpoints.putEmployeeList.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        employeeListApi.endpoints.putEmployeeList.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          
          // Fix: Check if payload exists and has the necessary data
          if (payload && payload._id) {
            // Handle the case where the payload itself contains the employee data
            const updated = {
              id: payload._id,
              userName: payload.userName,
              firstName: payload.firstName,
              lastName: payload.lastName,
              email: payload.email,
              employeeId: payload.employeeId,
              userId: payload.userId,
              role: payload.role,
              hourlyRate: payload.hourlyRate,
              employeeType: payload.employeeType,
              overTimeRate: payload.overTimeRate,
              totalHours: payload.totalHours,
              phoneNo: payload.phoneNo,
              address: payload.address,
              employeeStartTime: payload.employeeStartTime,
              employeeEndTime: payload.employeeEndTime,
              deductions: payload.deductions || [],
            };
            state.employees = state.employees.map((employee) =>
              employee.id === updated.id ? updated : employee
            );
          } else if (payload && payload.data && payload.data._id) {
            // Handle the case where payload.data contains the employee data
            const updated = {
              id: payload.data._id,
              userName: payload.data.userName,
              firstName: payload.data.firstName,
              lastName: payload.data.lastName,
              email: payload.data.email,
              employeeId: payload.data.employeeId,
              userId: payload.data.userId,
              role: payload.data.role,
              hourlyRate: payload.data.hourlyRate,
              employeeType: payload.data.employeeType,
              overTimeRate: payload.data.overTimeRate,
              totalHours: payload.data.totalHours,
              phoneNo: payload.data.phoneNo,
              address: payload.data.address,
              employeeStartTime: payload.data.employeeStartTime,
              employeeEndTime: payload.data.employeeEndTime,
              deductions: payload.data.deductions || [],
            };
            state.employees = state.employees.map((employee) =>
              employee.id === updated.id ? updated : employee
            );
          } else {
            // If we get here, the payload structure is not as expected
            console.error('Invalid payload structure for employee update:', payload);
          }
        }
      )
      .addMatcher(
        employeeListApi.endpoints.putEmployeeList.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update employee';
        }
      );
  },
});

export const { setEmployees, clearEmployees } = employeeListSlice.actions;

export const selectEmployees = (state: RootState) => state.employeeList.employees;
export const selectEmployeeLoading = (state: RootState) => state.employeeList.isLoading;
export const selectEmployeeError = (state: RootState) => state.employeeList.error;

export default employeeListSlice.reducer;