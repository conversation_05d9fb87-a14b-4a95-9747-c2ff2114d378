import { useState } from "react";
import { NavLink } from "react-router-dom";
import DashboardIcon from '@mui/icons-material/Dashboard';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import KitchenIcon from '@mui/icons-material/Kitchen';
import CategoryIcon from '@mui/icons-material/Category';
import TableChartIcon from '@mui/icons-material/TableChart';
import Inventory2Icon from '@mui/icons-material/Inventory2';
import BarChartIcon from '@mui/icons-material/BarChart';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import GroupsIcon from '@mui/icons-material/Groups';
import PeopleIcon from '@mui/icons-material/People';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import { ChevronDown } from "lucide-react";

type ExpandedState = {
  catalog: boolean;
  tableManagement: boolean;
  stockManagement: boolean;
  marketing: boolean;
  administration: boolean;
  employees: boolean;
  customer: boolean;
};

export default function Sidebar() {
  // Initialize all expanded states as false
  const [activeExpand, setActiveExpand] = useState<keyof ExpandedState | null>(null);

  const toggleExpand = (item: keyof ExpandedState) => {
    setActiveExpand(prevActive => prevActive === item ? null : item);
  };

  // Helper function to check if a section is expanded
  const isExpanded = (item: keyof ExpandedState) => activeExpand === item;

  return (
    <div className="w-72 bg-white border-r border-r-gray-200">
      <div className="flex flex-col p-2 pt-5 space-y-1">
        <NavLink
          to="/admin/dashboard"
          className={({ isActive }) =>
            `group flex items-center p-2 rounded-lg transition-all duration-300 ${
              isActive
                ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
            }`
          }
        >
          {({ isActive }) => (
            <>
              <DashboardIcon 
                className={`h-5 w-5 mr-3 transition-colors duration-300 ${
                  isActive
                    ? "text-white"
                    : "text-orange-500 group-hover:text-white"
                }`}
              />
              <span className="font-sm text-lg">Dashboard</span>
            </>
          )}
        </NavLink>

        <NavLink
          to="/admin/orders"
          end={false}
          className={({ isActive }) =>
            `group flex items-center p-2 rounded-lg transition-all duration-300 ${
              isActive
                ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
            }`
          }
        >
          {({ isActive }) => (
            <>
              <ReceiptLongIcon
                className={`h-5 w-5 mr-3 transition-colors duration-300 ${
                  isActive
                    ? "text-white"
                    : "text-orange-500 group-hover:text-white"
                }`}
              />
              <span className="font-sm text-lg">Orders</span>
            </>
          )}
        </NavLink>

        <NavLink
          to="/admin/kitchen-display"
          end={false}
          className={({ isActive }) =>
            `group flex items-center p-2 rounded-lg transition-all duration-300 ${
              isActive
                ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
            }`
          }
        >
          {({ isActive }) => (
            <>
              <KitchenIcon
                className={`h-5 w-5 mr-3 transition-colors duration-300 ${
                  isActive
                    ? "text-white"
                    : "text-orange-500 group-hover:text-white"
                }`}
              />
              <span className="font-sm text-lg">Kitchen Display</span>
            </>
          )}
        </NavLink>

        <div className="flex flex-col">
          <div
            className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-100 cursor-pointer text-gray-800"
            onClick={() => toggleExpand("catalog")}
          >
            <div className="flex items-center">
              <div className="text-orange-500">
                <CategoryIcon className="h-5 w-5 mr-3" />
              </div>
              <span className="font-sm text-lg">Catalog</span>
            </div>
            <ChevronDown
              className={`h-4 w-4 text-orange-500 transition-transform ${
                isExpanded("catalog") ? "transform rotate-180" : ""
              }`}
            />
          </div>
          {isExpanded("catalog") && (
            <div className="ml-8 text-sm space-y-2 mt-1 mb-1">
              <NavLink
                to="/admin/catalog/parent-categories"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
                Parent Categories
              </NavLink>

              <NavLink
                to="/admin/catalog/categories"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300 text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
                Categories
              </NavLink>
              
              <NavLink
                to="/admin/catalog/menu-items"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300 text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
                Menu Items
              </NavLink>

              <NavLink
                to="/admin/catalog/modifiers"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
                Modifiers
              </NavLink>
            </div>
          )}
        </div>

        {/* Expandable - Table Management */}
        <div className="flex flex-col">
          <div
            className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-100 cursor-pointer text-gray-800"
            onClick={() => toggleExpand("tableManagement")}
          >
            <div className="flex items-center">
              <div className="text-orange-500">
                <TableChartIcon className="h-5 w-5 mr-3" />
              </div>
              <span className="font-sm text-lg">Table Management</span>
            </div>
            <ChevronDown
              className={`h-4 w-4 text-orange-500 transition-transform ${
                isExpanded("tableManagement") ? "transform rotate-180" : ""
              }`}
            />
          </div>
          {isExpanded("tableManagement") && (
            <div className="ml-8 text-sm space-y-2 mt-1 mb-1">
               <NavLink
                to="/admin/tables-management/tables"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Tables
              </NavLink>
              <NavLink
                to="/admin/tables-management/site-management"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Site Management
              </NavLink>
              <NavLink
                to="/admin/tables-management/tables-customization"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Tables Customization
              </NavLink>
             
            </div>
          )}
        </div>
        <div className="flex flex-col">
          <div
            className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-100 cursor-pointer text-gray-800"
            onClick={() => toggleExpand("stockManagement")}
          >
            <div className="flex items-center">
              <div className="text-orange-500">
                <Inventory2Icon className="h-5 w-5 mr-3" />
              </div>
              <span className="font-sm text-lg">Stock Management</span>
            </div>
            <ChevronDown
              className={`h-4 w-4 text-orange-500 transition-transform ${
                isExpanded("stockManagement") ? "transform rotate-180" : ""
              }`}
            />
          </div>
          {isExpanded("stockManagement") && (
            <div className="ml-8 text-sm space-y-2 mt-1 mb-1">
             <NavLink
                to="/admin/stock-management/ingredients-category"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Ingredients Category
              </NavLink>
              <NavLink
                 to="/admin/stock-management/ingredients"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300 text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
              Ingredients
              </NavLink>
              <NavLink
                to="/admin/stock-management/supplier"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300 text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
              Supplier
              </NavLink>
              <NavLink
                to="/admin/stock-management/unit-of-measurement"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Unit Of Measurement
              </NavLink>
              <NavLink
                 to="/admin/stock-management/wastages"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Wastages
              </NavLink>
            </div>
          )}
        </div>
        <div className="flex flex-col">
          <div
            className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-100 cursor-pointer text-gray-800"
            onClick={() => toggleExpand("marketing")}
          >
            <div className="flex items-center">
              <div className="text-orange-500">
                <BarChartIcon className="h-5 w-5 mr-3" />
              </div>
              <span className="font-sm text-lg">Marketing</span>
            </div>
            <ChevronDown
              className={`h-4 w-4 text-orange-500 transition-transform ${
                isExpanded("marketing") ? "transform rotate-180" : ""
              }`}
            />
          </div>
          {isExpanded("marketing") && (
            <div className="ml-8 text-sm space-y-2 mt-1 mb-1">
              <NavLink
                 to="/admin/marketing/coupons"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Coupons
              </NavLink>
              <NavLink
                 to="/admin/marketing/email-marketing"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Email Marketing 
              </NavLink>
              <NavLink
                 to="/admin/marketing/sms-marketing"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
              SMS Marketing
              </NavLink>
              <NavLink
                 to="/admin/marketing/loyalty-offers"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Loyalty Offers
              </NavLink>
            </div>
          )}
        </div>

        <NavLink
          to="/admin/administration"
          className={({ isActive }) =>
            `group flex items-center p-2 rounded-lg transition-all duration-300 ${
              isActive
                ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
            }`
          }
        >
          {({ isActive }) => (
            <>
              <AdminPanelSettingsIcon
                className={`h-5 w-5 mr-3 transition-colors duration-300 ${
                  isActive
                    ? "text-white"
                    : "text-orange-500 group-hover:text-white"
                }`}
              />
              <span className="font-sm text-lg">Administration</span>
            </>
          )}
        </NavLink>

        <NavLink
          to="/admin/automated-payroll"
          className={({ isActive }) =>
            `group flex items-center p-2 rounded-lg transition-all duration-300 ${
              isActive
                ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
            }`
          }
        >
          {({ isActive }) => (
            <>
              <CreditCardIcon
                className={`h-5 w-5 mr-3 transition-colors duration-300 ${
                  isActive
                    ? "text-white"
                    : "text-orange-500 group-hover:text-white"
                }`}
              />
              <span className="font-sm text-lg">Automated Payroll</span>
            </>
          )}
        </NavLink>

        <div className="flex flex-col">
          <div
            className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-100 cursor-pointer text-gray-800"
            onClick={() => toggleExpand("employees")}
          >
            <div className="flex items-center">
              <div className="text-orange-500">
                <GroupsIcon className="h-5 w-5 mr-3" />
              </div>
              <span className="font-sm text-lg">Employees</span>
            </div>
            <ChevronDown
              className={`h-4 w-4 text-orange-500 transition-transform ${
                isExpanded("employees") ? "transform rotate-180" : ""
              }`}
            />
          </div>
          {isExpanded("employees") && (
            <div className="ml-8 text-sm space-y-2 mt-1 mb-1">
              <NavLink
                 to="/admin/employees/employee-list"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Employee List
              </NavLink>
              <NavLink
                 to="/admin/employees/salary-structure"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Salary Structure 
              </NavLink>
              <NavLink
                 to="/admin/employees/employee-deductions"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300 text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Employee Deductions 
              </NavLink>
            </div>
          )}
        </div>
        <div className="flex flex-col">
          <div
            className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-100 cursor-pointer text-gray-800"
            onClick={() => toggleExpand("customer")}
          >
            <div className="flex items-center">
              <div className="text-orange-500">
                <PeopleIcon className="h-5 w-5 mr-3" />
              </div>
              <span className="font-sm text-lg">Customers</span>
            </div>
            <ChevronDown
              className={`h-4 w-4 text-orange-500 transition-transform ${
                isExpanded("customer") ? "transform rotate-180" : ""
              }`}
            />
          </div>
          {isExpanded("customer") && (
            <div className="ml-8 text-sm space-y-2 mt-1 mb-1">
              <NavLink
                 to="/admin/customer/customer-list"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300  text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Customer List
              </NavLink>
              <NavLink
                 to="/admin/customer/gift-card"
                end={false}
                className={({ isActive }) =>
                  `block p-3 rounded-lg transition-all duration-300 text-md cursor-pointer ${
                    isActive
                      ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                      : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
                  }`
                }
              >
               Gift Cards
              </NavLink>
              
            </div>
          )}
        </div>

        <NavLink
          to="/admin/connect-stripe"
          className={({ isActive }) =>
            `group flex items-center p-2 rounded-lg transition-all duration-300 ${
              isActive
                ? "bg-gradient-to-r from-red-400 to-orange-400 text-white"
                : "hover:bg-gradient-to-r from-red-400 to-orange-400 hover:text-white text-gray-800"
            }`
          }
        >
          {({ isActive }) => (
            <>
              <AccountBalanceWalletIcon
                className={`h-5 w-5 mr-3 transition-colors duration-300 ${
                  isActive
                    ? "text-white"
                    : "text-orange-500 group-hover:text-white"
                }`}
              />
              <span className="font-sm text-lg">Connect Stripe</span>
            </>
          )}
        </NavLink>
      </div>
    </div>
  );
}