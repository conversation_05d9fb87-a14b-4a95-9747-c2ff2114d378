import React from 'react';

interface FormErrorMessageProps {
  children: React.ReactNode;
}

const FormErrorMessage: React.FC<FormErrorMessageProps> = ({ children }) => {
  return (
    <div className="flex items-center text-red-500 text-xs mt-1 ml-2 bg-red-50 px-2 py-1 rounded-md">
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        className="h-3 w-3 mr-1" 
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
        />
      </svg>
      {children}
    </div>
  );
};

export default FormErrorMessage;