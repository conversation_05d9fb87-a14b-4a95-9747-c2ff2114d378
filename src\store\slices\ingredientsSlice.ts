import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type {  Ingredient } from '../api/ingredientsApi';
import { ingredientsApi } from '../api/ingredientsApi';

interface IngredientsState {
  ingredients: Ingredient[];
  isLoading: boolean;
  error: string | null;
}

const initialState: IngredientsState = {
  ingredients: [],
  isLoading: false,
  error: null,
};

const ingredientsSlice = createSlice({
  name: 'ingredients',
  initialState,
  reducers: {
    setIngredients: (state, action: PayloadAction<Ingredient[]>) => {
      state.ingredients = action.payload;
    },
    clearIngredients: (state) => {
      state.ingredients = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET all ingredients
      .addMatcher(
        ingredientsApi.endpoints.getIngredients.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        ingredientsApi.endpoints.getIngredients.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.ingredients = payload;
        }
      )
      .addMatcher(
        ingredientsApi.endpoints.getIngredients.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch ingredients';
        }
      )

      // DELETE ingredient
      .addMatcher(
        ingredientsApi.endpoints.deleteIngredient.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        ingredientsApi.endpoints.deleteIngredient.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const ingredientId = action.meta.arg.originalArgs as string;
          state.ingredients = state.ingredients.filter(
            (ingredient) => ingredient.id !== ingredientId
          );
        }
      )
      .addMatcher(
        ingredientsApi.endpoints.deleteIngredient.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete ingredient';
        }
      )

      // POST ingredient
      .addMatcher(
        ingredientsApi.endpoints.postIngredient.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        ingredientsApi.endpoints.postIngredient.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          const newIngredient: Ingredient = {
            id: payload._id,
            IngredientID: payload.IngredientID,
            IngredientName: payload.IngredientName,
            UnitPrice: payload.UnitPrice,
            CurrentStock: payload.CurrentStock,
            UnitofMeasurement: payload.UnitofMeasurement,
            supplierId: payload.supplierId,
            CategoryType: payload.CategoryType,
            Expiry: payload.Expiry,
            ThresholdLevel: payload.ThresholdLevel,
            ShelfLife: payload.ShelfLife,
            StorageInstructions: payload.StorageInstructions,
            Alternative: payload.Alternative,
            NutritionalInformation: payload.NutritionalInformation,
            Notes: payload.Notes,
            Active: payload.Active,
            stockHistory: [],
            createdAt: ''
          };
          state.ingredients.push(newIngredient);
        }
      )
      .addMatcher(
        ingredientsApi.endpoints.postIngredient.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create ingredient';
        }
      )

      // PUT ingredient
      .addMatcher(
        ingredientsApi.endpoints.putIngredient.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        ingredientsApi.endpoints.putIngredient.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          const updated: Ingredient = {
            id: payload.data._id,
            IngredientID: payload.data.IngredientID,
            IngredientName: payload.data.IngredientName,
            UnitPrice: payload.data.UnitPrice,
            CurrentStock: payload.data.CurrentStock,
            UnitofMeasurement: payload.data.UnitofMeasurement,
            supplierId: payload.data.supplierId,
            CategoryType: payload.data.CategoryType,
            Expiry: payload.data.Expiry,
            ThresholdLevel: payload.data.ThresholdLevel,
            ShelfLife: payload.data.ShelfLife,
            StorageInstructions: payload.data.StorageInstructions,
            Alternative: payload.data.Alternative,
            NutritionalInformation: payload.data.NutritionalInformation,
            Notes: payload.data.Notes,
            Active: payload.data.Active,
            stockHistory: payload.data.stockHistory,
            createdAt: payload.data.createdAt,
          };
          state.ingredients = state.ingredients.map((ingredient) =>
            ingredient.id === updated.id ? updated : ingredient
          );
        }
      )
      .addMatcher(
        ingredientsApi.endpoints.putIngredient.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update ingredient';
        }
      );
  },
});

export const { setIngredients, clearIngredients } = ingredientsSlice.actions;

export const selectIngredients = (state: RootState) => state.ingredients.ingredients;
export const selectIngredientsLoading = (state: RootState) => state.ingredients.isLoading;
export const selectIngredientsError = (state: RootState) => state.ingredients.error;

export default ingredientsSlice.reducer;