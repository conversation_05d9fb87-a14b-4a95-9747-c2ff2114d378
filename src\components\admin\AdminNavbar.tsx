import { useState } from 'react';
import { Search, Bell, Settings, User2Icon } from 'lucide-react';
import { HiMenu } from "react-icons/hi";
import { useNavigate } from "react-router-dom";
import { clearCredentials } from '../../store/slices/authSlice';
import { useDispatch } from 'react-redux';
import { selectCurrentUser } from '../../store/slices/authSlice';
import { useSelector } from 'react-redux';

interface NavbarProps {
  toggleSidebar: () => void;
}

export default function Navbar({ toggleSidebar }: NavbarProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [isSearchOpen, setIsSearchOpen] = useState(false);
    const navigate = useNavigate();
    const dispatch = useDispatch();
  
    const handleSwitchToAdmin = () => {
      navigate("/pos/pos-dashboard");
    };
  
    const toggleMenu = () => {
      setIsOpen(!isOpen);
    };
  
    const toggleSearch = () => {
      setIsSearchOpen(!isSearchOpen);
    };

    const handleLogout = () => {
      dispatch(clearCredentials());
      localStorage.removeItem('token');
      localStorage.removeItem('userId');
  
      navigate('/auth');
    };

    const user = useSelector(selectCurrentUser);
    const username = user?.name || 'Guest';

  return (
    <div className="w-full sticky top-0 z-50">
      <nav className="bg-white relative border-b border-b-gray-200">
        <div className="max-w-full mx-auto px-4">
          <div className="flex justify-between items-center h-20">
      
            <div className="flex items-center">
              <HiMenu
                onClick={toggleSidebar}
                className="text-natural cursor-pointer"
                size={24}
              />
              <span className="font-bold text-xl ml-4 cursor-pointer">PatronWorks</span>
            </div>

            {/* Mobile user icon */}
            <div className="flex md:hidden items-center">
              <button
                onClick={toggleMenu}
                className="p-2 rounded-md hover:bg-gray-100"
              >
                <User2Icon className="h-5 w-5 cursor-pointer" />
              </button>
            </div>

            {/* Search box - only visible on desktop when toggled */}
            {isSearchOpen && (
              <div className="absolute left-1/2 transform -translate-x-1/2 top-1/2 -translate-y-1/2 w-full max-w-md px-4 z-10 hidden md:block">
                <div className="relative">
                  <input 
                    type="text"
                    placeholder="Quick Search"
                    className="w-[300px] py-2 pl-4 pr-10 border border-gray-300 rounded-lg bg-gray-50"
                    autoFocus
                  />
                </div>
              </div>
            )}

            {/* Desktop navigation */}
            <div className="hidden md:flex items-center space-x-4">
              <button
                onClick={toggleSearch} 
                className="p-2 rounded-md hover:bg-gray-100"
              >
                <Search className="h-5 w-5 cursor-pointer" />
              </button>
              <div className="relative">
                <button className="p-2 rounded-md hover:bg-gray-100">
                  <Bell className="h-5 w-5 cursor-pointer" />
                  <span className="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
                    18
                  </span>
                </button>
              </div>
              <button className="p-2 rounded-md hover:bg-gray-100">
                <Settings className="h-5 w-5 cursor-pointer" />
              </button>

              <button 
                className="bg-orange-50 text-orange-500 cursor-pointer px-2 py-1 text-sm font-medium rounded-full hover:bg-orange-100"
                onClick={handleSwitchToAdmin}
              >
              <div className="relative group inline-block">
  <button 
    className="bg-orange-50 text-orange-500 cursor-pointer px-2 py-1 text-sm font-medium rounded-full hover:bg-orange-100"
    onClick={handleSwitchToAdmin}
  >
    <h1 className='font-bold text-md'>Switch to POS</h1>
  </button>
  <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 hidden group-hover:block bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
    Switch to Point of Sale view
    <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-0 border-b-4 border-l-transparent border-r-transparent border-b-gray-800"></div>
  </div>
</div>
               
              </button>

              {/* User Profile */}
              <div className="flex items-center ml-3">
                <div className="h-8 w-8 rounded-full bg-gray-200"></div>
                <div className="ml-2">
                  <div className="text-sm font-medium">{username}</div>
                 <div className="relative group inline-block">
  <button 
    onClick={handleLogout} 
    className="cursor-pointer"
  >
    <div className="text-xs text-orange-500 hover:text-orange-700 transition-colors">Log out</div>
  </button>
  <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 hidden group-hover:block bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
    Sign out of your account
    <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-0 border-b-4 border-l-transparent border-r-transparent border-b-gray-800"></div>
  </div>
</div>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile menu - positioned on the right side */}
          {isOpen && (
            <div className="md:hidden bg-white pb-3 absolute right-0 top-20 w-64 shadow-lg rounded-bl-lg border-l border-b border-gray-200">
              <div className="flex flex-col space-y-2 p-3">
                <div className="flex items-center p-2 border-b border-gray-100 pb-4">
                  <div className="h-8 w-8 rounded-full bg-gray-200"></div>
                  <div className="ml-2">
                    <div className="text-sm font-medium">{username}</div>
                    <button onClick={handleLogout}>
                      <div className="text-xs text-orange-500">Log out</div>
                    </button>
                  </div>
                </div>
                <button className="flex items-center p-2 rounded-md hover:bg-gray-100">
                  <Bell className="h-5 w-5 mr-2" />
                  Notifications
                  <span className="ml-auto h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
                    18
                  </span>
                </button>
                <button className="flex items-center p-2 rounded-md hover:bg-gray-100">
                  <Settings className="h-5 w-5 mr-2" />
                  Settings
                </button>
                <button 
                  className="flex items-center justify-center bg-orange-50 text-orange-500 px-3 py-2 text-sm font-medium rounded-full hover:bg-orange-100"
                  onClick={handleSwitchToAdmin}
                >
                  Switch to POS
                </button>
              </div>
            </div>
          )}
        </div>
      </nav>
    </div>
  );
}