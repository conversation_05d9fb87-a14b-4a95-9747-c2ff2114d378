import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type {  Wastage } from '../api/wastagesApi';
import { wastageApi } from '../api/wastagesApi';

interface WastageState {
  wastages: Wastage[];
  isLoading: boolean;
  error: string | null;
}

const initialState: WastageState = {
  wastages: [],
  isLoading: false,
  error: null,
};

const wastageSlice = createSlice({
  name: 'wastage',
  initialState,
  reducers: {
    setWastages: (state, action: PayloadAction<Wastage[]>) => {
      state.wastages = action.payload;
    },
    clearWastages: (state) => {
      state.wastages = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // GET all wastages
      .addMatcher(
        wastageApi.endpoints.getWastages.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        wastageApi.endpoints.getWastages.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.wastages = payload;
        }
      )
      .addMatcher(
        wastageApi.endpoints.getWastages.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to fetch wastages';
        }
      )

      // DELETE wastage
      .addMatcher(
        wastageApi.endpoints.deleteWastage.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        wastageApi.endpoints.deleteWastage.matchFulfilled,
        (state, action) => {
          state.isLoading = false;
          const wastageId = action.meta.arg.originalArgs as string;
          state.wastages = state.wastages.filter(
            (wastage) => wastage.id !== wastageId
          );
        }
      )
      .addMatcher(
        wastageApi.endpoints.deleteWastage.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to delete wastage';
        }
      )

      // POST wastage
      .addMatcher(
        wastageApi.endpoints.postWastage.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        wastageApi.endpoints.postWastage.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          const newWastage: Wastage = {
            id: payload._id,
            IngredientName: payload.IngredientName,
            userId: payload.userId,
            Quantity: payload.Quantity,
            ReasonOfWastage: payload.ReasonOfWastage,
            PersonResponsible: payload.PersonResponsible,
            Cost: payload.Cost,
            Supplier: payload.Supplier,
            LocationOfWastage: payload.LocationOfWastage,
            DisposalPlan: payload.DisposalPlan,
            PreventiveMeasure: payload.PreventiveMeasure,
            createdAt: payload.createdAt,
            updatedAt: payload.updatedAt
          };
          state.wastages.push(newWastage);
        }
      )
      .addMatcher(
        wastageApi.endpoints.postWastage.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to create wastage';
        }
      )

      // UPDATE wastage
      .addMatcher(
        wastageApi.endpoints.putWastage.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        wastageApi.endpoints.putWastage.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          // The response structure might be different depending on your backend
          // Adjust this based on the actual response format
          const updated: Wastage = {
            id: payload.data._id,
            IngredientName: payload.data.IngredientName,
            userId: payload.data.userId,
            Quantity: payload.data.Quantity,
            ReasonOfWastage: payload.data.ReasonOfWastage,
            PersonResponsible: payload.data.PersonResponsible,
            Cost: payload.data.Cost,
            Supplier: payload.data.Supplier,
            LocationOfWastage: payload.data.LocationOfWastage,
            DisposalPlan: payload.data.DisposalPlan,
            PreventiveMeasure: payload.data.PreventiveMeasure,
            createdAt: payload.data.createdAt,
            updatedAt: payload.data.updatedAt
          };
          state.wastages = state.wastages.map((wastage) =>
            wastage.id === updated.id ? updated : wastage
          );
        }
      )
      .addMatcher(
        wastageApi.endpoints.putWastage.matchRejected,
        (state, { error }) => {
          state.isLoading = false;
          state.error = error.message || 'Failed to update wastage';
        }
      );
  },
});

export const { setWastages, clearWastages } = wastageSlice.actions;

export const selectWastages = (state: RootState) => state.wastage.wastages;
export const selectWastageLoading = (state: RootState) => state.wastage.isLoading;
export const selectWastageError = (state: RootState) => state.wastage.error;

export default wastageSlice.reducer;