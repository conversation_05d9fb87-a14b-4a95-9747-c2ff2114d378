import React, { useRef } from 'react';
import { IoMdClose } from "react-icons/io";

interface StandaloneModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  width?: string;
}

const StandaloneModal: React.FC<StandaloneModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  width = "max-w-3xl"
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  if (!isOpen) return null;

  // Function to handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Only close if clicking directly on the backdrop (not on modal content)
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // We don't need to stop propagation anymore

  return (
    <div
      className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
      onClick={handleBackdropClick}
      style={{ zIndex: 10000 }} // Extremely high z-index
    >
      <div
        ref={modalRef}
        className={`bg-white rounded-xl shadow-lg w-full ${width} mx-4 flex flex-col max-h-[90vh] relative`}
      >
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-bold">{title}</h2>
          <button
            onClick={onClose}
            className="cursor-pointer rounded-full p-1 hover:bg-gray-100 transition-colors"
          >
            <IoMdClose className="w-6 h-6 text-gray-700" />
          </button>
        </div>

        <div className="flex-1 overflow-y-auto">
          {children}
        </div>

        {footer && (
          <div className="px-6 py-4 border-t border-gray-200">
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default StandaloneModal;
