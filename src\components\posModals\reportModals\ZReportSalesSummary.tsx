import React, { useState, useEffect } from 'react';
import CustomModal from '../../CustomModal';
import { IoReceiptOutline } from 'react-icons/io5';
import { useGetPaymentsQuery } from '../../../store/api/pos/orderapi';
import { useGetEmployeesQuery } from '../../../store/api/pos/customer';
import ZReportSearchResults from './ZReportSearchResults';
// import ZReportSearchResults from './ZReportSearchResults';

interface ZReportSalesSummaryProps {
  isOpen: boolean;
  onClose: () => void;
}

const ZReportSalesSummary: React.FC<ZReportSalesSummaryProps> = ({ isOpen, onClose }) => {
  const userId = localStorage.getItem("userId") || ""
  const { data, isLoading, error } = useGetPaymentsQuery(userId)
  const { data: employeeData, isLoading: isLoadingEmployees, error: employeeError } = useGetEmployeesQuery(userId)

  const [timeRange, setTimeRange] = useState('Today');
  const [employee, setEmployee] = useState('all'); // Default to 'all' for all employees
  const [_employeeId, setEmployeeId] = useState(''); // Store the selected employee ID
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [showOnlyTotals, setShowOnlyTotals] = useState(false);
  const [showProducts, setShowProducts] = useState(false);
  const [selectedPaymentMethods, setSelectedPaymentMethods] = useState<string[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchResultParams, setSearchResultParams] = useState<any>(null);
  // Flag to track if custom date range is selected
  const [isCustomDateRange, setIsCustomDateRange] = useState(false);

  // Generate payment methods from API data
  const paymentMethods = React.useMemo(() => {
    if (!data || !Array.isArray(data)) {
      return [];
    }

    return data.map(payment => ({
      id: payment._id,
      label: payment.name,
      isActive: payment.isActive,
      isDefault: payment.defaultPayment
    }));
  }, [data]);

  // Initialize selected payment methods with active payment methods
  useEffect(() => {
    if (data && Array.isArray(data)) {
      // Select all active payment methods by default
      const activePaymentMethodIds = data
        .filter(payment => payment.isActive)
        .map(payment => payment._id);

      setSelectedPaymentMethods(activePaymentMethodIds);
    }
  }, [data]);

  // Reset selected payment methods when "Show Only Totals" changes
  useEffect(() => {
    if (!showOnlyTotals) {
      // Clear selected payment methods when "Show Only Totals" is unchecked
      setSelectedPaymentMethods([]);
    } else if (data && Array.isArray(data)) {
      // Re-select active payment methods when "Show Only Totals" is checked
      const activePaymentMethodIds = data
        .filter(payment => payment.isActive)
        .map(payment => payment._id);

      setSelectedPaymentMethods(activePaymentMethodIds);
    }
  }, [showOnlyTotals, data]);

  const handleSearch = () => {
    // Get the selected employee details if an employee is selected
    let selectedEmployeeDetails = null;
    if (employee !== 'all' && employeeData) {
      const selectedEmployee = employeeData.find(emp => emp._id === employee);
      if (selectedEmployee) {
        selectedEmployeeDetails = {
          id: selectedEmployee._id,
          name: `${selectedEmployee.firstName} ${selectedEmployee.lastName}`,
          employeeId: selectedEmployee.employeeId,
          role: selectedEmployee.role,
          employeeType: selectedEmployee.employeeType
        };
      }
    }

    // If Show Only Totals is checked but no payment methods are selected, select all active payment methods
    let selectedPaymentMethodsToUse = selectedPaymentMethods;
    if (showOnlyTotals && selectedPaymentMethods.length === 0 && data && Array.isArray(data)) {
      selectedPaymentMethodsToUse = data
        .filter(payment => payment.isActive)
        .map(payment => payment._id);
    }

    // Make sure isCustomDateRange is set if we have dates and custom timeRange
    if (startDate && endDate && timeRange.toLowerCase() === 'custom') {
      setIsCustomDateRange(true);
    }

    // Prepare search parameters
    const searchParams = {
      timeRange,
      employee: employee === 'all' ? 'Admin' : selectedEmployeeDetails?.name || '',
      employeeId: employee === 'all' ? null : employee,
      employeeDetails: selectedEmployeeDetails,
      dateRange: startDate && endDate ? { startDate, endDate } : null,
      timeSelection: startTime && endTime ? { startTime, endTime } : null,
      showOnlyTotals,
      showProducts,
      paymentMethods: selectedPaymentMethodsToUse.length > 0
        ? paymentMethods.filter(method => selectedPaymentMethodsToUse.includes(method.id))
        : [],
      isCustomDateRange
    };

    console.log('Search with parameters:', searchParams);

    // Set the search parameters and show the search results modal
    setSearchResultParams(searchParams);
    setShowSearchResults(true);
  };

  // Handle closing the search results modal
  const handleCloseSearchResults = () => {
    setShowSearchResults(false);
  };

  return (
    <>
      <CustomModal
        isOpen={isOpen}
        onClose={onClose}
        title="Z Report-Sales Summary"
        width="max-w-6xl"
      >
        <div className="p-6">
          {/* Top Filters */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex gap-4">
              <select
                value={timeRange}
                onChange={(e) => {
                  setTimeRange(e.target.value);
                  // Set isCustomDateRange flag if 'custom' is selected
                  setIsCustomDateRange(e.target.value.toLowerCase() === 'custom');
                }}
                className="border border-orange text-orange rounded-full px-4 py-2"
              >
                <option>Today</option>
                <option>Yesterday</option>
                <option>This Week</option>
                <option>This Month</option>
                <option>custom</option>
              </select>
              <select
                value={employee}
                onChange={(e) => {
                  const selectedValue = e.target.value;
                  setEmployee(selectedValue);
                  // If "all" is selected, clear the employeeId
                  if (selectedValue === 'all') {
                    setEmployeeId('');
                  } else {
                    // Find the selected employee and set the employeeId
                    const selectedEmployee = employeeData?.find(emp => emp._id === selectedValue);
                    if (selectedEmployee) {
                      setEmployeeId(selectedEmployee._id);
                    }
                  }
                }}
                className="border border-orange text-orange rounded-full px-4 py-2"
              >
                <option value="all">All Employees</option>
                {isLoadingEmployees ? (
                  <option disabled>Loading employees...</option>
                ) : employeeError ? (
                  <option disabled>Error loading employees</option>
                ) : employeeData && employeeData.length > 0 ? (
                  employeeData.map((emp) => (
                    <option key={emp._id} value={emp._id}>
                      {emp.firstName} {emp.lastName}
                    </option>
                  ))
                ) : (
                  <option disabled>No employees found</option>
                )}
              </select>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex gap-2 items-center">
                <input
                  type="date"
                  className="border border-gray-300 rounded-md p-2"
                  value={startDate}
                  onChange={(e) => {
                    setStartDate(e.target.value);
                    // If both dates are set and timeRange is custom, set isCustomDateRange flag
                    if (e.target.value && endDate && timeRange.toLowerCase() === 'custom') {
                      setIsCustomDateRange(true);
                    }
                  }}
                  placeholder="Start date"
                />
                <span className="text-gray-400">→</span>
                <input
                  type="date"
                  className="border border-gray-300 rounded-md p-2"
                  value={endDate}
                  onChange={(e) => {
                    setEndDate(e.target.value);
                    // If both dates are set and timeRange is custom, set isCustomDateRange flag
                    if (startDate && e.target.value && timeRange.toLowerCase() === 'custom') {
                      setIsCustomDateRange(true);
                    }
                  }}
                  placeholder="End date"
                />
              </div>
              <div className="flex gap-2 items-center">
                <input
                  type="time"
                  className="border border-gray-300 rounded-md p-2"
                  value={startTime}
                  onChange={(e) => setStartTime(e.target.value)}
                  placeholder="Start Time"
                />
                <span className="text-gray-400">→</span>
                <input
                  type="time"
                  className="border border-gray-300 rounded-md p-2"
                  value={endTime}
                  onChange={(e) => setEndTime(e.target.value)}
                  placeholder="End Time"
                />
              </div>
            </div>
          </div>

          {/* Show Only Totals Toggle */}
          <div className="flex items-center justify-between p-4 border border-[#E4E4E4] rounded-lg mb-4 bg-gray-50">
            <div className="flex items-center gap-2">
              <IoReceiptOutline className="text-gray-500" size={20} />
              <span className="text-gray-500">Show Only Totals</span>
            </div>
            <input
              type="checkbox"
              checked={showOnlyTotals}
              onChange={(e) => setShowOnlyTotals(e.target.checked)}
              className="w-5 h-5 rounded-full"
            />
          </div>

          {/* Payment Methods Grid */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            {isLoading ? (
              // Loading state
              <div className="col-span-2 flex justify-center items-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange"></div>
                <span className="ml-2 text-gray-600">Loading payment methods...</span>
              </div>
            ) : error ? (
              // Error state
              <div className="col-span-2 p-4 bg-red-50 border border-red-200 rounded-lg text-red-600">
                <p>Error loading payment methods. Please try again later.</p>
              </div>
            ) : paymentMethods.length === 0 ? (
              // Empty state
              <div className="col-span-2 p-4 bg-gray-50 border border-gray-200 rounded-lg text-gray-600 text-center">
                <p>No payment methods found.</p>
              </div>
            ) : (
              // Render payment methods
              paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className={`flex items-center justify-between p-4 border rounded-lg ${!showOnlyTotals
                    ? 'border-gray-200 bg-gray-100 opacity-50 cursor-not-allowed'
                    : method.isActive
                      ? 'border-[#E4E4E4] bg-gray-50'
                      : 'border-gray-200 bg-gray-100 opacity-70'
                    }`}
                >
                  <div className="flex items-center gap-2">
                    <IoReceiptOutline className="text-gray-500" size={20} />
                    <div>
                      <span className="text-gray-700">{method.label}</span>
                      {method.isDefault && (
                        <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Default</span>
                      )}
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    className="w-5 h-5 rounded-full"
                    disabled={!method.isActive || !showOnlyTotals}
                    checked={selectedPaymentMethods.includes(method.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedPaymentMethods(prev => [...prev, method.id]);
                      } else {
                        setSelectedPaymentMethods(prev => prev.filter(id => id !== method.id));
                      }
                    }}
                  />
                </div>
              ))
            )}
          </div>

          {/* Show Products Toggle */}
          <div className="flex items-center justify-between p-4 border border-[#E4E4E4] rounded-lg mb-6 bg-gray-50">
            <div className="flex items-center gap-2">
              <IoReceiptOutline className="text-gray-500" size={20} />
              <span className="text-gray-500">Show Products</span>
            </div>
            <input
              type="checkbox"
              checked={showProducts}
              onChange={(e) => setShowProducts(e.target.checked)}
              className="w-5 h-5 rounded-full"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4 font-bold">
            <button
              onClick={handleSearch}
              className="px-14 py-3 bg-orange text-white rounded-full cursor-pointer "
            >
              Search
            </button>
            <button
              onClick={onClose}
              className="px-14 py-3 border border-orange text-orange rounded-full cursor-pointer"
            >
              Cancel
            </button>
          </div>
        </div>
      </CustomModal>

      {/* Search Results Modal */}
      {showSearchResults && searchResultParams && (
        <ZReportSearchResults
          isOpen={showSearchResults}
          onClose={handleCloseSearchResults}
          searchParams={searchResultParams}
        />
      )}
    </>
  );
};

export default ZReportSalesSummary;