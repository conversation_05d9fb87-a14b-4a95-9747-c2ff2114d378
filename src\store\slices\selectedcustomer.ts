import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';

export interface CustomerLoyalty {
    timeStamp: string;
    CardNo: string;
    ActivateCard: boolean;
    ExpiresIn: string;
    StartDate: string;
    Points: number;
    Visits: number;
}

export interface SelectedCustomer {
    _id: string;
    CustomerId: string;
    FirstName: string;
    LastName: string;
    Email: string;
    Phone: string;
    Address: string;
    City: string;
    State: string;
    isActive: boolean;
    userId: string;
    CardNo: string;
    LastVisit: string;
    creditLimits: number;
    timeStamp: string;
    __v: number;
    CustomerLoyalty?: CustomerLoyalty;
}

interface SelectedCustomerState {
    selectedCustomer: SelectedCustomer | null;
}

const initialState: SelectedCustomerState = {
    selectedCustomer: null,
};

const selectedCustomerSlice = createSlice({
    name: 'selectedCustomer',
    initialState,
    reducers: {
        setSelectedCustomer: (state, action: PayloadAction<SelectedCustomer>) => {
            state.selectedCustomer = action.payload;
        },
        clearSelectedCustomer: (state) => {
            state.selectedCustomer = null;
        },
    },
});

export const { setSelectedCustomer, clearSelectedCustomer } = selectedCustomerSlice.actions;

export const selectSelectedCustomer = (state: RootState) => state.selectedCustomer.selectedCustomer;

export default selectedCustomerSlice.reducer;
