import React from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { useLoginMutation } from '../store/api/userApi';

type LoginFormInputs = {
  email: string;
  password: string;
};

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const [login, { isLoading }] = useLoginMutation();
  const [loginError, setLoginError] = React.useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormInputs>();

  const onSubmit = async (data: LoginFormInputs) => {
    setLoginError(null);
    try {

      const response = await login(data).unwrap();


      console.log('Login successful:', response);
      navigate('/admin/dashboard');
    } catch (err: any) {
      console.error('Login failed:', err);
      setLoginError(err?.data?.message || 'Login failed. Please check your credentials.');
    }
  };

  // const handleSignUp = () => navigate('/create-account');
  const handleForgotPassword = () => navigate('/forgot-password');

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      <div className="mt-8">
        <h1 className="text-4xl font-bold text-center">
          Patron<span className="text-orange-500">Works</span>
        </h1>
      </div>

      <div className="flex-grow flex justify-center items-start pt-8">
        <div className="bg-white p-8 rounded-lg shadow-sm w-full max-w-md">
          <h2 className="text-2xl font-bold mb-2">Login to your Account</h2>
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Email */}
            <div className="mb-4">
              <label htmlFor="email" className="block text-gray-600 mb-1">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </div>
                <input
                  id="email"
                  type="email"
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /\S+@\S+\.\S+/,
                      message: 'Email is invalid',
                    },
                  })}
                  className="pl-10 w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter Email Address"
                />
              </div>
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
            </div>

            {/* Password */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-1">
                <label htmlFor="password" className="block text-gray-600">
                  Password
                </label>
                <span
                  className="text-orange-500 text-sm cursor-pointer"
                  onClick={handleForgotPassword}
                >
                  Forgot password?
                </span>
              </div>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <input
                  id="password"
                  type="password"
                  {...register('password', {
                    required: 'Password is required',
                  })}
                  className="pl-10 w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Enter Password"
                />
              </div>
              {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
            </div>

            {loginError && (
              <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
                {loginError}
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className={`w-full bg-orange-500 text-white py-3 rounded font-medium ${
                isLoading ? 'opacity-70 cursor-not-allowed' : 'hover:bg-orange-600 transition-colors'
              }`}
            >
              {isLoading ? 'Signing in...' : 'Sign in'}
            </button>
          </form>

          {/* <div className="text-center mt-6">
            <p className="text-gray-600">
              Don't have an Account?
              <span
                className="text-orange-500 ml-1 font-medium cursor-pointer"
                onClick={handleSignUp}
              >
                Sign up
              </span>
            </p>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
