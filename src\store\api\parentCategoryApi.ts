import { baseApi } from './baseApi.ts';

export interface ParentCategory {
    id: string;
    name: string;
    subCategoriesCount: number;
    parent_pic?: string;
  }


export const productParentCategoryApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getParentCategories: builder.query<ParentCategory[], string>({
      query: (userId) => `/parentcategory?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          name: item.name,
          subCategoriesCount: 0,
           parent_pic: item.parent_pic,
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'ParentCategory' as const, id })),
              { type: 'ParentCategory', id: 'LIST' },
            ]
          : [{ type: 'ParentCategory', id: 'LIST' }],
    }),


    deleteParentCategory: builder.mutation<void, string>({
      query: (id) => ({
        url: `/parentcategory/${id}`,
        method: 'DELETE',
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(productParentCategoryApi.util.invalidateTags([{ type: 'ParentCategory', id }]));
        } catch (error) {
          console.error('Failed to delete category', error);
        }
      },
    }),

    postParentCategory: builder.mutation<any, FormData>({
      query: (originalFormData) => {
        const userId = localStorage.getItem('userId');
        if (userId) {
          originalFormData.append('userId', userId);
        }
        return {
          url: '/parentcategory',
          method: 'POST',
          body: originalFormData,
        };
      },
      invalidatesTags: [{ type: 'ParentCategory', id: 'LIST' }],
    }),

    putParentCategory: builder.mutation<any, { id: string; formData: FormData }>({
      query: ({ id, formData }) => ({
        url: `/parentcategory/${id}`,
        method: 'PUT',
        body: formData,
      }),
      invalidatesTags: [{ type: 'ParentCategory' }],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetParentCategoriesQuery,
  useDeleteParentCategoryMutation,
  usePostParentCategoryMutation,
  usePutParentCategoryMutation,
} = productParentCategoryApi;
