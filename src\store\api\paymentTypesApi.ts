import { baseApi } from "./baseApi";

export interface PaymentTypes {
  id: string;
  name: string;
  paymentsGTypeId: string;
  isActive: string;
  defaultPayment: string;
  showCaption: string;
  userId: string;
}

export const paymentTypesApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getPaymentTypes: builder.query<PaymentTypes[], string>({
      query: (userId) => `/payment?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          name: item.name,
          paymentsGTypeId: item.paymentsGTypeId,
          isActive: item.isActive,
          defaultPayment: item.defaultPayment,
          showCaption: item.showCaption,
          userId: item.userId
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "PaymentTypes" as const, id })),
              { type: "PaymentTypes", id: "LIST" },
            ]
          : [{ type: "PaymentTypes", id: "LIST" }],
    }),

    getPaymentType: builder.query<PaymentTypes, string>({
      query: (id) => `/payment/${id}`,
      transformResponse: (response: any) => ({
        id: response._id,
        name: response.name,
        paymentsGTypeId: response.paymentsGTypeId,
        isActive: response.isActive,
        defaultPayment: response.defaultPayment,
        showCaption: response.showCaption,
        userId: response.userId
      }),
      providesTags: (_result, _error, id) => [{ type: "PaymentTypes", id }],
    }),

    deletePaymentType: builder.mutation<void, string>({
      query: (id) => ({
        url: `/payment/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            paymentTypesApi.util.invalidateTags([
              { type: "PaymentTypes", id },
              { type: "PaymentTypes", id: "LIST" }
            ])
          );
        } catch (error) {
          console.error("Failed to delete payment type", error);
        }
      },
    }),

    postPaymentType: builder.mutation<any, any>({
      query: (data) => {
        console.log("Sending POST request with data:", data);
        return {
          url: "/payment",
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: [{ type: "PaymentTypes", id: "LIST" }],
    }),

    putPaymentType: builder.mutation<
      any,
      { id: string; formData: any }
    >({
      query: ({ id, formData }) => ({
        url: `/payment/${id}`,
        method: "PUT",
        body: {
          ...formData,
        },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "PaymentTypes", id },
        { type: "PaymentTypes", id: "LIST" },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetPaymentTypesQuery,
  useGetPaymentTypeQuery,
  useDeletePaymentTypeMutation,
  usePostPaymentTypeMutation,
  usePutPaymentTypeMutation,
} = paymentTypesApi;