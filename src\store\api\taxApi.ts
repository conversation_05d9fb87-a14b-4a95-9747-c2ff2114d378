import { baseApi } from "./baseApi";

export interface Tax {
  id: string;
  name: string;
  taxValue: string;
  active: string;
  byDefault: string;
  userId: string;
}

export const taxApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getTaxs: builder.query<Tax[], string>({
      query: (userId) => `/Tax?userId=${userId}`,
      transformResponse: (response: any[]) => {
        return response.map((item) => ({
          id: item._id,
          name: item.name,
          taxValue: item.taxValue,
          active: item.active,
          byDefault: item.byDefault,
          userId: item.userId
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "Tax" as const, id })),
              { type: "Tax", id: "LIST" },
            ]
          : [{ type: "Tax", id: "LIST" }],
    }),

    getTax: builder.query<Tax, string>({
      query: (id) => `/Tax/${id}`,
      transformResponse: (response: any) => ({
        id: response._id,
        name: response.name,
        taxValue: response.taxValue,
        active: response.active,
        byDefault: response.byDefault,
        userId: response.userId
      }),
      providesTags: (_result, _error, id) => [{ type: "Tax", id }],
    }),

    deleteTax: builder.mutation<void, string>({
      query: (id) => ({
        url: `/Tax/${id}`,
        method: "DELETE",
      }),
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          dispatch(
            taxApi.util.invalidateTags([
              { type: "Tax", id },
              { type: "Tax", id: "LIST" }
            ])
          );
        } catch (error) {
          console.error("Failed to delete Tax", error);
        }
      },
    }),

    postTax: builder.mutation<any, any>({
      query: (data) => {
        console.log("Sending POST request with data:", data);
        return {
          url: "/Tax",
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: [{ type: "Tax", id: "LIST" }],
    }),

    putTax: builder.mutation<
      any,
      { id: string; formData: any }
    >({
      query: ({ id, formData }) => ({
        url: `/Tax/${id}`,
        method: "PUT",
        body: {
          ...formData,
        },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "Tax", id },
        { type: "Tax", id: "LIST" },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetTaxsQuery,
  useGetTaxQuery,
  useDeleteTaxMutation,
  usePostTaxMutation,
  usePutTaxMutation,
} = taxApi;