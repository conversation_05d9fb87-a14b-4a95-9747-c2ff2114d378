import React, { useState, useEffect } from "react";
import { NavLink, useLocation } from "react-router-dom";
import clsx from "clsx";
import {
  AiFillHome,
  AiOutlineHome,
  AiFillClockCircle,
  AiOutlineClockCircle,
  AiFillShopping,
} from "react-icons/ai";
import { LuBookAudio } from "react-icons/lu";
import { IoMenuOutline } from "react-icons/io5";
import { MdOutlineStoreMallDirectory, MdStoreMallDirectory } from "react-icons/md";
import { useNavigate } from "react-router-dom";
import RunningOrdersModal from "./table/RunningOrdersModal";


type Props = {
  toggleSidebar?: () => void;
};

const Navbar: React.FC<Props> = ({ toggleSidebar }) => {
  const location = useLocation();
  const currentPath = location.pathname;
  const navigate = useNavigate();
  const [isRunningOrdersModalOpen, setIsRunningOrdersModalOpen] = useState(false);
  const [currentDateTime, setCurrentDateTime] = useState(new Date());

  // Update the date and time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDateTime(new Date());
    }, 1000);

    // Clean up the interval on component unmount
    return () => clearInterval(timer);
  }, []);

  // Format the date and time
  const formattedTime = currentDateTime.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true  // Changed to true to show AM/PM
  });

  const formattedDate = currentDateTime.toLocaleDateString('en-US', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });

  const handleSwitchToAdmin = () => {
    navigate("/admin/dashboard");
  };

  const handleRunningOrdersClick = (e: React.MouseEvent) => {
    e.preventDefault();
    navigate("/tables", { state: { openRunningOrdersModal: true } });
  };

  const handleReservationsClick = (e: React.MouseEvent) => {
    e.preventDefault();
    navigate("/tables", { state: { openReservationModal: true } });
  };

  const navItems = [
    {
      label: "Home",
      to: "/pos/pos-dashboard",
      match: ["/pos/pos-dashboard"],
      icon: AiOutlineHome,
      iconActive: AiFillHome,
      onClick: undefined,
    },
    {
      label: "All Tables",
      to: "/tables",
      match: ["/tables"],
      icon: MdOutlineStoreMallDirectory,
      iconActive: MdStoreMallDirectory,
      onClick: undefined,
    },
    {
      label: "Reservations",
      to: "/tables",
      match: ["/reservations"],
      icon: AiOutlineClockCircle,
      iconActive: AiFillClockCircle,
      onClick: handleReservationsClick,
    },
    {
      label: "Running Orders",
      to: "/tables",
      match: ["/running-orders"],
      icon: LuBookAudio,
      iconActive: AiFillShopping,
      onClick: handleRunningOrdersClick,
    },
  ];

  return (
    <>
      <div className="fixed top-0  left-0 w-full h-18 bg-white flex items-center justify-between px-4 sm:px-6 border z-50 border-[#E4E4E4]">
        {/* Left: Logo & Menu */}
        <div className="flex items-center gap-3">
          <IoMenuOutline
            onClick={toggleSidebar}
            className="text-natural cursor-pointer"
            size={28}
          />
          <h1 className="text-lg font-semibold text-black">Patronworks</h1>
        </div>

        <div className="flex items-center gap-4 md:gap-6">
          {navItems.map(
            ({ to, label, match, icon: Icon, iconActive: IconActive, onClick }) => {
              const isActive = match.includes(currentPath);
              return (
                <NavLink
                  key={label}
                  to={to}
                  className={clsx(
                    "flex items-center gap-1 text-[16px] md:text-base  transition",
                    isActive ? "text-orange" : "text-natural"
                  )}
                  onClick={onClick}
                >
                  <div
                    className={clsx(
                      "flex items-center gap-2 px-2 py-1 rounded-md transition",
                      isActive
                    )}
                  >
                    {isActive ? <IconActive size={23} /> : <Icon size={23} />}
                    <span className="hidden xl:inline">{label}</span>
                  </div>
                </NavLink>
              );
            }
          )}
        </div>
        <div className="flex items-center gap-3 sm:gap-4">
          <button
            className="text-orange px-3 py-2 rounded-full text-sm font-semibold bg-orange-100 transition whitespace-nowrap cursor-pointer"
            onClick={handleSwitchToAdmin}
          >
            Switch to Admin
          </button>
          <div className="hidden lg:block bg-gray-100 px-6 py-2 rounded-full text-sm whitespace-nowrap">
            {formattedTime} &nbsp; {formattedDate}
          </div>
          <img
            src="https://i.pravatar.cc/40"
            className="w-8 h-8 rounded-full"
            alt="User"
          />
        </div>
      </div>

      {/* Add the RunningOrdersModal */}
      <RunningOrdersModal
        isOpen={isRunningOrdersModalOpen}
        onClose={() => setIsRunningOrdersModalOpen(false)}
      />
    </>
  );
};

export default Navbar;
