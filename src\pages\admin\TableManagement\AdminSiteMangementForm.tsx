import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  usePostSiteMutation,
  usePutSiteMutation,
  useGetSiteQuery,
} from '../../../store/api/siteManagementApi';
import Swal from 'sweetalert2';

const AdminSiteForm = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  
  const userId = localStorage.getItem('userId') || '';

  const [postSite, { isLoading: isCreating }] = usePostSiteMutation();
  const [putSite, { isLoading: isUpdating }] = usePutSiteMutation();
  
  const { data: siteData, isLoading: isLoadingSite } = useGetSiteQuery(id || '', {
    skip: !isEditMode,
  });

  const [formData, setFormData] = useState({
    siteName: '',
    noOfTables: '',
    shortDescription: '',
    isActive: false,
    imageUrl: '',
  });
  
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isCompressing, setIsCompressing] = useState(false); // Added compression state

  // Image compression function
  const compressImage = (file: File, maxWidth: number = 800, maxHeight: number = 800, quality: number = 0.8): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }
        
        canvas.width = width;
        canvas.height = height;
        
        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              resolve(file); // Fallback to original file
            }
          },
          file.type,
          quality
        );
      };
      
      img.src = URL.createObjectURL(file);
    });
  };

  // Load existing data when in edit mode
  useEffect(() => {
    if (isEditMode && siteData) {
      setFormData({
        siteName: siteData.siteName || '',
        noOfTables: siteData.numberOfTables?.toString() || '',
        shortDescription: siteData.briefDescription || '',
        isActive: siteData.isActive || false,
        imageUrl: siteData.siteImage || '',
      });
      
      // Set image preview for existing image
      if (siteData.siteImage) {
        setImagePreview(siteData.siteImage);
      }
    }
  }, [siteData, isEditMode]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    
    // Clear image preview if imageUrl is cleared
    if (name === 'imageUrl' && value === '') {
      setImagePreview(null);
    }
    // Set image preview if imageUrl is provided
    if (name === 'imageUrl' && value) {
      setImagePreview(value);
      // Clear selected file if URL is provided
      setSelectedFile(null);
    }
  };

  const handleToggleChange = () => {
    setFormData({ ...formData, isActive: !formData.isActive });
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Check file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        Swal.fire({
          title: 'Invalid File Type',
          text: 'Please select a valid image file (JPG, PNG, GIF, SVG)',
          icon: 'error',
          confirmButtonColor: '#ea580c',
        });
        return;
      }

      setIsCompressing(true);
      
      try {
        let processedFile = file;
        
        // Compress image if it's not SVG and larger than 500KB
        if (file.type !== 'image/svg+xml' && file.size > 500 * 1024) {
          processedFile = await compressImage(file);
          
          // Show compression result
          const originalSizeMB = (file.size / (1024 * 1024)).toFixed(2);
          const compressedSizeMB = (processedFile.size / (1024 * 1024)).toFixed(2);
          
          console.log(`Image compressed: ${originalSizeMB}MB → ${compressedSizeMB}MB`);
        }
        
        setSelectedFile(processedFile);
        
        // Create image preview
        const reader = new FileReader();
        reader.onloadend = () => {
          setImagePreview(reader.result as string);
        };
        reader.readAsDataURL(processedFile);
        
        // Clear image URL when file is selected
        setFormData(prev => ({ ...prev, imageUrl: '' }));
        
      } catch (error) {
        console.error('Error processing image:', error);
        Swal.fire({
          title: 'Error',
          text: 'Failed to process image. Please try again.',
          icon: 'error',
          confirmButtonColor: '#ea580c',
        });
      } finally {
        setIsCompressing(false);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Validate form data
      if (!formData.siteName.trim()) {
        Swal.fire({
          title: 'Form Error',
          text: 'Please enter site name',
          icon: 'error',
          confirmButtonColor: '#ea580c',
        });
        return;
      }
      
      if (!formData.noOfTables.trim()) {
        Swal.fire({
          title: 'Form Error',
          text: 'Please enter number of tables',
          icon: 'error',
          confirmButtonColor: '#ea580c',
        });
        return;
      }

      // Check if number of tables is valid
      const tableCount = parseInt(formData.noOfTables);
      if (isNaN(tableCount) || tableCount <= 0) {
        Swal.fire({
          title: 'Form Error',
          text: 'Please enter a valid number of tables',
          icon: 'error',
          confirmButtonColor: '#ea580c',
        });
        return;
      }

      // Prepare form data for API
      const submitFormData = new FormData();
      submitFormData.append('siteName', formData.siteName);
      submitFormData.append('numberOfTables', formData.noOfTables);
      submitFormData.append('briefDescription', formData.shortDescription);
      submitFormData.append('isActive', formData.isActive.toString());
      submitFormData.append('userId', userId);
      
      // Handle image - either file upload or URL
      if (selectedFile) {
        submitFormData.append('siteImage', selectedFile);
      } else if (formData.imageUrl) {
        submitFormData.append('siteImage', formData.imageUrl);
      } else {
        Swal.fire({
          title: 'Form Error',
          text: 'Please upload an image or provide an image URL',
          icon: 'error',
          confirmButtonColor: '#ea580c',
        });
        return;
      }

      // Show loading state
      Swal.fire({
        title: isEditMode ? 'Updating Site...' : 'Creating Site...',
        html: 'Please wait...',
        allowOutsideClick: false,
        allowEscapeKey: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });

      if (isEditMode && id) {
        await putSite({ id, data: submitFormData }).unwrap();
        Swal.fire({
          title: 'Success!',
          text: 'Site updated successfully!',
          icon: 'success',
          confirmButtonColor: '#ea580c',
        }).then(() => {
          navigate('/admin/tables-management/site-management');
        });
      } else {
        await postSite(submitFormData).unwrap();
        Swal.fire({
          title: 'Success!',
          text: 'Site added successfully!',
          icon: 'success',
          confirmButtonColor: '#ea580c',
        }).then(() => {
          navigate('/admin/tables-management/site-management');
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      Swal.fire({
        title: 'Error!',
        text: `Failed to ${isEditMode ? 'update' : 'add'} site. Please try again.`,
        icon: 'error',
        confirmButtonColor: '#ea580c',
      });
    }
  };

  const handleCancel = () => {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You will lose any unsaved changes',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#ea580c',
      cancelButtonColor: '#d1d5db',
      confirmButtonText: 'Yes, leave page',
      cancelButtonText: 'No, stay here'
    }).then((result) => {
      if (result.isConfirmed) {
        navigate('/admin/tables-management/site-management');
      }
    });
  };

  if (isEditMode && isLoadingSite) {
    return (
      <div className="flex justify-center items-start bg-white h-screen pt-[35vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading site data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg shadow-sm p-2 bg-gray-50">
      {/* Header */}
      <div className="flex items-center p-2 rounded-xl bg-white border border-gray-200 mb-2">
        <button 
          className="flex items-center text-gray-800 text-xl font-medium cursor-pointer"
          onClick={handleCancel}
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          {isEditMode ? 'Edit Site' : 'Create Site'}
        </button>
      </div>

      <form onSubmit={handleSubmit} className='border border-gray-200 rounded-2xl bg-white'>
        {/* Site Details Section */}
        <div className="bg-orange-50 px-4 py-3 rounded-t-2xl">
          <h2 className="text-base font-medium text-gray-800">Site Details</h2>
        </div>
        
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Site Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Site Name
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                name="siteName"
                placeholder="Enter Site Name"
                value={formData.siteName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
              />
            </div>
            
            {/* No Of Tables */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                No Of Tables
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                name="noOfTables"
                placeholder="Enter Number of Tables"
                value={formData.noOfTables}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
              />
            </div>
          </div>
          
          {/* Short Description */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Short Description
            </label>
            <textarea
              name="shortDescription"
              placeholder="Enter Description"
              value={formData.shortDescription}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              rows={3}
            />
          </div>
        </div>
        
        {/* Permissions Section */}
        <div className="bg-orange-50 px-4 py-3">
          <h2 className="text-base font-medium text-gray-800">Permissions</h2>
        </div>
        
        <div className="p-4">
          <div className="flex items-center">
            <div className="relative inline-block w-10 mr-2 align-middle">
              <input
                type="checkbox"
                id="activeToggle"
                className="opacity-0 w-0 h-0"
                checked={formData.isActive}
                onChange={handleToggleChange}
              />
              <label
                htmlFor="activeToggle"
                className={`block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer ${
                  formData.isActive ? 'bg-orange-500' : ''
                }`}
              >
                <span
                  className={`block h-6 w-6 rounded-full bg-white transform transition-transform duration-200 ease-in ${
                    formData.isActive ? 'translate-x-4' : 'translate-x-0'
                  }`}
                ></span>
              </label>
            </div>
            <label htmlFor="activeToggle" className="text-sm font-medium text-gray-700 mt-6">
              Active
            </label>
          </div>
        </div>
        
        {/* Site Image Section */}
        <div className="bg-orange-50 px-4 py-3">
          <h2 className="text-base font-medium text-gray-800">Site Image</h2>
        </div>
        
        <div className="p-4">
          <div className="flex items-start mb-4">
            <div className="flex-shrink-0">
              <div className="w-16 h-16 border border-gray-200 bg-gray-100 rounded-full flex items-center justify-center text-orange-500 overflow-hidden">
                {imagePreview ? (
                  <img 
                    src={imagePreview} 
                    alt="Preview" 
                    className="w-full h-full object-cover rounded-full" 
                  />
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                )}
              </div>
            </div>
            
            <div className="ml-4">
              <div>
                <label className="cursor-pointer">
                  <span className={`text-sm font-medium ${isCompressing ? 'text-gray-400' : 'text-blue-500'}`}>
                    {isCompressing ? 'Compressing...' : 'Upload'}
                  </span>
                  <input 
                    type="file"
                    className="hidden"
                    accept=".svg,.png,.jpg,.jpeg,.gif"
                    onChange={handleFileUpload}
                    disabled={isCompressing}
                  />
                </label>
                <span className="ml-2 text-sm text-gray-500">Site Image</span>
              </div>
              <p className="text-xs text-gray-500">
                SVG, PNG, JPG or GIF (max. 800 x 800px)
              </p>
              {isCompressing && (
                <p className="mt-1 text-xs text-blue-600">Compressing image...</p>
              )}
              {selectedFile && !isCompressing && (
                <div className="mt-1">
                  <p className="text-xs text-gray-700">Selected: {selectedFile.name}</p>
                  <p className="text-xs text-green-600">
                    Size: {(selectedFile.size / (1024 * 1024)).toFixed(2)}MB
                  </p>
                </div>
              )}
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Or Enter Image URL
            </label>
            <input
              type="url"
              name="imageUrl"
              placeholder="Add Image URL"
              value={formData.imageUrl}
              onChange={handleInputChange}
              disabled={!!selectedFile || isCompressing}
              className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                (selectedFile || isCompressing) ? 'bg-gray-100' : ''
              }`}
            />
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 p-4 border-t border-gray-100">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 border border-orange-500 text-orange-500 rounded-lg cursor-pointer hover:bg-orange-50 focus:outline-none"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isCreating || isUpdating || isCompressing}
            className={`px-6 py-2 ${
              (isCreating || isUpdating || isCompressing) ? 'bg-orange-300' : 'bg-orange-500 hover:bg-orange-600'
            } text-white rounded-lg cursor-pointer focus:outline-none`}
          >
            {isCompressing ? 'Processing...' :
             isEditMode ? 
              (isUpdating ? 'Updating...' : 'Update Site') : 
              (isCreating ? 'Adding...' : 'Add Site')
            }
          </button>
        </div>
      </form>
    </div>
  );
};

export default AdminSiteForm;