import React, { useState, useEffect } from "react";
import CustomModal from "../../CustomModal";
import { IoSearchOutline } from "react-icons/io5";
import { FiEdit } from "react-icons/fi";
import { useGetCustomersQuery } from "../../../store/api/customerApi";
import CustomerBalanceEditModal from "./CustomerBalanceEditModal";

interface BalanceAdjustmentProps {
  isOpen: boolean;
  onClose: () => void;
}

interface CustomerLoyalty {
  CardNo: string;
  Points: number;
  creditLimits: number;
  LastVisit: string;
  ExpiresIn: string;
  StartDate: string;
  ActivateCard: boolean;
}

// Define a type that maps API customer to the expected format
interface LocalCustomer {
  _id: string;
  FirstName: string;
  LastName: string;
  CustomerId: string;
  CustomerLoyalty?: CustomerLoyalty;
  Email: string;
  Phone: string;
  Address: string;
  City: string;
  State: string;
  isActive: boolean;
  userId: string;
}

const BalanceAdjustment: React.FC<BalanceAdjustmentProps> = ({
  isOpen,
  onClose,
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [filteredCustomers, setFilteredCustomers] = useState<LocalCustomer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<LocalCustomer | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [_activatedCardCount, setActivatedCardCount] = useState<number>(0);
  const itemsPerPage = 5;

  // No need to pass userId as it's handled inside the hook
  const { data, isLoading, error, refetch } = useGetCustomersQuery();
  console.log("customer data is", data)
  // Filter and paginate customers when data or search term changes
  useEffect(() => {
    if (data) {

      // Map API customers to local format with correct property names
      const mappedCustomers = data.map((customer: any) => ({
        _id: customer._id,
        FirstName: customer.FirstName,
        LastName: customer.LastName,
        CustomerId: customer.CustomerId,
        CustomerLoyalty: customer.CustomerLoyalty,
        Email: customer.Email,
        Phone: customer.Phone,
        Address: customer.Address,
        City: customer.City,
        State: customer.State,
        isActive: customer.isActive,
        userId: customer.userId
      }));

      // First filter by ActivateCard status
      const activatedCardCustomers = mappedCustomers.filter((customer) =>
        customer.CustomerLoyalty?.ActivateCard === true
      );

      // Update the count of customers with activated cards
      setActivatedCardCount(activatedCardCustomers.length);
      console.log("Customers with activated cards:", activatedCardCustomers.length);

      // Then filter by search term
      const filtered = activatedCardCustomers.filter((customer) => {
        const fullName = `${customer.FirstName || ""} ${customer.LastName || ""}`.toLowerCase();
        const cardNo = customer.CustomerLoyalty?.CardNo || "";
        return fullName.includes(searchTerm.toLowerCase()) ||
          cardNo.includes(searchTerm.toLowerCase());
      });

      setFilteredCustomers(filtered);
      // Reset to first page when filter changes
      setCurrentPage(1);
    }
  }, [data, searchTerm]);

  // Calculate total pages
  const totalPages = Math.ceil((filteredCustomers?.length || 0) / itemsPerPage);

  // Get current page items
  const currentCustomers = filteredCustomers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePrevPage = (): void => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = (): void => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    setSearchTerm(e.target.value);
  };

  const handleEditClick = (customer: LocalCustomer): void => {
    setSelectedCustomer(customer);
    setIsEditModalOpen(true);
  };

  const handleEditModalClose = (): void => {
    setIsEditModalOpen(false);
    setSelectedCustomer(null);
  };

  // Function to handle successful update
  const handleUpdateSuccess = (): void => {
    // Refetch the data to update the UI
    refetch();
  };

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage === 1 ? 'text-gray-400' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={handlePrevPage}
          disabled={currentPage === 1}
          type="button"
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage === totalPages || totalPages === 0 ? 'text-gray-400' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={handleNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
          type="button"
        >
          Next →
        </button>
      </div>
      <button
        onClick={onClose}
        className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
        type="button"
      >
        Cancel
      </button>
    </div>
  );

  // Function to safely format numbers
  const formatNumber = (value: number | undefined | null): string => {
    if (value === undefined || value === null) return "0.00";
    return value.toFixed(2);
  };

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Balance Adjustment"
      width="max-w-4xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search Bar */}
        <div className="relative mb-6">
          <IoSearchOutline
            size={25}
            className="absolute font-bold left-3 top-2/5 transform -translate-y-1/2 text-black"
          />
          <input
            type="text"
            placeholder="Search Customer"
            value={searchTerm}
            onChange={handleSearchChange}
            className="pl-10 pr-4 py-2 w-full pb-5 border-b border-[#E4E4E4] rounded-md focus:outline-none"
          />

        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
              <div className="ml-3 text-orange-500 font-medium">
                Loading...
              </div>
            </div>
          ) : error ? (
            <div className="text-center py-4 text-red-500">
              Error loading customers. Please try again.
            </div>
          ) : (
            <table className="w-full">
              <thead>
                <tr className="text-left border-b border-gray-200">
                  <th className="pb-3 text-gray-500 font-normal">Card</th>
                  <th className="pb-3 text-gray-500 font-normal">Person</th>
                  <th className="pb-3 text-gray-500 font-normal">Points</th>
                  <th className="pb-3 text-gray-500 font-normal">Balance</th>
                  <th className="pb-3 text-gray-500 font-normal">Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentCustomers && currentCustomers.length > 0 ? (
                  currentCustomers.map((customer) => (
                    <tr key={customer._id} className="border-b border-gray-200">
                      <td className="py-4">{customer.CustomerLoyalty?.CardNo || "No Card"}</td>
                      <td className="py-4">{`${customer.FirstName || ""} ${customer.LastName || ""}`}</td>
                      <td className="py-4">{formatNumber(customer.CustomerLoyalty?.Points)}</td>
                      <td className="py-4">{formatNumber(customer.CustomerLoyalty?.creditLimits)}</td>
                      <td className="py-4">
                        <button
                          className="flex items-center gap-2 cursor-pointer text-orange-500 border border-orange-500 rounded-full px-6 py-1 hover:bg-orange-50"
                          type="button"
                          onClick={() => handleEditClick(customer)}
                        >
                          <FiEdit />
                          Edit
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="py-4 text-center">
                      No customers with activated loyalty cards found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* Customer Balance Edit Modal */}
      <CustomerBalanceEditModal
        isOpen={isEditModalOpen}
        onClose={handleEditModalClose}
        customer={selectedCustomer}
        onUpdateSuccess={handleUpdateSuccess}
      />
    </CustomModal>
  );
};

export default BalanceAdjustment;