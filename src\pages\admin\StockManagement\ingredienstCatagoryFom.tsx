import React, { useState, useEffect } from 'react';
import { ChevronLeft } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  usePostIngredientsCategoryMutation,
  usePutIngredientsCategoryMutation,
  useGetIngredientsCategoriesQuery,
} from '../../../store/api/ingredientsCategoryApi';
import Swal from 'sweetalert2';

interface CategoryFormData {
  name: string;
}

const IngredientCategoryForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const userId = localStorage.getItem('userId') || '';

  const { data: categories = [] } = useGetIngredientsCategoriesQuery(userId);
  const category = categories.find((cat) => cat.id === id);

  const [postIngredientsCategory, { isLoading: isCreating }] = usePostIngredientsCategoryMutation();
  const [putIngredientsCategory, { isLoading: isUpdating }] = usePutIngredientsCategoryMutation();

  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
  });

  useEffect(() => {
    if (isEditMode && category) {
      setFormData({
        name: category.name,
      });
    }
  }, [isEditMode, category]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (isEditMode && id) {
        // Update existing category
        await putIngredientsCategory({
          id,
          formData: {
            _id: id, 
            name: formData.name,
            userId: userId
          }
        }).unwrap();
        
        Swal.fire({
          title: 'Success!',
          text: 'Ingredient Category updated successfully!',
          icon: 'success',
          confirmButtonColor: '#f97316', // orange-500
        });
      } else {
        await postIngredientsCategory({
          name: formData.name,
          userId: userId,
        }).unwrap();
        
        Swal.fire({
          title: 'Success!',
          text: 'Ingredient Category created successfully!',
          icon: 'success',
          confirmButtonColor: '#f97316', // orange-500
        });
      }
      navigate('/admin/stock-management/ingredients-category');
    } catch (error) {
      console.error(isEditMode ? 'Error updating category:' : 'Error creating category:', error);
      
      Swal.fire({
        title: 'Error!',
        text: isEditMode ? 'Failed to update Ingredient Category.' : 'Failed to create Ingredient Category.',
        icon: 'error',
        confirmButtonColor: '#f97316', // orange-500
      });
    }
  };

  const handleCancel = () => {
    navigate('/admin/stock-management/ingredients-category');
  };

  const isLoading = isCreating || isUpdating;

  return (
    <div className="rounded w-full p-3 bg-gray-50">
      <div className="p-4 flex items-center border border-gray-100 rounded-2xl bg-white mb-4">
        <button
          type="button"
          className="flex items-center text-gray-800 font-medium"
          onClick={handleCancel}
        >
          <ChevronLeft size={20} className="mr-1" />
          <h1 className='text-xl'>{isEditMode ? 'Edit' : 'Add'} Ingredient Category</h1>
        </button>
      </div>

      <form onSubmit={handleSubmit} className='bg-white border border-gray-200 rounded-2xl'>
        <div className="border border-gray-100 rounded-2xl bg-white">
          <h2 className="text-base bg-orange-50 p-3 rounded-t-2xl font-medium text-gray-800 mb-4">
            Ingredient Category Details
          </h2>
          <div className="space-y-4">
            <div className='w-full p-4'>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                name="name"
                placeholder="Enter Category Name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                required
              />
            </div>
          </div>

          <div className="p-4 flex justify-end space-x-2">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-300 rounded text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className={`px-6 py-2 rounded text-white text-sm font-medium focus:outline-none ${
                isLoading ? 'bg-orange-300 cursor-not-allowed' : 'bg-orange-500 hover:bg-orange-600'
              }`}
            >
              {isLoading
                ? isEditMode ? 'Updating...' : 'Adding...'
                : isEditMode ? 'Update Ingredient Category' : 'Add Ingredient Category'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default IngredientCategoryForm;