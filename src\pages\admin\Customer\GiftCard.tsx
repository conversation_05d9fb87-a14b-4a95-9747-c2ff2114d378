import React, { useState, useEffect } from 'react';
import { Search, Edit, ChevronLeft, ChevronRight, PlusCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useGetCustomersQuery } from "../../../store/api/customerApi";
import Swal from 'sweetalert2';
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";


interface GiftCard {
  customerId: string;
  _id: string; 
  cardNumber: string;
  customerName: string;
  credit: number;
  debit: number;
  balance: number;
  status: 'Active' | 'Inactive';
  expiryDate?: string | Date;
  startDate?: string | Date;
}

export default function GiftCard() {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [giftCards, setGiftCards] = useState<GiftCard[]>([]);
  const [showErrorScreen, setShowErrorScreen] = useState(false);
  const navigate = useNavigate();

  // Fetch customers with loyalty cards
  const { data: customersData, isLoading, isError } = useGetCustomersQuery();

  // Show error alert when there's an error
  useEffect(() => {
    if (isError && !showErrorScreen) {
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Error loading gift cards!',
        confirmButtonText: 'OK',
        confirmButtonColor: '#f97316', // Orange color to match your theme
      }).then(() => {
        setShowErrorScreen(true);
      });
    }
  }, [isError, showErrorScreen]);

  useEffect(() => {
    if (customersData) {
      // Transform API data into gift card format
      const cards = customersData
        .filter(customer => customer.CustomerLoyalty?.CardNo)
        .map(customer => {
          const status: 'Active' | 'Inactive' = customer.CustomerLoyalty?.ActivateCard ? 'Active' : 'Inactive';
          
          return {
            customerId: customer.CustomerId,
            _id: customer._id,
            cardNumber: customer.CustomerLoyalty?.CardNo || '',
            customerName: `${customer.FirstName} ${customer.LastName}`,
            credit: customer.CustomerLoyalty?.creditLimits || 0,
            debit: 0,
            balance: customer.CustomerLoyalty?.creditLimits || 0,
            status, // Now properly typed
            expiryDate: customer.CustomerLoyalty?.ExpiresIn,
            startDate: customer.CustomerLoyalty?.StartDate
          };
        });
      
      setGiftCards(cards);
    }
  }, [customersData]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const filteredCards = giftCards.filter(card => 
    card.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    card.cardNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    card.customerId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const itemsPerPage = 5;
  const totalPages = Math.ceil(filteredCards.length / itemsPerPage);
  const paginatedCards = filteredCards.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleIssueGiftCard = () => {
    navigate('/admin/customer/gift-card/add-gift-form');
  };

  const formatDate = (dateString?: string | Date) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  if (isLoading) {
    return <div className="flex justify-center items-start bg-white h-screen pt-[40vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading Gift Cards...</p>
        </div>
      </div>
  }

  if (isError && showErrorScreen) {
    return <div className="flex justify-center items-center h-screen text-red-500">Error loading gift cards</div>;
  }

  return (
    <div className="bg-gray-50 p-6 rounded-lg">
      <div className="flex items-center justify-between md:flex-row flex-col mb-6 bg-white rounded-2xl p-4 border border-gray-200 text-gray-800">
        <h1 className="text-3xl font-bold">Gift Card</h1>
        <div className="flex items-center md:flex-row flex-col-reverse gap-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Gift Cards"
              className="pl-3 pr-10 py-2 border border-gray-200 rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchTerm}
              onChange={handleSearch}
            />
            <Search className="absolute right-3 top-2.5 text-gray-400 h-5 w-5" />
          </div>
          <button 
            onClick={handleIssueGiftCard}
            className="bg-orange-500 cursor-pointer text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-orange-600 transition-colors"
          >
            Issue Gift Card
            <PlusCircle size={20} />
          </button>
        </div>
      </div>

      <div className="overflow-x-auto bg-white-50 border border-gray-200 rounded-2xl">
        {filteredCards.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            No gift cards found. Click "Issue Gift Card" to create one.
          </div>
        ) : (
          <table className="min-w-full table-fixed">
            <thead>
              <tr className="text-left border-b border-b-gray-50 bg-orange-50">
                <th className="px-6 py-4 text-sm font-semibold text-gray-700 w-24 whitespace-nowrap">Customer ID</th>
                <th className="px-6 py-4 text-sm font-semibold text-gray-700 w-32 whitespace-nowrap">Card Number</th>
                <th className="px-6 py-4 text-sm font-semibold text-gray-700 w-40 whitespace-nowrap">Customer Name</th>
                <th className="px-6 py-4 text-sm font-semibold text-gray-700 w-20 whitespace-nowrap">Credit</th>
                <th className="px-6 py-4 text-sm font-semibold text-gray-700 w-20 whitespace-nowrap">Debit</th>
                <th className="px-6 py-4 text-sm font-semibold text-gray-700 w-20 whitespace-nowrap">Balance</th>
                <th className="px-6 py-4 text-sm font-semibold text-gray-700 w-24 whitespace-nowrap">Start Date</th>
                <th className="px-6 py-4 text-sm font-semibold text-gray-700 w-24 whitespace-nowrap">Expiry Date</th>
                <th className="px-6 py-4 text-sm font-semibold text-gray-700 w-20 whitespace-nowrap">Status</th>
                <th className="px-6 py-4 text-sm font-semibold text-gray-700 w-20 whitespace-nowrap">Actions</th>
              </tr>
            </thead>
            <tbody>
              {paginatedCards.map((card, index) => (
                <tr key={index} className="border-b border-gray-50 bg-white hover:bg-orange-50">
                  <td className="px-6 py-4 text-sm text-gray-700 truncate">{card.customerId}</td>
                  <td className="px-6 py-4 text-sm text-gray-700 truncate">{card.cardNumber}</td>
                  <td className="px-6 py-4 text-sm text-gray-700 truncate">{card.customerName}</td>
                  <td className="px-6 py-4 text-sm text-gray-700">${card.credit.toFixed(2)}</td>
                  <td className="px-6 py-4 text-sm text-gray-700">${card.debit.toFixed(2)}</td>
                  <td className="px-6 py-4 text-sm text-gray-700">${card.balance.toFixed(2)}</td>
                  <td className="px-6 py-4 text-sm text-gray-700">{formatDate(card.startDate)}</td>
                  <td className="px-6 py-4 text-sm text-gray-700">{formatDate(card.expiryDate)}</td>
                  <td className="px-6 py-4 text-sm">
                    <span className={`px-3 py-1 rounded-lg text-sm ${
                      card.status === 'Active' 
                        ? 'bg-green-50 text-green-500 px-4 border border-green-300' 
                        : 'bg-orange-50 text-orange-500 border border-orange-300'
                    }`}>
                      {card.status}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-2">
                      <button 
                        className="text-blue-500 hover:text-blue-700"
                        onClick={() => navigate(`/admin/customer/gift-card/add-gift-form/${card._id}`)}
                      >
                        <Edit
                          id="edit-icon"
                          data-tooltip-id="edit-tooltip"
                          data-tooltip-content="Edit"
                          size={20}
                          className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                        />
                        <Tooltip
                          id="edit-tooltip"
                          place="bottom"
                          className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                        />

                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {filteredCards.length > 0 && (
        <div className="flex items-center justify-start mt-4 gap-2">
          <button
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
            className={`flex items-center justify-center w-8 h-8 rounded-md border ${
              currentPage === 1
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <ChevronLeft size={16} />
          </button>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
            <button
              key={number}
              onClick={() => setCurrentPage(number)}
              className={`w-8 h-8 rounded-md flex items-center justify-center border ${
                currentPage === number
                  ? 'bg-orange-500 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              {number}
            </button>
          ))}

          <button
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`flex items-center justify-center w-8 h-8 rounded-md border ${
              currentPage === totalPages
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <ChevronRight size={16} />
          </button>
        </div>
      )}
    </div>
  );
}