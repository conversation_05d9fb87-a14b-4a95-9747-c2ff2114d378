import { useState, useEffect, type ChangeEvent, type FormEvent } from "react";
import { ChevronLeft, ChevronDown, X, Search } from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import {
  usePostWastageMutation,
  useGetWastageQuery,
  useGetWastagesQuery,
} from '../../../store/api/wastagesApi';
import { useGetIngredientsQuery } from '../../../store/api/ingredientsApi';
import { useGetSuppliersQuery } from '../../../store/api/supplierApi';
import Swal from "sweetalert2";

interface Ingredient {
  _id?: string;
  id?: string;
  IngredientName: string;
  UnitPrice?: number;
}

interface Supplier {
  _id?: string;
  id?: string;
  SupplierName: string;
  name?: string; 
}

interface WastageFormData {
  IngredientName: string;
  Quantity: string;
  ReasonOfWastage: string;
  PersonResponsible: string;
  Cost: string;
  Supplier: string;
  LocationOfWastage: string;
  DisposalPlan: string;
  PreventiveMeasure: string;
  userId: string;
}

interface WastageApiResponse extends Omit<WastageFormData, 'IngredientName' | 'Supplier'> {
  IngredientName?: string | Ingredient;
  Supplier?: string | Supplier;
  _id?: string;
}

export default function AddWastage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const userId = localStorage.getItem('userId') || '';
  
  const { data: wastage } = useGetWastageQuery(id || '', { skip: !isEditMode });
  const { data: _ } = useGetWastagesQuery(userId);
  const [postWastage, { isLoading: isCreating }] = usePostWastageMutation();
  
  const { data: ingredients = [] } = useGetIngredientsQuery(userId);
  const { data: suppliers = [] } = useGetSuppliersQuery(userId);

  const [showSupplierDropdown, setShowSupplierDropdown] = useState<boolean>(false);
  const [showIngredientDropdown, setShowIngredientDropdown] = useState<boolean>(false);

  const [ingredientSearch, setIngredientSearch] = useState<string>('');
  const [supplierSearch, setSupplierSearch] = useState<string>('');

  const [formData, setFormData] = useState<WastageFormData>({
    IngredientName: "", 
    Quantity: "",
    ReasonOfWastage: "",
    PersonResponsible: "",
    Cost: "0",
    Supplier: "", 
    LocationOfWastage: "",
    DisposalPlan: "",
    PreventiveMeasure: "",
    userId: userId
  });

  const [selectedIngredientName, setSelectedIngredientName] = useState<string>("Select Ingredient");
  const [selectedSupplierName, setSelectedSupplierName] = useState<string>("Select Supplier");

  useEffect(() => {
    if (isEditMode && wastage) {
      const wastageData = wastage as unknown as WastageApiResponse;
      setFormData({
        ...wastageData,
        IngredientName: typeof wastageData.IngredientName === 'object' && wastageData.IngredientName?._id
          ? wastageData.IngredientName._id
          : (wastageData.IngredientName as string) || "",
        Supplier: typeof wastageData.Supplier === 'object' && wastageData.Supplier?._id
          ? wastageData.Supplier._id
          : (wastageData.Supplier as string) || "",
        userId: userId
      });

      if (wastageData.IngredientName && typeof wastageData.IngredientName === 'object') {
        setSelectedIngredientName((wastageData.IngredientName as Ingredient).IngredientName || "Select Ingredient");
      }
      
      if (wastageData.Supplier && typeof wastageData.Supplier === 'object') {
        setSelectedSupplierName(
          (wastageData.Supplier as Supplier).SupplierName || 
          (wastageData.Supplier as Supplier).name || 
          "Select Supplier"
        );
      }
    }
  }, [isEditMode, wastage, userId]);

  const filteredIngredients = (ingredients as Ingredient[])
    .filter(ingredient => 
      ingredient.IngredientName.toLowerCase().includes(ingredientSearch.toLowerCase())
    );

  const filteredSuppliers = (suppliers as Supplier[])
    .filter(supplier => 
      supplier.SupplierName.toLowerCase().includes(supplierSearch.toLowerCase())
    );

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSelectIngredient = (ingredient: Ingredient) => {
    setFormData({
      ...formData,
      IngredientName: ingredient._id || ingredient.id || "",
      Cost: ingredient.UnitPrice ? (ingredient.UnitPrice * (Number(formData.Quantity) || 1)).toString() : "0"
    });
    setSelectedIngredientName(ingredient.IngredientName);
    setShowIngredientDropdown(false);
  };

  const handleSelectSupplier = (supplier: Supplier) => {
    setFormData({
      ...formData,
      Supplier: supplier._id || supplier.id || ""
    });
    setSelectedSupplierName(supplier.SupplierName);
    setShowSupplierDropdown(false);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.IngredientName) {
      await Swal.fire({
        title: "Validation Error",
        text: "Please select an ingredient.",
        icon: "warning",
        confirmButtonColor: "#ea580c"
      });
      return;
    }

    if (!formData.Quantity || Number(formData.Quantity) <= 0) {
      await Swal.fire({
        title: "Validation Error",
        text: "Please enter a valid quantity.",
        icon: "warning",
        confirmButtonColor: "#ea580c"
      });
      return;
    }

    if (!formData.Supplier) {
      await Swal.fire({
        title: "Validation Error",
        text: "Please select a supplier.",
        icon: "warning",
        confirmButtonColor: "#ea580c"
      });
      return;
    }
    
    try {
      const submissionData = {
        ...formData,
        Quantity: Number(formData.Quantity),
        Cost: Number(formData.Cost),
      };

      await postWastage(submissionData).unwrap();
      
      await Swal.fire({
        title: "Success!",
        text: `Wastage ${isEditMode ? 'updated' : 'recorded'} successfully!`,
        icon: "success",
        confirmButtonColor: "#ea580c"
      });

      navigate('/admin/stock-management/wastages');
    } catch (error: any) {
      console.error('Error creating wastage record:', error);
      
      // Extract error message if available
      let errorMessage = "Failed to record wastage.";
      if (error?.data?.message) {
        errorMessage = error.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }
      
      await Swal.fire({
        title: "Error!",
        text: errorMessage,
        icon: "error",
        confirmButtonColor: "#ea580c"
      });
    }
  };

  const handleCancel = async () => {
    // Check if form has been modified
    const hasChanges = 
      formData.IngredientName !== "" ||
      formData.Quantity !== "" ||
      formData.ReasonOfWastage !== "" ||
      formData.PersonResponsible !== "" ||
      formData.Cost !== "0" ||
      formData.Supplier !== "" ||
      formData.LocationOfWastage !== "" ||
      formData.DisposalPlan !== "" ||
      formData.PreventiveMeasure !== "";

    if (hasChanges) {
      const result = await Swal.fire({
        title: "Are you sure?",
        text: "You have unsaved changes. Do you want to leave without saving?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#ea580c",
        cancelButtonColor: "#6b7280",
        confirmButtonText: "Yes, leave",
        cancelButtonText: "No, stay"
      });

      if (result.isConfirmed) {
        navigate('/admin/stock-management/wastages');
      }
    } else {
      navigate('/admin/stock-management/wastages');
    }
  };

  // Calculate cost automatically when quantity changes
  useEffect(() => {
    // Find the selected ingredient
    const selectedIngredient = (ingredients as Ingredient[]).find(
      i => i._id === formData.IngredientName || i.id === formData.IngredientName
    );
    
    if (selectedIngredient && formData.Quantity) {
      setFormData(prev => ({
        ...prev,
        Cost: ((selectedIngredient.UnitPrice ?? 0) * Number(formData.Quantity)).toString()
      }));
    }
  }, [formData.Quantity, formData.IngredientName, ingredients]);

  return (
    <div className="p-4 bg-gray-50 min-h-screen">
      {/* Header with back button */}
      <div className="mb-4 bg-white p-4 rounded-lg shadow-sm">
        <button 
          onClick={handleCancel}
          className="flex items-center text-lg font-semibold text-gray-800"
        >
          <ChevronLeft size={20} />
          {isEditMode ? "Edit Wastage" : "Add Wastage"}
        </button>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Wastage Details Section */}
        <div className="mb-6 bg-white rounded-2xl border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800 bg-orange-50 p-3 mb-4 rounded-t-2xl">Wastage Details</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-3">
            {/* Ingredient Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ingredient Name
                <span className="text-red-500 ml-1">*</span>
              </label>
              <div className="relative">
                <div 
                  className="w-full p-2 border border-gray-300 rounded-md flex justify-between items-center cursor-pointer"
                  onClick={() => setShowIngredientDropdown(!showIngredientDropdown)}
                >
                  <span className={selectedIngredientName !== "Select Ingredient" ? "text-gray-900" : "text-gray-500"}>
                    {selectedIngredientName}
                  </span>
                  <ChevronDown size={16} className="text-gray-400" />
                </div>
                
                {showIngredientDropdown && (
                  <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg">
                    <div className="p-2 bg-gray-100 flex items-center">
                      <Search className="w-4 h-4 text-gray-400 mr-2" />
                      <input 
                        type="text" 
                        placeholder="Search ingredient" 
                        className="w-full text-sm outline-none"
                        value={ingredientSearch}
                        onChange={(e) => setIngredientSearch(e.target.value)}
                      />
                      <X 
                        className="w-4 h-4 text-gray-400 cursor-pointer" 
                        onClick={() => setShowIngredientDropdown(false)}
                      />
                    </div>
                    <div className="max-h-40 overflow-y-auto">
                      {filteredIngredients.length > 0 ? (
                        filteredIngredients.map((ingredient) => (
                          <div 
                            key={ingredient._id || ingredient.id}
                            className="p-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => handleSelectIngredient(ingredient)}
                          >
                            {ingredient.IngredientName}
                          </div>
                        ))
                      ) : (
                        <div className="p-2 text-gray-500">No ingredients found</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            {/* Quantity */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Quantity
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="number"
                name="Quantity"
                placeholder="Enter Quantity"
                value={formData.Quantity}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
                min="0"
              />
            </div>
            
            {/* Reason of Wastage */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reason of Wastage
              </label>
              <input
                type="text"
                name="ReasonOfWastage"
                placeholder="Enter Reason of Wastage"
                value={formData.ReasonOfWastage}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>
            
            {/* Person Responsible */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Person Responsible
              </label>
              <input
                type="text"
                name="PersonResponsible"
                placeholder="Enter Person Responsible"
                value={formData.PersonResponsible}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>
            
            {/* Cost */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Cost (Exact or Approximate)
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="number"
                name="Cost"
                placeholder="0"
                value={formData.Cost}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
                min="0"
                step="0.01"
              />
            </div>
            
            {/* Supplier */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Supplier
                <span className="text-red-500 ml-1">*</span>
              </label>
              <div className="relative">
                <div 
                  className="w-full p-2 border border-gray-300 rounded-md flex justify-between items-center cursor-pointer"
                  onClick={() => setShowSupplierDropdown(!showSupplierDropdown)}
                >
                  <span className={selectedSupplierName !== "Select Supplier" ? "text-gray-900" : "text-gray-500"}>
                    {selectedSupplierName}
                  </span>
                  <ChevronDown size={16} className="text-gray-400" />
                </div>
                
                {showSupplierDropdown && (
                  <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg">
                    <div className="p-2 bg-gray-100 flex items-center">
                      <Search className="w-4 h-4 text-gray-400 mr-2" />
                      <input 
                        type="text" 
                        placeholder="Search supplier" 
                        className="w-full text-sm outline-none"
                        value={supplierSearch}
                        onChange={(e) => setSupplierSearch(e.target.value)}
                      />
                      <X 
                        className="w-4 h-4 text-gray-400 cursor-pointer" 
                        onClick={() => setShowSupplierDropdown(false)}
                      />
                    </div>
                    <div className="max-h-40 overflow-y-auto">
                      {filteredSuppliers.length > 0 ? (
                        filteredSuppliers.map((supplier) => (
                          <div 
                            key={supplier._id || supplier.id}
                            className="p-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => handleSelectSupplier(supplier)}
                          >
                            {supplier.SupplierName}
                          </div>
                        ))
                      ) : (
                        <div className="p-2 text-gray-500">No suppliers found</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            {/* Location of Wastage */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Location of Wastage
              </label>
              <input
                type="text"
                name="LocationOfWastage"
                placeholder="Enter Location of Wastage"
                value={formData.LocationOfWastage}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>
            
            {/* Disposal Plan */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Disposal Plan
              </label>
              <input
                type="text"
                name="DisposalPlan"
                placeholder="Enter Disposal Plan"
                value={formData.DisposalPlan}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>
          </div>
          
          {/* Preventive Measures - Full Width */}
          <div className="p-3">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Preventive Measures
            </label>
            <input
              type="text"
              name="PreventiveMeasure"
              placeholder="Enter Preventive Measures"
              value={formData.PreventiveMeasure}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>
        </div>

        {/* Button Group */}
        <div className="flex justify-end gap-4">
          <button
            type="submit"
            disabled={isCreating}
            className={`px-4 py-2 rounded-md text-white ${
              isCreating ? 'bg-orange-300 cursor-not-allowed' : 'bg-orange-500 hover:bg-orange-600'
            }`}
          >
            {isCreating ? 'Saving...' : `${isEditMode ? 'Update' : 'Save'} Wastage`}
          </button>
          
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}