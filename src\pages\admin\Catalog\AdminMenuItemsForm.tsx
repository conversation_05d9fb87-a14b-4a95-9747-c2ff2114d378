import React, { useState, useEffect } from "react";
import { ChevronLeft, X, Image } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useGetCategoriesQuery } from "../../../store/api/CategoryApi";
import { useGetParentCategoriesQuery } from "../../../store/api/parentCategoryApi";
import { useUpdateProductMutation } from "../../../store/api/menuitemApi";
import {
  usePostProductMutation,
  useGetProductsQuery,
} from "../../../store/api/menuitemApi";
import { useParams } from "react-router-dom";
import { useGetUnitOfMeasurementsQuery } from "../../../store/api/unitOfMeasurementApi";
import { useGetIngredientsQuery } from "../../../store/api/ingredientsApi";
import Swal from "sweetalert2";

const compressImage = (
  file: any,
  maxWidth = 800,
  maxHeight = 800,
  quality = 0.7
) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      if (!event.target?.result) return;
      const img = new window.Image();
      img.src = event.target.result as string;

      img.onload = () => {
        // Create canvas for resizing
        const canvas = document.createElement("canvas");
        let width = img.width;
        let height = img.height;

        // Calculate new dimensions
        if (width > height) {
          if (width > maxWidth) {
            height = Math.round((height * maxWidth) / width);
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = Math.round((width * maxHeight) / height);
            height = maxHeight;
          }
        }

        // Resize image
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext("2d");
        if (!ctx) return;
        ctx.drawImage(img, 0, 0, width, height);

        // Convert to blob
        canvas.toBlob(
          (blob) => {
            if (!blob) return;
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          },
          file.type,
          quality
        );
      };
    };
  });
};

interface IngredientQuantity {
  id: string;
  name: string;
  quantity: string;
}

const CreateMenuItem: React.FC = () => {
  // Form state
  const [menuItemName, setMenuItemName] = useState<string>("");
  const [barcode, setBarcode] = useState<string>("");
  const [category, setCategory] = useState<string>("");
  const [categoryId, setCategoryId] = useState<string>("");
  const [parentCategory, setParentCategory] = useState<string>("");
  const [parentCategoryId, setParentCategoryId] = useState<string>("");
  const [retailPrice, setRetailPrice] = useState<string>("");
  const [price, setPrice] = useState<string>("");
  const [totalQuantity, setTotalQuantity] = useState<string>("");
  const [selectedUnit, setSelectedUnit] = useState<string>("");
  const [selectedUnitId, setSelectedUnitId] = useState<string>("");
  const [selectedIngredients, setSelectedIngredients] = useState<
    IngredientQuantity[]
  >([]);
  const [hasPicture, setHasPicture] = useState<boolean>(false);
  const [isActive, setIsActive] = useState<boolean>(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isCompressing, setIsCompressing] = useState<boolean>(false);

  // Dropdown states
  const [isCategoryOpen, setIsCategoryOpen] = useState<boolean>(false);
  const [isParentCategoryOpen, setIsParentCategoryOpen] =
    useState<boolean>(false);
  const [isUnitOpen, setIsUnitOpen] = useState<boolean>(false);
  const [isIngredientsOpen, setIsIngredientsOpen] = useState<boolean>(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadStatus, setUploadStatus] = useState<string>("");

  const userId = localStorage.getItem("userId") || "";

  const { data: categories, isLoading: categoriesLoading } =
    useGetCategoriesQuery(userId);
  const { data: parentCategories, isLoading: parentCategoriesLoading } =
    useGetParentCategoriesQuery(userId);
  const {
    data: unitsData,
    isLoading: unitsLoading,
    error: unitsError,
  } = useGetUnitOfMeasurementsQuery(userId);

  const { data: ingredientsData } = useGetIngredientsQuery(userId);
  const [postProduct, { isLoading: isCreating}] = usePostProductMutation();
  const [updateProduct, { isLoading: isUpdating }] = useUpdateProductMutation();

  const { id } = useParams<{ id: string }>();
  const { data: products = [] } = useGetProductsQuery(userId);
  const product = products.find((product) => product.id === id);




   useEffect(() => {
    if (product) {
      setMenuItemName(product.name);
      setBarcode(product.barCode);
      setPrice(product.price.toString());
      setRetailPrice(product.retailPrice.toString());
      setTotalQuantity(product.totalQuantity.toString());
      setIsActive(product.active);
      setHasPicture(product.hasPicture);
      
      if (product.categoryId?.length > 0) {
        setCategory(product.categoryId[0].name);
        setCategoryId(product.categoryId[0]._id);
      }
      
   if (product.categoryParents && product.categoryParents.length > 0) {
  setParentCategory(product.categoryParents[0].name);
  setParentCategoryId(product.categoryParents[0]._id);
}
      
      if (product.unit) {
        setSelectedUnit(product.unit.name);
        setSelectedUnitId(product.unit._id);
      }
   if (product.ingredient?.length > 0) {
  const ingredients = product.ingredient.map(item => ({
    id: item.ingredientId?._id || '',
    name: item.ingredientId?.IngredientName || '',
    quantity: item.quantity?.toString() || '0'
  }));
  setSelectedIngredients(ingredients);
}
     if (product.pictureUrl) {
      setPreviewUrl(product.pictureUrl);
      setHasPicture(true);
    }
    }
  }, [product]);



  console.log(" selected product", product);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
  if (e.target.files && e.target.files[0]) {
    // Clear any existing preview URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }

    const file = e.target.files[0];
    setUploadStatus(`Original size: ${(file.size / (1024 * 1024)).toFixed(2)} MB`);

    if (file.size > 1 * 1024 * 1024) { 
      setIsCompressing(true);
      try {
        const compressedFile = (await compressImage(file)) as File;
        setUploadStatus(
          `Compressed: ${(compressedFile.size / (1024 * 1024)).toFixed(2)} MB`
        );
        setSelectedFile(compressedFile);
        setPreviewUrl(URL.createObjectURL(compressedFile));
      } catch (error) {
        console.error("Error compressing image:", error);
        Swal.fire("Compression Failed", "Error compressing image", "error");
      } finally {
        setIsCompressing(false);
      }
    } else {
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
    }
  }
};

  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const handleIngredientToggle = (ingredient: any) => {
    setSelectedIngredients((prev) => {
      // Check if ingredient is already selected
      const exists = prev.some((item) => item.id === ingredient.id);

      if (exists) {
        return prev.filter((item) => item.id !== ingredient.id);
      } else {
        return [
          ...prev,
          {
            id: ingredient.id,
            name: ingredient.IngredientName,
            quantity: "0",
          },
        ];
      }
    });
  };

  const handleIngredientQuantityChange = (id: string, quantity: string) => {
    setSelectedIngredients((prev) =>
      prev.map((item) => (item.id === id ? { ...item, quantity } : item))
    );
  };

  const removeIngredient = (id: string) => {
    setSelectedIngredients((prev) => prev.filter((item) => item.id !== id));
  };

  const navigate = useNavigate();

  const handleCancel = () => {
    navigate("/admin/catalog/menu-items");
  };

 const handleSubmit = async () => {
  if (!menuItemName || !price) {
    Swal.fire({
      icon: "error",
      title: "Validation Error",
      text: "Please fill in all required fields",
      confirmButtonColor: "#3085d6",
    });
    return;
  }
  try {
    const formData = new FormData();
    formData.append("name", menuItemName);
    formData.append("barCode", barcode);
    formData.append("price", price);
    formData.append("retailPrice", retailPrice);
    formData.append("totalQuantity", totalQuantity);
    formData.append("hasPicture", String(hasPicture));
    formData.append("active", isActive ? "true" : "false");

    if (categoryId) {
      formData.append("categoryId", categoryId);
    }

    if (parentCategoryId) {
      formData.append("categoryParents", parentCategoryId);
    }

    if (selectedUnitId) {
      formData.append("unit", selectedUnitId);
    }

    if (selectedIngredients.length > 0) {
      const ingredientData = selectedIngredients.map((item) => ({
        ingredientId: item.id,
        quantity: item.quantity,
      }));
      formData.append("ingredient", JSON.stringify(ingredientData));
    }
if (selectedFile && selectedFile instanceof File && selectedFile.size > 0) {
  console.log("Appending compressed file:", selectedFile.name);
  formData.append("Product_pic", selectedFile);
} else if (product?.pictureUrl && typeof product.pictureUrl === 'string' && product.pictureUrl !== "") {
  console.log("Appending existing image URL:", product.pictureUrl);
  formData.append("Product_pic", product.pictureUrl);
}


    if (id && product) {
      await await updateProduct({ id, formData }).unwrap(); 
      Swal.fire({
        icon: "success",
        title: "Success!",
        text: "Menu item updated successfully!",
        confirmButtonColor: "#6366f1",
      });
    }
     else {
      // Create new product
      await postProduct(formData).unwrap();
      Swal.fire({
        icon: "success",
        title: "Success!",
        text: "Menu item created successfully!",
        confirmButtonColor: "#6366f1",
      });
    }

    navigate("/admin/catalog/menu-items");
  } catch (error: any) {
    const errorMessage = error.data?.message || error.message || "An unknown error occurred";
    Swal.fire({
      icon: "error",
      title: id ? "Update Failed" : "Creation Failed",
      text: errorMessage,
      confirmButtonColor: "#3085d6",
    });
  }
};
  if (categoriesLoading || parentCategoriesLoading || unitsLoading) {
    return  <div className="flex justify-center items-start bg-white h-screen pt-[35vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading ...</p>
        </div>
      </div>
  }

  if (unitsError) {
    return <div>Error loading units: {JSON.stringify(unitsError)}</div>;
  }

  return (
    <div className="w-full p-4 bg-gray-50">
      {/* Header */}
    <div className="flex items-center mb-4 p-2 border border-gray-200 rounded-2xl bg-white">
  <ChevronLeft className="w-5 h-5 mr-2" onClick={handleCancel} />
  <h1 className="text-xl font-medium">
    {id ? "Edit Menu Item" : "Create Menu Item"}
  </h1>
</div>
      <div className="bg-white rounded-xl shadow-sm">
        <div className="p-4 bg-orange-50 rounded-t-xl text-orange-800 font-medium">
          Menu Items Details
        </div>

        <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm mb-1">Menu Item Name *</label>
            <input
              type="text"
              className="w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              placeholder="Enter Menu Item Name"
              value={menuItemName}
              onChange={(e) => setMenuItemName(e.target.value)}
              required
            />
          </div>

          {/* Barcode */}
          <div>
            <label className="block text-sm mb-1">Barcode</label>
            <input
              type="text"
              className="w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              placeholder="Enter Barcode"
              value={barcode}
              onChange={(e) => setBarcode(e.target.value)}
            />
          </div>

          {/* Category Name */}
          <div>
            <label className="block text-sm mb-1">Category Name</label>
            <div className="relative">
              <button
                type="button"
                className="w-full p-2 border border-gray-200 rounded-md bg-white flex justify-between items-center"
                onClick={() => setIsCategoryOpen(!isCategoryOpen)}
              >
                <span className="text-gray-500">
                  {category || "Select Category"}
                </span>
                <svg
                  className="w-4 h-4 ml-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {isCategoryOpen && (
                <div className="absolute left-0 right-0 mt-1 bg-white border border-orange-500 rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
                  <div className="p-2 border-b border-b-gray-200 flex justify-between items-center">
                    <span className="text-sm font-medium">Select Category</span>
                    <X
                      className="w-4 h-4 cursor-pointer text-gray-500"
                      onClick={() => setIsCategoryOpen(false)}
                    />
                  </div>
                  {categories?.map((cat: any) => (
                    <div
                      key={cat._id}
                      className="p-2 hover:bg-orange-50 cursor-pointer border-l-4 border-transparent hover:border-orange-500"
                      onClick={() => {
                        setCategoryId(cat.id);
                        setCategory(cat.name);
                        setIsCategoryOpen(false);
                      }}
                    >
                      <span>{cat.name}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Parent Category Name */}
          <div>
            <label className="block text-sm mb-1">Parent Category Name</label>
            <div className="relative">
              <button
                type="button"
                className="w-full p-2 border border-gray-200 rounded-md bg-white flex justify-between items-center"
                onClick={() => setIsParentCategoryOpen(!isParentCategoryOpen)}
              >
                <span className="text-gray-500">
                  {parentCategory || "Select Parent Category"}
                </span>
                <svg
                  className="w-4 h-4 ml-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {isParentCategoryOpen && (
                <div className="absolute left-0 right-0 mt-1 bg-white border border-orange-500 rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
                  <div className="p-2 border-b border-b-gray-200 flex justify-between items-center">
                    <span className="text-sm font-medium">
                      Select Parent Category
                    </span>
                    <X
                      className="w-4 h-4 cursor-pointer text-gray-500"
                      onClick={() => setIsParentCategoryOpen(false)}
                    />
                  </div>
                  {parentCategories?.map((cat: any) => (
                    <div
                      key={cat._id}
                      className="p-2 hover:bg-orange-50 cursor-pointer border-l-4 border-transparent hover:border-orange-500"
                      onClick={() => {
                        setParentCategoryId(cat.id);
                        setParentCategory(cat.name);
                        setIsParentCategoryOpen(false);
                      }}
                    >
                      <span>{cat.name}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Retail Price */}
          <div>
            <label className="block text-sm mb-1">Retail Price</label>
            <input
              type="number"
              className="w-full p-2 border  border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              placeholder="Enter Retail Price"
              value={retailPrice}
              onChange={(e) => setRetailPrice(e.target.value)}
            />
          </div>

          {/* Price */}
          <div>
            <label className="block text-sm mb-1">Price *</label>
            <input
              type="number"
              className="w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              placeholder="Enter Price"
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              required
            />
          </div>

          {/* Total Quantity */}
          <div>
            <label className="block text-sm mb-1">Total Quantity</label>
            <input
              type="number"
              className="w-full p-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              placeholder="Enter Total Quantity"
              value={totalQuantity}
              onChange={(e) => setTotalQuantity(e.target.value)}
            />
          </div>

          {/* Select Unit */}
          <div>
            <label className="block text-sm mb-1">Select Unit</label>
            <div className="relative">
              <button
                type="button"
                className="w-full p-2 border border-gray-200 rounded-md bg-white flex justify-between items-center"
                onClick={() => setIsUnitOpen(!isUnitOpen)}
              >
                <span className="text-gray-500">
                  {selectedUnit || "Select Unit"}
                </span>
                <svg
                  className="w-4 h-4 ml-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {isUnitOpen && unitsData && (
                <div className="absolute left-0 right-0 mt-1 bg-white border border-orange-500 rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
                  <div className="p-2 border-b border-b-gray-200 flex justify-between items-center">
                    <span className="text-sm font-medium">Select Unit</span>
                    <X
                      className="w-4 h-4 cursor-pointer text-gray-500"
                      onClick={() => setIsUnitOpen(false)}
                    />
                  </div>
                  {unitsData.map((unit: any) => (
                    <div
                      key={unit.id}
                      className="p-2 hover:bg-orange-50 cursor-pointer border-l-4 border-transparent hover:border-orange-500"
                      onClick={() => {
                        setSelectedUnitId(unit.id);
                        setSelectedUnit(unit.name);
                        setIsUnitOpen(false);
                      }}
                    >
                      <span>{unit.name}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Select Ingredients */}
          <div>
            <label className="block text-sm mb-1">Select Ingredients *</label>
            <div className="relative">
              <button
                type="button"
                className="w-full p-2 border border-gray-200 rounded-md bg-white flex justify-between items-center"
                onClick={() => setIsIngredientsOpen(!isIngredientsOpen)}
              >
                <span className="text-gray-500">
                  {selectedIngredients.length > 0
                    ? `${selectedIngredients.length} ingredients selected`
                    : "Select Ingredients"}
                </span>
                <svg
                  className="w-4 h-4 ml-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {isIngredientsOpen && ingredientsData && (
                <div className="absolute left-0 right-0 mt-1 bg-white border border-orange-500 rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
                  <div className="p-2 border-b border-b-gray-200 flex justify-between items-center">
                    <span className="text-sm font-medium">
                      Select Ingredients
                    </span>
                    <X
                      className="w-4 h-4 cursor-pointer text-gray-500"
                      onClick={() => setIsIngredientsOpen(false)}
                    />
                  </div>
                  {ingredientsData.map((ingredient: any) => (
                    <div
                      key={ingredient.id}
                      className="p-2 hover:bg-orange-50 flex items-center"
                    >
                      <input
                        type="checkbox"
                        id={`ingredient-${ingredient.id}`}
                        className="mr-2"
                        checked={selectedIngredients.some(
                          (item) => item.id === ingredient.id
                        )}
                        onChange={() => handleIngredientToggle(ingredient)}
                      />
                      <label
                        htmlFor={`ingredient-${ingredient.id}`}
                        className="cursor-pointer flex-grow"
                      >
                        {ingredient.IngredientName}
                      </label>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

       
        {selectedIngredients.length > 0 && (
          <div className="p-4 border-t border-gray-200">
            <h3 className="text-sm font-medium mb-3">Selected Ingredients</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {selectedIngredients.map((ingredient) => (
                <div
                  key={ingredient.id}
                  className="flex items-center space-x-2"
                >
                  <div className="flex-grow">
                    <label className="block text-sm mb-1">
                      Quantity for {ingredient.name}
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="number"
                        className="flex-grow p-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                        placeholder={`Enter quantity for ${ingredient.name}`}
                        value={ingredient.quantity}
                        onChange={(e) =>
                          handleIngredientQuantityChange(
                            ingredient.id,
                            e.target.value
                          )
                        }
                      />
                      <button
                        type="button"
                        className="p-2 bg-orange-500 text-white rounded-md hover:bg-orange-600"
                        onClick={() => removeIngredient(ingredient.id)}
                      >
                        <X size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Permissions Section */}
        <div className="border border-gray-200 rounded-2xl mt-4">
          <div className="p-4 bg-orange-50 rounded-t-2xl text-orange-800 font-medium">
            Permissions
          </div>

          <div className="space-y-4 p-4">
            <div className="flex items-center">
              <label className="flex items-center cursor-pointer">
                <div className="relative">
                  <input
                    type="checkbox"
                    className="sr-only"
                    checked={hasPicture}
                    onChange={() => setHasPicture(!hasPicture)}
                  />
                  <div
                    className={`block w-10 h-6 rounded-full ${
                      hasPicture ? "bg-orange-500" : "bg-gray-300"
                    }`}
                  ></div>
                  <div
                    className={`dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition transform ${
                      hasPicture ? "translate-x-4" : ""
                    }`}
                  ></div>
                </div>
                <div className="ml-3 text-sm">Has Picture</div>
              </label>
            </div>

            <div className="flex items-center">
              <label className="flex items-center cursor-pointer">
                <div className="relative">
                  <input
                    type="checkbox"
                    className="sr-only"
                    checked={isActive}
                    onChange={(e) => setIsActive(e.target.checked)}
                  />
                  <div
                    className={`block w-10 h-6 rounded-full ${
                      isActive ? "bg-orange-500" : "bg-gray-300"
                    }`}
                  ></div>
                  <div
                    className={`dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition transform ${
                      isActive ? "translate-x-4" : ""
                    }`}
                  ></div>
                </div>
                <div className="ml-3 text-sm">Active</div>
              </label>
            </div>
          </div>
        </div>

        {/* Menu Items Image Section */}
        <div className="border border-gray-200 rounded-2xl mt-4">
          <div className="p-4 bg-orange-50 rounded-t-2xl text-orange-800 font-medium">
            Menu Items Image
          </div>

          <div className="flex items-start space-x-4 p-3">
            <div className="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center overflow-hidden">
  {previewUrl ? (
    <img
      src={previewUrl}
      alt="Preview"
      className="w-full h-full object-cover"
    />
  ) : hasPicture && product?.pictureUrl ? (
    <img
      src={product.pictureUrl}
      alt="Current Product"
      className="w-full h-full object-cover"
    />
  ) : (
    <Image size={24} className="text-orange-500" />
  )}
</div>

            <div>
              <div className="flex items-center">
                <label className="cursor-pointer">
                  <span className="text-sm font-medium text-blue-500">
                    Upload
                  </span>
                  <input
                    type="file"
                    className="hidden"
                    accept=".svg,.png,.jpg,.jpeg,.gif"
                    onChange={handleFileUpload}
                    disabled={isCompressing}
                  />
                </label>
                <span className="ml-2 text-sm text-gray-500">
                  Category Image
                </span>
              </div>
              <p className="mt-1 text-xs text-gray-500">
                SVG, PNG, JPG, or GIF (recommended: max. 800 x 800px, under 1MB)
              </p>
              {isCompressing && (
                <p className="mt-1 text-xs text-orange-500">
                  Compressing image...
                </p>
              )}
              {uploadStatus && (
                <p className="mt-1 text-xs text-gray-700">{uploadStatus}</p>
              )}
              {selectedFile && (
                <p className="mt-1 text-xs text-gray-700">
                  Selected: {selectedFile.name}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Actions Buttons */}
        <div className="p-4 flex justify-end space-x-4">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 border border-gray-300 rounded-md bg-white text-gray-700"
            disabled={isCreating}
          >
            Cancel
          </button>
         <button
  type="button"
  onClick={handleSubmit}
  className="px-6 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors disabled:opacity-50"
  disabled={isCreating || isUpdating}
>
  {isCreating ? "Creating..." : isUpdating ? "Updating..." : id ? "Update Menu Item" : "Add Menu Item"}
</button>
        </div>
      </div>
    </div>
  );
};

export default CreateMenuItem;
