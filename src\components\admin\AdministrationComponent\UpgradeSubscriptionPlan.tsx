import { useState, useEffect, type JSXElementConstructor, type Key,
  type ReactElement, type ReactNode, type ReactPortal } from "react";
import { useGetAppProductsQuery } from "../../../store/api/checkoutApi";
import { toast } from 'react-toastify';

export default function UpgradeSubscriptionPlan() {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  const {
    data: products,
    isLoading,
    isError,
    error
  } = useGetAppProductsQuery();


  useEffect(() => {
    if (products && products.length > 0) {
      const activeProduct = products.find(product => product.metadata?.status === "active");
      setSelectedPlan(activeProduct?.id || products[0].id);
    }
  }, [products]);

  useEffect(() => {
    if (isError) {
      const errorMessage = 'data' in error ? (error.data as { message?: string })?.message : ('error' in error ? error.error : 'Unknown error');
      toast.error(`Failed to fetch subscription plans: ${errorMessage || "Unknown error"}`);
    }
  }, [isError, error]);

  const handleUpgrade = () => {
    if (!selectedPlan) {
      toast.warning("Please select a plan first");
      return;
    }

    const selectedProduct = products?.find(product => product.id === selectedPlan);
    toast.info(`Upgrading to ${selectedProduct?.name} plan. This will redirect to payment.`);

  };

  const handleCancel = () => {
    toast.info("Upgrade cancelled");
  };

  if (isLoading) {
    return (
      <div className="max-w-5xl mx-auto p-4 text-center">
        <h1 className="text-2xl font-bold text-center mb-6">Loading Subscription Plans...</h1>
        <div className="animate-pulse">
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            {[1, 2, 3].map((item) => (
              <div key={item} className="flex-1 border rounded-lg p-6">
                <div className="h-6 bg-gray-200 rounded mb-4 w-1/2 mx-auto"></div>
                <div className="h-8 bg-gray-200 rounded mb-6 w-1/3 mx-auto"></div>
                {[1, 2, 3, 4, 5].map((line) => (
                  <div key={line} className="h-4 bg-gray-200 rounded mb-2 w-3/4 mx-auto"></div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="max-w-5xl mx-auto p-4 text-center">
        <div className="bg-red-100 p-4 rounded-lg mb-4">
          <p className="text-red-700">Failed to load subscription plans</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 bg-red-500 hover:bg-red-600 text-white py-1 px-4 rounded text-sm"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }


  const formatProductsToPlans = () => {
    if (!products) return [];

    return products
      .filter(product => product.active)
      .map(product => {
        const isActive = product.metadata?.status === "active";

        const features = [
          ...(product.features?.map((feature: { name: any }) => feature.name) || []),
        ];

        return {
          id: product.id,
          name: product.name,
          price: product.price?.toFixed(2) || "0.00",
          priceColor: isActive ? "text-orange-500" : "text-gray-700",
          borderColor: isActive ? "border-orange-500" : "border-gray-200",
          features: features,
          highlight: isActive ? "Currently Active" : null
        };
      });
  };

  const plans = products ? formatProductsToPlans() : [];

  return (
    <div className="max-w-5xl mx-auto p-4">
      <h1 className="text-2xl font-bold text-center mb-6">Upgrade Your Subscription</h1>

      {plans.length === 0 ? (
        <div className="text-center text-gray-500">No subscription plans available</div>
      ) : (
        <>
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            {plans.map((plan) => (
              <div
                key={plan.id}
                className={`flex-1 border-2 rounded-lg p-6 transition-all cursor-pointer ${selectedPlan === plan.id ? plan.borderColor : "border-gray-200"
                  } ${selectedPlan === plan.id ? "ring-2 ring-orange-300" : ""}`}
                onClick={() => setSelectedPlan(plan.id)}
              >
                <div className="text-center mb-4">
                  <h2 className="text-xl font-bold">{plan.name}</h2>
                  <div className="flex justify-center items-baseline">
                    <span className={`text-2xl font-bold ${plan.priceColor}`}>
                      ${plan.price}
                    </span>
                    <span className="text-gray-600"> / month</span>
                  </div>
                </div>

                <div className="space-y-2">
                  {plan.features.map((feature: string | number | bigint | boolean | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode> | ReactPortal | Promise<string | number | bigint | boolean | ReactPortal | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode> | null | undefined> | null | undefined, index: Key | null | undefined) => (
                    <div key={index} className="text-sm text-center flex items-center justify-center">
                      <svg className="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                      </svg>
                      {feature}
                    </div>
                  ))}

                  {plan.highlight && (
                    <div className="text-center text-orange-500 font-medium mt-4">
                      {plan.highlight}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-4">
            <button
              className="bg-orange-500 hover:bg-orange-600 text-white font-medium py-2 px-6 rounded disabled:bg-gray-300 disabled:cursor-not-allowed"
              onClick={handleUpgrade}
              disabled={!selectedPlan}
            >
              Upgrade Subscription
            </button>
            <button
              className="border border-gray-300 text-orange-500 font-medium py-2 px-6 rounded hover:bg-gray-50"
              onClick={handleCancel}
            >
              Cancel
            </button>
          </div>
        </>
      )}
    </div>
  );
}